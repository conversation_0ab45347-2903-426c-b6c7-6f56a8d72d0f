apiVersion: apps/v1beta2
kind: Deployment
metadata:
  labels:
    affinity: mpp-anti-affinity
    app: mps-job-analysis
  name: mps-job-analysis
  namespace: mps-traffic
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: mps-job-analysis
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        affinity: mpp-anti-affinity
        app: mps-job-analysis
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: beta.kubernetes.io/instance-type
                operator: In
                values:
                - ecs.c6.xlarge
              - key: mpp.aliyun.com/node-role
                operator: In
                values:
                - mpp-traffic
              - key: mpp.aliyun.com/prouct
                operator: In
                values:
                - mps
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: affinity
                operator: In
                values:
                - mpp-anti-affinity
            topologyKey: kubernetes.io/hostname
      containers:
      - env:
        - name: JAVA_OPTS
          value: '-Dspring.profiles.active=#region#'
        image: >-
          registry-vpc.#region#.aliyuncs.com/aliyun-mps/mps-job-analysis:#version#
        imagePullPolicy: IfNotPresent
        name: app
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /home/<USER>/mps-job-analysis/logs
          name: trace-log
      - env:
        - name: ALIYUN_LOGTAIL_USER_ID
          value: '1426105496228119'
        - name: ALIYUN_LOGTAIL_USER_DEFINED_ID
          value: mpp-#region#-group
        - name: ALIYUN_LOGTAIL_CONFIG
          value: /etc/ilogtail/conf/#region#/ilogtail_config.json
        - name: ALIYUN_LOG_ENV_TAGS
          value: _pod_name_|_pod_ip_|_namespace_|_node_name_|_node_ip_
        - name: _pod_name_
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: _pod_ip_
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: _namespace_
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: _node_name_
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: _node_ip_
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        image: 'registry-vpc.#region#.aliyuncs.com/log-service/logtail:latest'
        imagePullPolicy: Always
        livenessProbe:
          exec:
            command:
            - /etc/init.d/ilogtaild
            - status
          failureThreshold: 3
          initialDelaySeconds: 30
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 1
        name: logtail
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /home/<USER>/job-analysis/logs
          name: trace-log
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: regsecret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: trace-log