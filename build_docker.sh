#!/bin/bash

if [ $# -lt 2 ]; then
  echo "<$0> <region> <version>"
  exit 1
fi

region=$1
version=$2

mvn clean package -DskipTests
docker build -t registry.$region.aliyuncs.com/aliyun-mps/mps-job-analysis:$version .
#docker tag registry.$region.aliyuncs.com/aliyun-mps/mps-job-analysis:$version registry.cn-beijing.aliyuncs.com/aliyun-mps/mps-job-analysis:$version
#docker tag registry.$region.aliyuncs.com/aliyun-mps/mps-job-analysis:$version registry.ap-southeast-1.aliyuncs.com/aliyun-mps/mps-job-analysis:$version
docker push registry.$region.aliyuncs.com/aliyun-mps/mps-job-analysis:$version
#docker push registry.cn-beijing.aliyuncs.com/aliyun-mps/mps-job-analysis:$version
#docker push registry.ap-southeast-1.aliyuncs.com/aliyun-mps/mps-job-analysis:$version