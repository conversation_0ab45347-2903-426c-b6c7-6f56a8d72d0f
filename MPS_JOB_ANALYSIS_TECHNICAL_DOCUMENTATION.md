# MPS Job Analysis System - 技术文档

## 项目概述

MPS Job Analysis System 是阿里云媒体处理服务(Media Processing Service)的作业分析系统，主要负责：

- 视频转码作业的资源预估和调度参数分析
- 作业执行结果的收集和报告
- SLA(服务等级协议)分析
- 系统健康检查和监控

## 系统架构

### 模块结构
```
mps-job-analysis/
├── mps-job-analysis-controller/    # Web控制器层
├── mps-job-analysis-core/         # 核心业务逻辑
├── mps-job-analysis-start/        # 启动模块和配置
├── db/                           # 数据库脚本
└── test/                         # 测试用例
```

### 技术栈
- **框架**: Spring Boot 2.0.1
- **数据库**: MySQL
- **序列化**: FastJSON
- **HTTP客户端**: Apache HttpClient
- **日志**: Logback + SLS(Simple Log Service)
- **构建工具**: Maven
- **容器化**: Docker

## 核心功能模块

### 1. 作业分析服务 (JobAnalysisService)

#### 主要接口
- `analysisByParseParam()` - 基于引擎参数分析
- `analysisByProbeMeta()` - 基于媒体元数据分析(已废弃)
- `estimateJobWithScheduleParams()` - 基于调度参数预估
- `analysisJobSla()` - SLA分析
- `analysisByWorkerBrain()` - 基于WorkerBrain分析
- `reportResult()` - 结果报告
- `parseJobScheduleParams()` - 解析调度参数

#### 分析模式
1. **BY_PARSE_ENGINE_PARAMS** - 解析引擎参数模式
2. **BY_PROBE_META** - 探测元数据模式(已废弃)
3. **BY_SCHEDULE_PARAMS** - 调度参数模式

### 2. Web API接口

#### 2.1 作业分析接口
```
POST /job/analysis
```

**请求参数:**
```json
{
  "engineModel": "mps-transcode-new",
  "engineParams": "{...}",
  "userId": "1207704461481212",
  "jobId": "8db9344b535d4bf69d0483a15e9352ed",
  "sliceProcess": true,
  "analysisMode": "byScheduleParams",
  "scheduleParams": {
    "pipelineId": "2c416551f7564aa48453a4ad9279f534",
    "priority": 6,
    "inputs": [...],
    "configs": [...],
    "speedXRange": ["5X"]
  },
  "tag": "asi-v100",
  "createTime": 1726979034400,
  "maxSliceNum": 10,
  "minSliceDuration": 30,
  "useWorkerBrainResult": true,
  "invokeWorkerBrain": true,
  "trace": {...}
}
```

**响应格式:**
```json
{
  "code": "Success",
  "executeMode": "SINGLE|DAG",
  "expectCostTime": 296,
  "quotaSet": {
    "cpu": 3809,
    "gpu": 238095,
    "disk": 52
  },
  "speedXMessage": {...},
  "graph": {...}  // DAG模式时返回
}
```

#### 2.2 结果报告接口
```
POST /job/reportResult
```

**请求参数:**
```json
{
  "jobId": "xxx",
  "allocQuotaSet": {...},
  "maxQuotaSet": {...},
  "avgQuotaSet": {...},
  "expectCostTime": 1000,
  "realCostTime": 1200,
  "engineModel": "mps-transcode-new",
  "engineParams": "{...}",
  "trace": {...}
}
```

#### 2.3 健康检查接口
```
GET /service/liveness   # 存活性检查
GET /service/readiness  # 就绪性检查
```

### 3. 数据模型

#### 3.1 JobAnalysisParam (作业分析参数)
```java
public class JobAnalysisParam {
    private String engineModel;        // 引擎模型
    private String engineParams;       // 引擎参数
    private String userId;             // 用户ID
    private String jobId;              // 作业ID
    private boolean isSliceProcess;    // 是否切片处理
    private String analysisMode;       // 分析模式
    private ScheduleParams scheduleParams; // 调度参数
    private Integer maxSliceNum;       // 最大切片数
    private Integer minSliceDuration;  // 最小切片时长
    private boolean useWorkerBrainResult; // 是否使用WorkerBrain结果
    private boolean invokeWorkerBrain; // 是否调用WorkerBrain
    private List<String> speedXRange;  // 倍速范围
    private boolean isAutoSpeedX;      // 是否自动倍速
}
```

#### 3.2 JobAnalysisResult (作业分析结果)
```java
public class JobAnalysisResult {
    private boolean isSuccess;         // 是否成功
    private String code;               // 结果码
    private String message;            // 消息
    private EnumExecuteMode executeMode; // 执行模式: SINGLE/DAG
    private Long expectCostTime;       // 预期耗时
    private Map<String, Long> quotaSet; // 资源配额
    private Long slaFinishDelay;       // SLA完成延迟
    private Long slaQueuingDelay;      // SLA排队延迟
    private DagJobGraph graph;         // DAG图(DAG模式)
    private Map<String, DagJobGraph> graphMap; // 倍速图集合
    private Map<String, Message> speedXMessage; // 倍速消息
}
```

#### 3.3 ScheduleParams (调度参数)
```java
public class ScheduleParams {
    private String pipelineId;         // 管道ID
    private Map<String, Long> quotaSet; // 配额集合
    private Integer priority;          // 优先级
    private Integer expectCostTime;    // 预期耗时
    private List<Input> inputs;        // 输入列表
    private List<Config> configs;      // 配置列表
    private Integer parallelNum;       // 并行数
    private String slaLevel;           // SLA等级
    private Integer sliceNum;          // 切片数
    private List<String> speedXRange;  // 倍速范围
}
```

### 4. 业务逻辑

#### 4.1 作业类型识别
系统支持多种作业类型：
- **Transcode** - 转码作业
- **Editing** - 编辑作业  
- **Package** - 打包作业
- **ConvertSubtitle** - 字幕转换作业

#### 4.2 资源预估算法
1. **简单作业** - 固定资源配额
2. **元数据作业** - 基于文件大小预估
3. **单帧快照** - 固定CPU资源
4. **转码作业** - 基于视频参数和转码规格预估
5. **切片作业** - 支持并行处理的DAG模式

#### 4.3 执行模式
- **SINGLE模式** - 单任务执行，返回资源配额和预期耗时
- **DAG模式** - 有向无环图执行，支持并行切片处理

#### 4.4 倍速处理
支持多倍速转码：
- 5X, 10X, 20X, 30X倍速
- 自动降级策略
- 倍速能力检测

### 5. 外部依赖

#### 5.1 DMES服务
- 用于DAG任务的资源预估
- 支持切片并行处理

#### 5.2 WorkerBrain服务  
- AI驱动的资源预估
- 支持多种引擎模型

#### 5.3 SLA服务
- 服务等级协议分析
- 时间预估和约束

#### 5.4 媒体元数据服务
- 视频文件信息解析
- 编解码器信息获取

### 6. 配置管理

#### 6.1 环境配置
- **dev** - 开发环境
- **test** - 测试环境  
- **online** - 生产环境
- **dedicatedCloud** - 专有云环境

#### 6.2 关键配置项
```properties
# 服务端口
server.port=8080

# 外部服务URL
app.dmes.serviceUrl=${DMES_SERVICE_URL}
app.worker_brain.mpsTranscodeNewUrl=${WORKER_BRAIN_MPS_TRANSCODE_NEW_URL}
app.sla.serviceUrl=${SLA_SERVICE_URL}

# 数据库配置
spring.datasource.url=jdbc:mysql://${DATA_SOURCE_URL}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}

# HTTP连接池
http-pool.maxTotal=1000
http-pool.connectTimeout=1000
http-pool.socketTimeout=2000
```

### 7. 数据库设计

#### 7.1 mps_engine_quota_config表
```sql
CREATE TABLE `mps_engine_quota_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `name` varchar(64) NOT NULL,
  `configs` varchar(1024) NOT NULL,
  `quota_set` varchar(128) NOT NULL,
  `cost` bigint unsigned DEFAULT NULL,
  `speed` decimal(8,4) DEFAULT NULL,
  `disk_ratio` decimal(8,4) DEFAULT NULL,
  `disk_quota` bigint unsigned DEFAULT NULL,
  `custom_param` varchar(1024) DEFAULT NULL,
  `max_migrate_retry` int unsigned DEFAULT '5',
  `migrate_discard_quota_threshold` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
);
```

### 8. 日志和监控

#### 8.1 日志分类
- **应用日志** - 业务逻辑日志
- **错误日志** - 异常和错误信息
- **依赖服务日志** - 外部服务调用日志
- **追踪日志** - 请求链路追踪
- **结果报告日志** - 作业结果统计
- **作业结果收集日志** - 作业执行数据收集

#### 8.2 SLS集成
- 结构化日志输出
- 实时日志分析
- 告警和监控

### 9. 部署和运维

#### 9.1 Docker化部署
```dockerfile
FROM openjdk:8u181
COPY ./mps-job-analysis-start/target/mps-job-analysis.jar /root/
ENTRYPOINT java `echo $JAVA_OPTS` -jar /root/mps-job-analysis.jar
```

#### 9.2 Kubernetes部署
- 支持多环境配置
- 自动扩缩容
- 健康检查集成

#### 9.3 监控指标
- 请求QPS和延迟
- 错误率统计
- 资源使用情况
- 外部依赖可用性

## 总结

MPS Job Analysis System是一个复杂的媒体处理作业分析系统，具有以下特点：

1. **多模式分析** - 支持多种分析模式和执行策略
2. **智能预估** - 结合AI和规则的资源预估算法
3. **高可扩展** - 模块化设计，易于扩展新功能
4. **生产就绪** - 完善的监控、日志和部署方案
5. **性能优化** - 支持并行处理和倍速优化

该系统为阿里云媒体处理服务提供了强大的作业调度和资源管理能力。
