#!/bin/bash

name='mps-job-analysis'

# check arguments.
if [ $# -lt 2 ]; then
    echo -e "\033[31m usage: <region> , <version> arguments at least \033[0m"
    exit 1
else 
    # get region and version from arguments.
    region=$1
    version=$2
    echo $region
    echo $version

fi

# create temporay deployment config file.
cp deployment-mps-job-analysis.yaml deployment-mps-job-analysis-$region.yaml

# replace region and version symbolic placeholder
if [ $(uname) == "Darwin" ]; then
    echo "xxxx"
    sed -i "" "s/#region#/$region/g" deployment-mps-job-analysis-$region.yaml
    sed -i "" "s/#version#/$version/g" deployment-mps-job-analysis-$region.yaml
    sed -i "" "s/#name#/$name/g" deployment-mps-job-analysis-$region.yaml

elif [ $(expr substr $(uname -s) 1 5) == "Linux" ]; then
    sed -i "s/#region#/$region/g" deployment-mps-job-analysis-$region.yaml
    sed -i "s/#version#/$version/g" deployment-mps-job-analysis-$region.yaml
    sed -i "s/#name#/$name/g" deployment-mps-job-analysis-$region.yaml

fi
