package com.aliyun.mpp.analysis.controller;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.web.bind.annotation.RequestMethod.GET;

/**
 * Created by lihe.lh on 2020/2/18.
 */
@RestController
@Slf4j
public class ServiceController {
    @RequestMapping(value = "/service/liveness", method = GET)
    public String checkLiveness() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", "Success");
        return jsonObject.toJSONString();
    }

    @RequestMapping(value = "/service/readiness", method = GET)
    public String checkReadiness() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", "Success");
        return jsonObject.toJSONString();
    }
}
