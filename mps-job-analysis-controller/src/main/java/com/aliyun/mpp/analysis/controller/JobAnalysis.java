package com.aliyun.mpp.analysis.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.service.JobAnalysisService;
import com.aliyun.mpp.analysis.domain.types.JobAnalysisResult;
import com.aliyun.mpp.analysis.domain.types.ParseScheduleParamsResult;
import com.aliyun.mpp.analysis.domain.types.ScheduleParams;
import com.aliyun.mpp.analysis.domain.types.SlaAnalysisResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * Created by lihe.lh on 2019/12/30.
 */
@Slf4j
@RestController
public class JobAnalysis extends BaseModel {
    @Resource
    private JobAnalysisService jobAnalysisService;

    @RequestMapping(value = "/job/analysis", method = POST)
    public String exec(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        return super.execRequest(request, jsonObject);
    }

    @Override
    protected String execImpl(HttpServletRequest request, JSONObject jsonBody) {
        JobAnalysisParam param = new JobAnalysisParam();
        param.setEngineModel(jsonBody.getString("engineModel"));
        param.setEngineParams(jsonBody.getString("engineParams"));
        param.setUserId(jsonBody.getString("userId"));

        param.setJobId(jsonBody.getString("jobId"));
        param.setSliceProcess(jsonBody.getBooleanValue("sliceProcess"));
        param.setAnalysisMode(jsonBody.getString("analysisMode"));
        param.setTag(jsonBody.getString("tag"));
        param.setCreateTime(jsonBody.getDate("createTime"));
        param.setMaxSliceNum(jsonBody.getInteger("maxSliceNum"));
        param.setMinSliceDuration(jsonBody.getInteger("minSliceDuration"));
        if(jsonBody.getBoolean("isAutoSpeedX") != null){
            param.setAutoSpeedX(jsonBody.getBoolean("isAutoSpeedX"));
        }
        if(jsonBody.getBoolean("useWorkerBrainResult") != null){
            param.setUseWorkerBrainResult(jsonBody.getBooleanValue("useWorkerBrainResult"));
        }
        if(jsonBody.getBoolean("invokeWorkerBrain") != null){
            param.setInvokeWorkerBrain(jsonBody.getBooleanValue("invokeWorkerBrain"));
        }

        try{
            param.setTrace(jsonBody.getJSONObject("trace"));
        }catch (Exception e){
            log.info("fail@JobAnalysis, parse trace failed", e);
        }

        JSONObject scheduleParams = jsonBody.getJSONObject("scheduleParams");
        if(scheduleParams != null){
            param.setPipelineId(scheduleParams.getString("pipelineId"));
        }

        //通过AnalysisParam参数判断走哪种预估流程
        JobAnalysisResult result = null;
        if (param.getAnalysisMode() != null && param.getAnalysisMode().equals(JobAnalysisParam.BY_PROBE_META)) {
            result = jobAnalysisService.analysisByProbeMeta(param);
        } else if(JobAnalysisParam.BY_SCHEDULE_PARAMS.equals(param.getAnalysisMode())){
            param.setScheduleParams(JSONObject.toJavaObject(scheduleParams, ScheduleParams.class));
            param.setScheduleParamsByJson(scheduleParams);
            result = jobAnalysisService.estimateJobWithScheduleParams(param);
            return JSONObject.toJSONString(result);
        }else {
            //全部返回single类型 qinxin
            result = jobAnalysisService.analysisByParseParam(param);
        }

        //以下使用调度参数预估老引擎quota
        try {
            ParseScheduleParamsResult parseScheduleParamsResult = jobAnalysisService.parseJobScheduleParams(result, param);
            if(parseScheduleParamsResult != null) {
                parseScheduleParamsResult.getScheduleParams().setPipelineId(scheduleParams.getString("pipelineId"));
                parseScheduleParamsResult.getScheduleParams().setSlaLevel(scheduleParams.getString("slaLevel"));
                param.setScheduleParams(parseScheduleParamsResult.getScheduleParams());
                param.setScheduleParamsByJson(parseScheduleParamsResult.getScheduleParamsByJson());
                SlaAnalysisResult slaAnalysisResult = jobAnalysisService.analysisJobSla(param);
                JobAnalysisResult resultByScheduleParams = jobAnalysisService.estimateJobWithScheduleParams(param);
                Long diskByScheduleParams = resultByScheduleParams.getQuotaSet() != null ?
                        resultByScheduleParams.getQuotaSet().get("disk") : null;
                Long diskByEngineParams = result.getQuotaSet() != null ?
                        result.getQuotaSet().get("disk") : null;

                if (diskByScheduleParams != null && diskByEngineParams != null && (diskByEngineParams > diskByScheduleParams)) {
                    resultByScheduleParams.getQuotaSet().put("disk", diskByEngineParams);
                }
                if (resultByScheduleParams.getQuotaSet() != null && !resultByScheduleParams.getQuotaSet().containsKey("gpu")
                        && result.getQuotaSet() != null && result.getQuotaSet().containsKey("gpu")) {
                    log.info("fail@JobAnalysis, lost gpu quota, use old,jobId:" + param.getJobId());
                } else {
                    result.setQuotaSet(resultByScheduleParams.getQuotaSet());
                    result.setExpectCostTime(resultByScheduleParams.getExpectCostTime());
                    result.setMaxMigrateRetry(resultByScheduleParams.getMaxMigrateRetry());
                    result.setMigrateDiscardQuotaThreshold(resultByScheduleParams.getMigrateDiscardQuotaThreshold());
                    Long slaTime = slaAnalysisResult.getSlaTime();
                    if(slaAnalysisResult == null || !slaAnalysisResult.isSuccess()){
                        slaTime = resultByScheduleParams.getExpectCostTime() * 1000L;
                    }
                    result.setSlaFinishDelay(slaTime/1000L);
                    result.setSlaQueuingDelay(60L);
                }
            }

        }catch (Exception e){
            log.error("fail@JobAnalysis, by converted schedule params", e);
        }

        return JSONObject.toJSONString(result);
    }

    public static void main(String[] args) {
        List<String> a = new ArrayList<>();
        a.add("5X");
        a.add("10X");
        a.add("20X");
        a.add("30X");
        System.out.println(JSON.toJSONString(a));
        String var = JSON.toJSONString(a);
        List<String> c = JSONObject.parseArray(var,String.class);
        for (String b: c) {
            System.out.println(b);
        }
    }
}
