package com.aliyun.mpp.analysis.controller;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.util.ResultUtil;
import com.aliyun.mpp.analysis.util.log.SLSLogInfo;
import com.aliyun.mpp.analysis.util.log.SLSLogUtil;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Map;

/**
 * Created by lihe.lh on 2020/1/17.
 */
@Slf4j
public abstract class BaseModel {
    public String execRequest(HttpServletRequest request, JSONObject jsonBody){
        if(jsonBody == null){
            jsonBody = new JSONObject();
        }

        String uri = request.getServletPath();


        JSONObject param = new JSONObject();
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()){
            String paramName = parameterNames.nextElement();
            param.put(paramName, request.getParameter(paramName));
        }

        if(jsonBody != null && !jsonBody.isEmpty()){
            for (Map.Entry<String, Object> entry : jsonBody.entrySet()) {
                param.put(entry.getKey(), entry.getValue());
            }
        }

        SLSLogInfo slsLogInfo = new SLSLogInfo();
        slsLogInfo.setUserId(param.getString("userId"));
        slsLogInfo.setFunction(uri);
        slsLogInfo.setTrace(param.getString("trace"));
        slsLogInfo.setEngineModel(param.getString("engineModel"));
        slsLogInfo.setProduct(param.getString("product"));

        slsLogInfo.setParam(param.toJSONString());
        slsLogInfo.setEvent(getEventInName());
        SLSLogUtil.info(slsLogInfo);

        long beginTime = System.currentTimeMillis();
        Throwable throwable = null;
        String result = null;
        try{
            result = execImpl(request, jsonBody);
        }catch (Throwable e) {
            log.info("fail@execRequest", e);
            throwable = e;
            result = ResultUtil.createErrorResult("InternalError", "The operation has failed due to some unknown error, exception or failure.",
                    request.getParameter("requestId"));
            return result;
        } finally {
            slsLogInfo.setEvent(getEventOutName());
            if (throwable != null) {
                slsLogInfo.setCode(throwable.getMessage());
            }
            slsLogInfo.setCostTime(System.currentTimeMillis() - beginTime);
            if (throwable != null) {
                StringBuilder stringBuilder = new StringBuilder();
                if(throwable.getCause() != null && throwable.getCause().getClass() != null){
                    stringBuilder.append("exceptionClass:" + (throwable.getCause() == null ? "" : throwable.getCause().getClass().getSimpleName()));
                }
                stringBuilder.append("exceptionMessage:" + throwable.getMessage());
                slsLogInfo.setMessage(stringBuilder.toString());
                JSONObject resultJson = JSONObject.parseObject(result);
                slsLogInfo.setCode(resultJson.getString("code"));
                SLSLogUtil.error(slsLogInfo);
                SLSLogUtil.warn(slsLogInfo);
            } else {
                try {
                    if(result != null) {
                        JSONObject resultJson = JSONObject.parseObject(result);
                        slsLogInfo.setCode(resultJson.getString("code"));
                        slsLogInfo.setMessage(resultJson.getString("message"));
                        slsLogInfo.setResult(result);
                        slsLogInfo.setJobId(resultJson.getString("jobId"));
                    }
                }catch (Exception e){
                    log.error("info@execRequest", e);
                }

                SLSLogUtil.info(slsLogInfo);
            }
        }
        return result;

    }

    protected abstract String execImpl(HttpServletRequest request, JSONObject jsonBody);

    protected String getEventInName(){
        return "api.in";
    }

    protected String getEventOutName(){
        return "api.out";
    }

}