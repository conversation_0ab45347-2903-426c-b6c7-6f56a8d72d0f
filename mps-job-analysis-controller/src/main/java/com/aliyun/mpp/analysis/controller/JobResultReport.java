package com.aliyun.mpp.analysis.controller;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.param.JobExecutionResultReportParam;
import com.aliyun.mpp.analysis.domain.service.JobAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.Map;

import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * Created by lihe.lh on 2019/12/30.
 */
@Slf4j
@RestController
public class JobResultReport{
    @Resource
    private JobAnalysisService jobAnalysisService;

    @RequestMapping(value = "/job/reportResult", method = POST)
    public String exec(HttpServletRequest request, @RequestBody JSONObject jsonBody) {
        JobExecutionResultReportParam param = new JobExecutionResultReportParam();
        param.setAllocQuotaSet(jsonBody.getObject("allocQuotaSet", Map.class));
        param.setMaxQuotaSet(jsonBody.getObject("maxQuotaSet", Map.class));
        param.setAvgQuotaSet(jsonBody.getObject("avgQuotaSet", Map.class));
        param.setEngineModel(jsonBody.getString("engineModel"));
        param.setEngineParams(jsonBody.getString("engineParams"));
        param.setExpectCostTime(jsonBody.getLong("expectCostTime"));
        param.setRealCostTime(jsonBody.getLong("realCostTime"));
        param.setCreateTime(jsonBody.getDate("createTime"));
        param.setSubmitTime(jsonBody.getDate("submitTime"));
        param.setTrace(jsonBody.getJSONObject("trace"));
        param.setJobId(jsonBody.getString("jobId"));
        param.setPipelineId(jsonBody.getString("pipelineId"));
        param.setTag(jsonBody.getString("tag"));
        param.setRequestId(jsonBody.getString("requestId"));
        param.setProduct(jsonBody.getString("product"));
        param.setResultData(jsonBody.get("resultData"));
        param.setUserId(jsonBody.getString("userId"));
        param.setEnv(jsonBody.getString("env"));
        param.setStation(jsonBody.getString("station"));
        jobAnalysisService.reportResult(param);

        JSONObject result = new JSONObject();
        result.put("code", "Success");
        return result.toJSONString();
    }
}
