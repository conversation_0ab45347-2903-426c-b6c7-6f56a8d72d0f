-- 创建数据库
CREATE DATABASE IF NOT EXISTS mps_job_analysis DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE mps_job_analysis;

-- 创建引擎配额配置表
CREATE TABLE IF NOT EXISTS `mps_engine_quota_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `name` varchar(64) NOT NULL COMMENT '配置名称',
  `configs` varchar(1024) NOT NULL COMMENT '配置参数',
  `quota_set` varchar(128) NOT NULL COMMENT '资源配额集合',
  `cost` bigint unsigned DEFAULT NULL COMMENT '预期耗时',
  `speed` decimal(8,4) DEFAULT NULL COMMENT '处理速度',
  `disk_ratio` decimal(8,4) DEFAULT NULL COMMENT '磁盘比例',
  `disk_quota` bigint unsigned DEFAULT NULL COMMENT '磁盘配额',
  `custom_param` varchar(1024) DEFAULT NULL COMMENT '自定义参数',
  `max_migrate_retry` int unsigned DEFAULT '5' COMMENT '最大迁移重试次数',
  `migrate_discard_quota_threshold` varchar(128) DEFAULT NULL COMMENT '迁移丢弃配额阈值',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='引擎配额配置表';

-- 插入一些示例数据
INSERT INTO `mps_engine_quota_config` (`gmt_create`, `gmt_modified`, `name`, `configs`, `quota_set`, `cost`, `speed`, `disk_ratio`, `disk_quota`, `max_migrate_retry`) VALUES
(NOW(), NOW(), 'mps-transcode-new', '{"engine":"mps-transcode-new","version":"1.0"}', '{"cpu":1000,"gpu":0,"disk":1000}', 300, 1.0000, 1.0000, 1000, 5),
(NOW(), NOW(), 'mps-editing', '{"engine":"mps-editing","version":"1.0"}', '{"cpu":2000,"gpu":1000,"disk":2000}', 600, 0.5000, 2.0000, 2000, 3),
(NOW(), NOW(), 'mps-snapshot', '{"engine":"mps-snapshot","version":"1.0"}', '{"cpu":500,"gpu":0,"disk":500}', 60, 2.0000, 0.5000, 500, 5);

-- 创建索引
CREATE INDEX `idx_gmt_create` ON `mps_engine_quota_config` (`gmt_create`);
CREATE INDEX `idx_gmt_modified` ON `mps_engine_quota_config` (`gmt_modified`);
