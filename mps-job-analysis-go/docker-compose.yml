version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mps-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: mps_job_analysis
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7-alpine
    container_name: mps-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  app:
    build: .
    container_name: mps-job-analysis-go
    ports:
      - "8080:8080"
      - "9000:9000"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./configs:/data/conf
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=password
      - DB_NAME=mps_job_analysis
      - REDIS_HOST=redis
      - REDIS_PORT=6379

volumes:
  mysql_data:
  redis_data:
