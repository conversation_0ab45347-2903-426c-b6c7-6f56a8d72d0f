// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: job/v1/job.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	JobAnalysisService_AnalyzeJob_FullMethodName   = "/api.job.v1.JobAnalysisService/AnalyzeJob"
	JobAnalysisService_ReportResult_FullMethodName = "/api.job.v1.JobAnalysisService/ReportResult"
)

// JobAnalysisServiceClient is the client API for JobAnalysisService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Job Analysis Service
type JobAnalysisServiceClient interface {
	// 作业分析接口
	AnalyzeJob(ctx context.Context, in *AnalyzeJobRequest, opts ...grpc.CallOption) (*AnalyzeJobResponse, error)
	// 结果报告接口
	ReportResult(ctx context.Context, in *ReportResultRequest, opts ...grpc.CallOption) (*ReportResultResponse, error)
}

type jobAnalysisServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewJobAnalysisServiceClient(cc grpc.ClientConnInterface) JobAnalysisServiceClient {
	return &jobAnalysisServiceClient{cc}
}

func (c *jobAnalysisServiceClient) AnalyzeJob(ctx context.Context, in *AnalyzeJobRequest, opts ...grpc.CallOption) (*AnalyzeJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AnalyzeJobResponse)
	err := c.cc.Invoke(ctx, JobAnalysisService_AnalyzeJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobAnalysisServiceClient) ReportResult(ctx context.Context, in *ReportResultRequest, opts ...grpc.CallOption) (*ReportResultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReportResultResponse)
	err := c.cc.Invoke(ctx, JobAnalysisService_ReportResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JobAnalysisServiceServer is the server API for JobAnalysisService service.
// All implementations must embed UnimplementedJobAnalysisServiceServer
// for forward compatibility.
//
// Job Analysis Service
type JobAnalysisServiceServer interface {
	// 作业分析接口
	AnalyzeJob(context.Context, *AnalyzeJobRequest) (*AnalyzeJobResponse, error)
	// 结果报告接口
	ReportResult(context.Context, *ReportResultRequest) (*ReportResultResponse, error)
	mustEmbedUnimplementedJobAnalysisServiceServer()
}

// UnimplementedJobAnalysisServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedJobAnalysisServiceServer struct{}

func (UnimplementedJobAnalysisServiceServer) AnalyzeJob(context.Context, *AnalyzeJobRequest) (*AnalyzeJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AnalyzeJob not implemented")
}
func (UnimplementedJobAnalysisServiceServer) ReportResult(context.Context, *ReportResultRequest) (*ReportResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportResult not implemented")
}
func (UnimplementedJobAnalysisServiceServer) mustEmbedUnimplementedJobAnalysisServiceServer() {}
func (UnimplementedJobAnalysisServiceServer) testEmbeddedByValue()                            {}

// UnsafeJobAnalysisServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JobAnalysisServiceServer will
// result in compilation errors.
type UnsafeJobAnalysisServiceServer interface {
	mustEmbedUnimplementedJobAnalysisServiceServer()
}

func RegisterJobAnalysisServiceServer(s grpc.ServiceRegistrar, srv JobAnalysisServiceServer) {
	// If the following call pancis, it indicates UnimplementedJobAnalysisServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&JobAnalysisService_ServiceDesc, srv)
}

func _JobAnalysisService_AnalyzeJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnalyzeJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobAnalysisServiceServer).AnalyzeJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobAnalysisService_AnalyzeJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobAnalysisServiceServer).AnalyzeJob(ctx, req.(*AnalyzeJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobAnalysisService_ReportResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobAnalysisServiceServer).ReportResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobAnalysisService_ReportResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobAnalysisServiceServer).ReportResult(ctx, req.(*ReportResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// JobAnalysisService_ServiceDesc is the grpc.ServiceDesc for JobAnalysisService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var JobAnalysisService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.job.v1.JobAnalysisService",
	HandlerType: (*JobAnalysisServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AnalyzeJob",
			Handler:    _JobAnalysisService_AnalyzeJob_Handler,
		},
		{
			MethodName: "ReportResult",
			Handler:    _JobAnalysisService_ReportResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "job/v1/job.proto",
}

const (
	ServiceHealthService_CheckLiveness_FullMethodName  = "/api.job.v1.ServiceHealthService/CheckLiveness"
	ServiceHealthService_CheckReadiness_FullMethodName = "/api.job.v1.ServiceHealthService/CheckReadiness"
)

// ServiceHealthServiceClient is the client API for ServiceHealthService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Service Health Service
type ServiceHealthServiceClient interface {
	// 存活性检查
	CheckLiveness(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error)
	// 就绪性检查
	CheckReadiness(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error)
}

type serviceHealthServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceHealthServiceClient(cc grpc.ClientConnInterface) ServiceHealthServiceClient {
	return &serviceHealthServiceClient{cc}
}

func (c *serviceHealthServiceClient) CheckLiveness(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResponse)
	err := c.cc.Invoke(ctx, ServiceHealthService_CheckLiveness_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceHealthServiceClient) CheckReadiness(ctx context.Context, in *HealthCheckRequest, opts ...grpc.CallOption) (*HealthCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResponse)
	err := c.cc.Invoke(ctx, ServiceHealthService_CheckReadiness_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceHealthServiceServer is the server API for ServiceHealthService service.
// All implementations must embed UnimplementedServiceHealthServiceServer
// for forward compatibility.
//
// Service Health Service
type ServiceHealthServiceServer interface {
	// 存活性检查
	CheckLiveness(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error)
	// 就绪性检查
	CheckReadiness(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error)
	mustEmbedUnimplementedServiceHealthServiceServer()
}

// UnimplementedServiceHealthServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedServiceHealthServiceServer struct{}

func (UnimplementedServiceHealthServiceServer) CheckLiveness(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLiveness not implemented")
}
func (UnimplementedServiceHealthServiceServer) CheckReadiness(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckReadiness not implemented")
}
func (UnimplementedServiceHealthServiceServer) mustEmbedUnimplementedServiceHealthServiceServer() {}
func (UnimplementedServiceHealthServiceServer) testEmbeddedByValue()                              {}

// UnsafeServiceHealthServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceHealthServiceServer will
// result in compilation errors.
type UnsafeServiceHealthServiceServer interface {
	mustEmbedUnimplementedServiceHealthServiceServer()
}

func RegisterServiceHealthServiceServer(s grpc.ServiceRegistrar, srv ServiceHealthServiceServer) {
	// If the following call pancis, it indicates UnimplementedServiceHealthServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ServiceHealthService_ServiceDesc, srv)
}

func _ServiceHealthService_CheckLiveness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceHealthServiceServer).CheckLiveness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceHealthService_CheckLiveness_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceHealthServiceServer).CheckLiveness(ctx, req.(*HealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServiceHealthService_CheckReadiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceHealthServiceServer).CheckReadiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServiceHealthService_CheckReadiness_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceHealthServiceServer).CheckReadiness(ctx, req.(*HealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ServiceHealthService_ServiceDesc is the grpc.ServiceDesc for ServiceHealthService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServiceHealthService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.job.v1.ServiceHealthService",
	HandlerType: (*ServiceHealthServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckLiveness",
			Handler:    _ServiceHealthService_CheckLiveness_Handler,
		},
		{
			MethodName: "CheckReadiness",
			Handler:    _ServiceHealthService_CheckReadiness_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "job/v1/job.proto",
}
