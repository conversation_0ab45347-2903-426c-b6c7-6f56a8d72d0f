// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.0
// - protoc             v5.29.3
// source: job/v1/job.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationJobAnalysisServiceAnalyzeJob = "/api.job.v1.JobAnalysisService/AnalyzeJob"
const OperationJobAnalysisServiceReportResult = "/api.job.v1.JobAnalysisService/ReportResult"

type JobAnalysisServiceHTTPServer interface {
	// AnalyzeJob 作业分析接口
	AnalyzeJob(context.Context, *AnalyzeJobRequest) (*AnalyzeJobResponse, error)
	// ReportResult 结果报告接口
	ReportResult(context.Context, *ReportResultRequest) (*ReportResultResponse, error)
}

func RegisterJobAnalysisServiceHTTPServer(s *http.Server, srv JobAnalysisServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/job/analysis", _JobAnalysisService_AnalyzeJob0_HTTP_Handler(srv))
	r.POST("/job/reportResult", _JobAnalysisService_ReportResult0_HTTP_Handler(srv))
}

func _JobAnalysisService_AnalyzeJob0_HTTP_Handler(srv JobAnalysisServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AnalyzeJobRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobAnalysisServiceAnalyzeJob)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AnalyzeJob(ctx, req.(*AnalyzeJobRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AnalyzeJobResponse)
		return ctx.Result(200, reply)
	}
}

func _JobAnalysisService_ReportResult0_HTTP_Handler(srv JobAnalysisServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReportResultRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJobAnalysisServiceReportResult)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReportResult(ctx, req.(*ReportResultRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ReportResultResponse)
		return ctx.Result(200, reply)
	}
}

type JobAnalysisServiceHTTPClient interface {
	AnalyzeJob(ctx context.Context, req *AnalyzeJobRequest, opts ...http.CallOption) (rsp *AnalyzeJobResponse, err error)
	ReportResult(ctx context.Context, req *ReportResultRequest, opts ...http.CallOption) (rsp *ReportResultResponse, err error)
}

type JobAnalysisServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewJobAnalysisServiceHTTPClient(client *http.Client) JobAnalysisServiceHTTPClient {
	return &JobAnalysisServiceHTTPClientImpl{client}
}

func (c *JobAnalysisServiceHTTPClientImpl) AnalyzeJob(ctx context.Context, in *AnalyzeJobRequest, opts ...http.CallOption) (*AnalyzeJobResponse, error) {
	var out AnalyzeJobResponse
	pattern := "/job/analysis"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobAnalysisServiceAnalyzeJob))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *JobAnalysisServiceHTTPClientImpl) ReportResult(ctx context.Context, in *ReportResultRequest, opts ...http.CallOption) (*ReportResultResponse, error) {
	var out ReportResultResponse
	pattern := "/job/reportResult"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJobAnalysisServiceReportResult))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

const OperationServiceHealthServiceCheckLiveness = "/api.job.v1.ServiceHealthService/CheckLiveness"
const OperationServiceHealthServiceCheckReadiness = "/api.job.v1.ServiceHealthService/CheckReadiness"

type ServiceHealthServiceHTTPServer interface {
	// CheckLiveness 存活性检查
	CheckLiveness(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error)
	// CheckReadiness 就绪性检查
	CheckReadiness(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error)
}

func RegisterServiceHealthServiceHTTPServer(s *http.Server, srv ServiceHealthServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/service/liveness", _ServiceHealthService_CheckLiveness0_HTTP_Handler(srv))
	r.GET("/service/readiness", _ServiceHealthService_CheckReadiness0_HTTP_Handler(srv))
}

func _ServiceHealthService_CheckLiveness0_HTTP_Handler(srv ServiceHealthServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HealthCheckRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthServiceCheckLiveness)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckLiveness(ctx, req.(*HealthCheckRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HealthCheckResponse)
		return ctx.Result(200, reply)
	}
}

func _ServiceHealthService_CheckReadiness0_HTTP_Handler(srv ServiceHealthServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HealthCheckRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthServiceCheckReadiness)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckReadiness(ctx, req.(*HealthCheckRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HealthCheckResponse)
		return ctx.Result(200, reply)
	}
}

type ServiceHealthServiceHTTPClient interface {
	CheckLiveness(ctx context.Context, req *HealthCheckRequest, opts ...http.CallOption) (rsp *HealthCheckResponse, err error)
	CheckReadiness(ctx context.Context, req *HealthCheckRequest, opts ...http.CallOption) (rsp *HealthCheckResponse, err error)
}

type ServiceHealthServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHealthServiceHTTPClient(client *http.Client) ServiceHealthServiceHTTPClient {
	return &ServiceHealthServiceHTTPClientImpl{client}
}

func (c *ServiceHealthServiceHTTPClientImpl) CheckLiveness(ctx context.Context, in *HealthCheckRequest, opts ...http.CallOption) (*HealthCheckResponse, error) {
	var out HealthCheckResponse
	pattern := "/service/liveness"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthServiceCheckLiveness))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHealthServiceHTTPClientImpl) CheckReadiness(ctx context.Context, in *HealthCheckRequest, opts ...http.CallOption) (*HealthCheckResponse, error) {
	var out HealthCheckResponse
	pattern := "/service/readiness"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthServiceCheckReadiness))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
