package service

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "mps-job-analysis-go/api/job/v1"
	"mps-job-analysis-go/internal/biz"
)

// MockJobAnalysisUsecase 模拟用例
type MockJobAnalysisUsecase struct {
	mock.Mock
}

func (m *MockJobAnalysisUsecase) AnalyzeJob(ctx context.Context, param *biz.JobAnalysisParam) (*biz.JobAnalysisResult, error) {
	args := m.Called(ctx, param)
	return args.Get(0).(*biz.JobAnalysisResult), args.Error(1)
}

func (m *MockJobAnalysisUsecase) ReportResult(ctx context.Context, param *biz.JobExecutionResultReportParam) error {
	args := m.Called(ctx, param)
	return args.Error(0)
}

func (m *MockJobAnalysisUsecase) CheckHealth(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func TestJobAnalysisService_AnalyzeJob(t *testing.T) {
	mockUC := new(MockJobAnalysisUsecase)
	logger := log.NewStdLogger(os.Stdout)
	service := NewJobAnalysisService(mockUC, logger)

	ctx := context.Background()
	now := time.Now()

	// 准备测试数据
	req := &pb.AnalyzeJobRequest{
		EngineModel:          "mps-transcode-new",
		EngineParams:         `{"format":"mp4"}`,
		UserId:               "test-user",
		JobId:                "test-job-123",
		SliceProcess:         false,
		AnalysisMode:         "byParseEngineParams",
		Tag:                  "test",
		CreateTime:           timestamppb.New(now),
		MaxSliceNum:          10,
		MinSliceDuration:     30,
		UseWorkerBrainResult: false,
		InvokeWorkerBrain:    false,
		SpeedXRange:          []string{"5X"},
		IsAutoSpeedX:         false,
		PipelineId:           "test-pipeline",
		RequestId:            "test-request",
	}

	expectedResult := &biz.JobAnalysisResult{
		Success:     true,
		Code:        "Success",
		Message:     "Analysis completed successfully",
		ExecuteMode: biz.ExecuteModeSingle,
		QuotaSet: map[string]int64{
			"cpu":  1000,
			"gpu":  0,
			"disk": 1000,
		},
		ExpectCostTime: func() *int64 { t := int64(300); return &t }(),
	}

	// 设置mock期望
	mockUC.On("AnalyzeJob", ctx, mock.AnythingOfType("*biz.JobAnalysisParam")).Return(expectedResult, nil)

	// 执行测试
	resp, err := service.AnalyzeJob(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.True(t, resp.Success)
	assert.Equal(t, "Success", resp.Code)
	assert.Equal(t, pb.ExecuteMode_SINGLE, resp.ExecuteMode)
	assert.Equal(t, int64(300), resp.ExpectCostTime)
	assert.Equal(t, int64(1000), resp.QuotaSet["cpu"])
	assert.Equal(t, int64(0), resp.QuotaSet["gpu"])
	assert.Equal(t, int64(1000), resp.QuotaSet["disk"])

	// 验证mock调用
	mockUC.AssertExpectations(t)
}

func TestJobAnalysisService_ReportResult(t *testing.T) {
	mockUC := new(MockJobAnalysisUsecase)
	logger := log.NewStdLogger(os.Stdout)
	service := NewJobAnalysisService(mockUC, logger)

	ctx := context.Background()
	now := time.Now()

	req := &pb.ReportResultRequest{
		JobId: "test-job-123",
		AllocQuotaSet: map[string]int64{
			"cpu":  1000,
			"gpu":  0,
			"disk": 1000,
		},
		MaxQuotaSet: map[string]int64{
			"cpu":  1200,
			"gpu":  0,
			"disk": 1200,
		},
		AvgQuotaSet: map[string]int64{
			"cpu":  800,
			"gpu":  0,
			"disk": 800,
		},
		EngineModel:    "mps-transcode-new",
		EngineParams:   `{"format":"mp4"}`,
		ExpectCostTime: 300,
		RealCostTime:   280,
		CreateTime:     timestamppb.New(now),
		SubmitTime:     timestamppb.New(now.Add(time.Minute)),
		PipelineId:     "test-pipeline",
		Tag:            "test",
		RequestId:      "test-request",
		Product:        "mps",
		UserId:         "test-user",
		Env:            "test",
		Station:        "test-station",
	}

	// 设置mock期望
	mockUC.On("ReportResult", ctx, mock.AnythingOfType("*biz.JobExecutionResultReportParam")).Return(nil)

	// 执行测试
	resp, err := service.ReportResult(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "Success", resp.Code)
	assert.Equal(t, "Result reported successfully", resp.Message)

	// 验证mock调用
	mockUC.AssertExpectations(t)
}

func TestJobAnalysisService_CheckLiveness(t *testing.T) {
	mockUC := new(MockJobAnalysisUsecase)
	logger := log.NewStdLogger(os.Stdout)
	service := NewJobAnalysisService(mockUC, logger)

	ctx := context.Background()
	req := &pb.HealthCheckRequest{}

	// 执行测试
	resp, err := service.CheckLiveness(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "Success", resp.Code)
	assert.Equal(t, "Service is alive", resp.Message)
}

func TestJobAnalysisService_CheckReadiness(t *testing.T) {
	mockUC := new(MockJobAnalysisUsecase)
	logger := log.NewStdLogger(os.Stdout)
	service := NewJobAnalysisService(mockUC, logger)

	ctx := context.Background()
	req := &pb.HealthCheckRequest{}

	// 设置mock期望 - 健康检查通过
	mockUC.On("CheckHealth", ctx).Return(nil)

	// 执行测试
	resp, err := service.CheckReadiness(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "Success", resp.Code)
	assert.Equal(t, "Service is ready", resp.Message)

	// 验证mock调用
	mockUC.AssertExpectations(t)
}
