package biz

import "time"

// EngineQuotaConfig 引擎配额配置
type EngineQuotaConfig struct {
	ID                              uint64            `json:"id"`
	Name                           string            `json:"name"`
	Configs                        string            `json:"configs"`
	QuotaSet                       map[string]int64  `json:"quota_set"`
	Cost                           *uint64           `json:"cost"`
	Speed                          *float64          `json:"speed"`
	DiskRatio                      *float64          `json:"disk_ratio"`
	DiskQuota                      *uint64           `json:"disk_quota"`
	CustomParam                    *string           `json:"custom_param"`
	MaxMigrateRetry                uint32            `json:"max_migrate_retry"`
	MigrateDiscardQuotaThreshold   map[string]int64  `json:"migrate_discard_quota_threshold"`
}

// JobAnalysisParam 作业分析参数
type JobAnalysisParam struct {
	EngineModel          string                 `json:"engine_model"`
	EngineParams         string                 `json:"engine_params"`
	UserID               string                 `json:"user_id"`
	JobID                string                 `json:"job_id"`
	SliceProcess         bool                   `json:"slice_process"`
	AnalysisMode         string                 `json:"analysis_mode"`
	ScheduleParams       *ScheduleParams        `json:"schedule_params"`
	Tag                  string                 `json:"tag"`
	CreateTime           *time.Time             `json:"create_time"`
	MaxSliceNum          *int32                 `json:"max_slice_num"`
	MinSliceDuration     *int32                 `json:"min_slice_duration"`
	UseWorkerBrainResult bool                   `json:"use_worker_brain_result"`
	InvokeWorkerBrain    bool                   `json:"invoke_worker_brain"`
	SpeedXRange          []string               `json:"speed_x_range"`
	IsAutoSpeedX         bool                   `json:"is_auto_speed_x"`
	Trace                map[string]interface{} `json:"trace"`
	PipelineID           string                 `json:"pipeline_id"`
	RequestID            string                 `json:"request_id"`
}

// ScheduleParams 调度参数
type ScheduleParams struct {
	PipelineID    string            `json:"pipeline_id"`
	QuotaSet      map[string]int64  `json:"quota_set"`
	Priority      *int32            `json:"priority"`
	ExpectCostTime *int32           `json:"expect_cost_time"`
	Inputs        []Input           `json:"inputs"`
	Configs       []Config          `json:"configs"`
	ParallelNum   *int32            `json:"parallel_num"`
	SlaLevel      string            `json:"sla_level"`
	SliceNum      *int32            `json:"slice_num"`
	SpeedXRange   []string          `json:"speed_x_range"`
}

// Input 输入参数
type Input struct {
	Duration      float64 `json:"duration"`
	VideoBitrate  float64 `json:"video_bitrate"`
	AvgFps        float64 `json:"avg_fps"`
	Size          int64   `json:"size"`
	Format        string  `json:"format"`
	Fps           float64 `json:"fps"`
	Width         int32   `json:"width"`
	AudioCodec    string  `json:"audio_codec"`
	AudioBitrate  float64 `json:"audio_bitrate"`
	Height        int32   `json:"height"`
	VideoCodec    string  `json:"video_codec"`
}

// Config 配置参数
type Config struct {
	Duration      float64           `json:"duration"`
	NhVersion     string            `json:"nh_version"`
	Format        string            `json:"format"`
	Fps           float64           `json:"fps"`
	ID            string            `json:"id"`
	AudioCodec    string            `json:"audio_codec"`
	JobType       string            `json:"job_type"`
	TemplateID    string            `json:"template_id"`
	AudioBitrate  float64           `json:"audio_bitrate"`
	VideoCodec    string            `json:"video_codec"`
	AudioOnly     bool              `json:"audio_only"`
	Extend        map[string]string `json:"extend"`
	GpuRestore    string            `json:"gpu_restore"`
	ByWorkerBrain *bool             `json:"by_worker_brain"`
}

// JobAnalysisResult 作业分析结果
type JobAnalysisResult struct {
	Success                        bool                           `json:"success"`
	Code                          string                         `json:"code"`
	Message                       string                         `json:"message"`
	ExecuteMode                   ExecuteMode                    `json:"execute_mode"`
	ExpectCostTime                *int64                         `json:"expect_cost_time"`
	QuotaSet                      map[string]int64               `json:"quota_set"`
	OriginQuotaSet                map[string]int64               `json:"origin_quota_set"`
	SlaFinishDelay                *int64                         `json:"sla_finish_delay"`
	SlaQueuingDelay               *int64                         `json:"sla_queuing_delay"`
	MaxMigrateRetry               *int32                         `json:"max_migrate_retry"`
	MigrateDiscardQuotaThreshold  map[string]int64               `json:"migrate_discard_quota_threshold"`
	Graph                         *DagJobGraph                   `json:"graph"`
	GraphMap                      map[string]*DagJobGraph        `json:"graph_map"`
	SpeedXMessage                 map[string]*SpeedXMessage      `json:"speed_x_message"`
}

// ExecuteMode 执行模式
type ExecuteMode string

const (
	ExecuteModeProbeFirst ExecuteMode = "PROBE_FIRST"
	ExecuteModeSingle     ExecuteMode = "SINGLE"
	ExecuteModeDAG        ExecuteMode = "DAG"
)

// DagJobGraph DAG作业图
type DagJobGraph struct {
	JobVertexs []JobVertex `json:"job_vertexs"`
	JobEdges   []JobEdge   `json:"job_edges"`
}

// JobVertex 作业顶点
type JobVertex struct {
	Name              string                `json:"name"`
	EngineModel       string                `json:"engine_model"`
	EngineParams      string                `json:"engine_params"`
	ResourceRequest   *TaskResourceRequest  `json:"resource_request"`
	ScheduleParams    *ScheduleParams       `json:"schedule_params"`
	MaxMigrateRetry   *int32                `json:"max_migrate_retry"`
}

// JobEdge 作业边
type JobEdge struct {
	Name       string `json:"name"`
	From       string `json:"from"`
	To         string `json:"to"`
	Mode       string `json:"mode"`
	Status     string `json:"status"`
	FromStatus string `json:"from_status"`
}

// TaskResourceRequest 任务资源请求
type TaskResourceRequest struct {
	ExpectCostTime int64            `json:"expect_cost_time"`
	QuotaSet       map[string]int64 `json:"quota_set"`
}

// SpeedXMessage 倍速消息
type SpeedXMessage struct {
	Code    int32  `json:"code"`
	Message string `json:"message"`
}

// JobExecutionResultReportParam 作业执行结果报告参数
type JobExecutionResultReportParam struct {
	JobID          string                 `json:"job_id"`
	AllocQuotaSet  map[string]int64       `json:"alloc_quota_set"`
	MaxQuotaSet    map[string]int64       `json:"max_quota_set"`
	AvgQuotaSet    map[string]int64       `json:"avg_quota_set"`
	EngineModel    string                 `json:"engine_model"`
	EngineParams   string                 `json:"engine_params"`
	ExpectCostTime *int64                 `json:"expect_cost_time"`
	RealCostTime   *int64                 `json:"real_cost_time"`
	CreateTime     *time.Time             `json:"create_time"`
	SubmitTime     *time.Time             `json:"submit_time"`
	Trace          map[string]interface{} `json:"trace"`
	PipelineID     string                 `json:"pipeline_id"`
	Tag            string                 `json:"tag"`
	RequestID      string                 `json:"request_id"`
	Product        string                 `json:"product"`
	ResultData     interface{}            `json:"result_data"`
	UserID         string                 `json:"user_id"`
	Env            string                 `json:"env"`
	Station        string                 `json:"station"`
}

// 分析模式常量
const (
	AnalysisModeByParseEngineParams = "byParseEngineParams"
	AnalysisModeByProbeMeta         = "byProbeMeta"
	AnalysisModeByScheduleParams    = "byScheduleParams"
)
