package biz

import (
	"context"
	"github.com/google/wire"
)

// JobAnalysisUsecase 作业分析用例接口
type JobAnalysisUsecase interface {
	AnalyzeJob(ctx context.Context, param *JobAnalysisParam) (*JobAnalysisResult, error)
	ReportResult(ctx context.Context, param *JobExecutionResultReportParam) error
	CheckHealth(ctx context.Context) error
}

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(NewJobAnalysisUsecase)
