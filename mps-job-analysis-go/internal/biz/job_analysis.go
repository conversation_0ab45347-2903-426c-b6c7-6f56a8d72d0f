package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// JobAnalysisRepo 作业分析仓储接口
type JobAnalysisRepo interface {
	GetEngineQuotaConfig(ctx context.Context, name string) (*EngineQuotaConfig, error)
	SaveEngineQuotaConfig(ctx context.Context, config *EngineQuotaConfig) error
	ListEngineQuotaConfigs(ctx context.Context, limit, offset int) ([]*EngineQuotaConfig, error)
	SaveJobResult(ctx context.Context, param *JobExecutionResultReportParam) error
	GetCachedResult(ctx context.Context, key string) (*JobAnalysisResult, error)
	SetCachedResult(ctx context.Context, key string, result *JobAnalysisResult, expiration time.Duration) error
	GenerateCacheKey(param *JobAnalysisParam) string
}

// ExternalServiceRepo 外部服务仓储接口
type ExternalServiceRepo interface {
	CallDMES(ctx context.Context, param interface{}) (interface{}, error)
	CallWorkerBrain(ctx context.Context, engineModel string, param interface{}) (interface{}, error)
	CallSLA(ctx context.Context, param interface{}) (interface{}, error)
	CallMediaMeta(ctx context.Context, param interface{}) (interface{}, error)
}

// jobAnalysisUsecase 作业分析用例实现
type jobAnalysisUsecase struct {
	repo        JobAnalysisRepo
	externalRepo ExternalServiceRepo
	log         *log.Helper
}

// NewJobAnalysisUsecase 创建作业分析用例
func NewJobAnalysisUsecase(repo JobAnalysisRepo, externalRepo ExternalServiceRepo, logger log.Logger) *JobAnalysisUsecase {
	return &JobAnalysisUsecase{
		repo:        repo,
		externalRepo: externalRepo,
		log:         log.NewHelper(logger),
	}
}

// AnalyzeJob 分析作业
func (uc *JobAnalysisUsecase) AnalyzeJob(ctx context.Context, param *JobAnalysisParam) (*JobAnalysisResult, error) {
	// 检查缓存
	cacheKey := uc.repo.GenerateCacheKey(param)
	if cached, err := uc.repo.GetCachedResult(ctx, cacheKey); err == nil && cached != nil {
		uc.log.WithContext(ctx).Infof("cache hit for job analysis: %s", param.JobID)
		return cached, nil
	}

	var result *JobAnalysisResult
	var err error

	// 根据分析模式选择不同的处理逻辑
	switch param.AnalysisMode {
	case AnalysisModeByParseEngineParams:
		result, err = uc.analysisByParseEngineParams(ctx, param)
	case AnalysisModeByProbeMeta:
		result, err = uc.analysisByProbeMeta(ctx, param)
	case AnalysisModeByScheduleParams:
		result, err = uc.analysisByScheduleParams(ctx, param)
	default:
		result, err = uc.analysisByParseEngineParams(ctx, param)
	}

	if err != nil {
		return nil, err
	}

	// 缓存结果
	if result.Success {
		if err := uc.repo.SetCachedResult(ctx, cacheKey, result, 30*time.Minute); err != nil {
			uc.log.WithContext(ctx).Warnf("failed to cache result: %v", err)
		}
	}

	return result, nil
}

// analysisByParseEngineParams 基于引擎参数分析
func (uc *JobAnalysisUsecase) analysisByParseEngineParams(ctx context.Context, param *JobAnalysisParam) (*JobAnalysisResult, error) {
	uc.log.WithContext(ctx).Infof("analyzing job by parse engine params: %s", param.JobID)

	// 获取引擎配额配置
	config, err := uc.repo.GetEngineQuotaConfig(ctx, param.EngineModel)
	if err != nil {
		return nil, err
	}

	result := &JobAnalysisResult{
		Success:     true,
		Code:        "Success",
		Message:     "Analysis completed successfully",
		ExecuteMode: ExecuteModeSingle,
	}

	if config != nil {
		result.QuotaSet = config.QuotaSet
		if config.Cost != nil {
			costTime := int64(*config.Cost)
			result.ExpectCostTime = &costTime
		}
		if config.MaxMigrateRetry > 0 {
			maxRetry := int32(config.MaxMigrateRetry)
			result.MaxMigrateRetry = &maxRetry
		}
		result.MigrateDiscardQuotaThreshold = config.MigrateDiscardQuotaThreshold
	} else {
		// 默认配额
		result.QuotaSet = map[string]int64{
			"cpu": 1000,
			"gpu": 0,
			"disk": 1000,
		}
		defaultCostTime := int64(300)
		result.ExpectCostTime = &defaultCostTime
	}

	// 如果启用了WorkerBrain，调用外部服务
	if param.InvokeWorkerBrain {
		if brainResult, err := uc.callWorkerBrain(ctx, param); err == nil {
			uc.mergeWorkerBrainResult(result, brainResult)
		} else {
			uc.log.WithContext(ctx).Warnf("failed to call worker brain: %v", err)
		}
	}

	// 如果是切片处理，生成DAG图
	if param.SliceProcess {
		result.ExecuteMode = ExecuteModeDAG
		result.Graph = uc.generateDAGGraph(ctx, param, result)
	}

	return result, nil
}

// analysisByProbeMeta 基于探测元数据分析（已废弃）
func (uc *JobAnalysisUsecase) analysisByProbeMeta(ctx context.Context, param *JobAnalysisParam) (*JobAnalysisResult, error) {
	uc.log.WithContext(ctx).Warnf("analysisByProbeMeta is deprecated, fallback to analysisByParseEngineParams")
	return uc.analysisByParseEngineParams(ctx, param)
}

// analysisByScheduleParams 基于调度参数分析
func (uc *JobAnalysisUsecase) analysisByScheduleParams(ctx context.Context, param *JobAnalysisParam) (*JobAnalysisResult, error) {
	uc.log.WithContext(ctx).Infof("analyzing job by schedule params: %s", param.JobID)

	if param.ScheduleParams == nil {
		return uc.analysisByParseEngineParams(ctx, param)
	}

	result := &JobAnalysisResult{
		Success:     true,
		Code:        "Success",
		Message:     "Analysis completed successfully",
		ExecuteMode: ExecuteModeSingle,
	}

	// 使用调度参数中的配额
	if param.ScheduleParams.QuotaSet != nil && len(param.ScheduleParams.QuotaSet) > 0 {
		result.QuotaSet = param.ScheduleParams.QuotaSet
	}

	// 使用调度参数中的预期耗时
	if param.ScheduleParams.ExpectCostTime != nil {
		expectTime := int64(*param.ScheduleParams.ExpectCostTime)
		result.ExpectCostTime = &expectTime
	}

	// 处理切片逻辑
	if param.ScheduleParams.SliceNum != nil && *param.ScheduleParams.SliceNum > 1 {
		result.ExecuteMode = ExecuteModeDAG
		result.Graph = uc.generateDAGGraph(ctx, param, result)
	}

	// SLA分析
	if param.ScheduleParams.SlaLevel != "" {
		if slaResult, err := uc.callSLA(ctx, param); err == nil {
			uc.mergeSLAResult(result, slaResult)
		} else {
			uc.log.WithContext(ctx).Warnf("failed to call SLA service: %v", err)
		}
	}

	return result, nil
}

// callWorkerBrain 调用WorkerBrain服务
func (uc *JobAnalysisUsecase) callWorkerBrain(ctx context.Context, param *JobAnalysisParam) (interface{}, error) {
	return uc.externalRepo.CallWorkerBrain(ctx, param.EngineModel, param)
}

// callSLA 调用SLA服务
func (uc *JobAnalysisUsecase) callSLA(ctx context.Context, param *JobAnalysisParam) (interface{}, error) {
	return uc.externalRepo.CallSLA(ctx, param)
}

// mergeWorkerBrainResult 合并WorkerBrain结果
func (uc *JobAnalysisUsecase) mergeWorkerBrainResult(result *JobAnalysisResult, brainResult interface{}) {
	// 这里实现WorkerBrain结果的合并逻辑
	uc.log.Info("merging worker brain result")
}

// mergeSLAResult 合并SLA结果
func (uc *JobAnalysisUsecase) mergeSLAResult(result *JobAnalysisResult, slaResult interface{}) {
	// 这里实现SLA结果的合并逻辑
	uc.log.Info("merging SLA result")
}

// generateDAGGraph 生成DAG图
func (uc *JobAnalysisUsecase) generateDAGGraph(ctx context.Context, param *JobAnalysisParam, result *JobAnalysisResult) *DagJobGraph {
	// 简化的DAG图生成逻辑
	graph := &DagJobGraph{
		JobVertexs: []JobVertex{
			{
				Name:        "main_task",
				EngineModel: param.EngineModel,
				EngineParams: param.EngineParams,
				ResourceRequest: &TaskResourceRequest{
					ExpectCostTime: *result.ExpectCostTime,
					QuotaSet:       result.QuotaSet,
				},
			},
		},
		JobEdges: []JobEdge{},
	}

	return graph
}

// ReportResult 报告作业结果
func (uc *JobAnalysisUsecase) ReportResult(ctx context.Context, param *JobExecutionResultReportParam) error {
	uc.log.WithContext(ctx).Infof("reporting job result: %s", param.JobID)
	return uc.repo.SaveJobResult(ctx, param)
}

// CheckHealth 健康检查
func (uc *JobAnalysisUsecase) CheckHealth(ctx context.Context) error {
	// 检查数据库连接
	_, err := uc.repo.ListEngineQuotaConfigs(ctx, 1, 0)
	return err
}
