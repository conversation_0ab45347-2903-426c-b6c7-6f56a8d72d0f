syntax = "proto3";

package kratos.api;

option go_package = "mps-job-analysis-go/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  ExternalServices external_services = 3;
  App app = 4;
  Log log = 5;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
    int32 max_idle_conns = 3;
    int32 max_open_conns = 4;
    google.protobuf.Duration conn_max_lifetime = 5;
  }
  message Redis {
    string addr = 1;
    string password = 2;
    int32 db = 3;
    google.protobuf.Duration dial_timeout = 4;
    google.protobuf.Duration read_timeout = 5;
    google.protobuf.Duration write_timeout = 6;
  }
  Database database = 1;
  Redis redis = 2;
}

message ExternalServices {
  message DMES {
    string url = 1;
    google.protobuf.Duration timeout = 2;
  }
  message WorkerBrain {
    string mps_transcode_new_url = 1;
    string mps_editing_url = 2;
    google.protobuf.Duration timeout = 3;
  }
  message SLA {
    string url = 1;
    google.protobuf.Duration timeout = 2;
  }
  message MediaMeta {
    string url = 1;
    google.protobuf.Duration timeout = 2;
  }
  DMES dmes = 1;
  WorkerBrain worker_brain = 2;
  SLA sla = 3;
  MediaMeta media_meta = 4;
}

message App {
  string region = 1;
  string deploy_env = 2;
  Media media = 3;
  
  message Media {
    int32 default_disk_times_for_multi_transcode = 1;
    int64 max_disk_for_multi_transcode = 2;
    int64 min_disk_for_multi_transcode = 3;
    string service_bucket_list = 4;
  }
}

message Log {
  string level = 1;
  string format = 2;
  string output = 3;
}
