package data

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"mps-job-analysis-go/internal/biz"
)

type jobAnalysisRepo struct {
	data *Data
	log  *log.Helper
}

// NewJobAnalysisRepo .
func NewJobAnalysisRepo(data *Data, logger log.Logger) biz.JobAnalysisRepo {
	return &jobAnalysisRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *jobAnalysisRepo) GetEngineQuotaConfig(ctx context.Context, name string) (*biz.EngineQuotaConfig, error) {
	var config MpsEngineQuotaConfig
	if err := r.data.db.WithContext(ctx).Where("name = ?", name).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	// 解析 quota_set JSON
	var quotaSet map[string]int64
	if err := json.Unmarshal([]byte(config.QuotaSet), &quotaSet); err != nil {
		r.log.Errorf("failed to unmarshal quota_set: %v", err)
		return nil, err
	}

	// 解析 migrate_discard_quota_threshold JSON
	var migrateThreshold map[string]int64
	if config.MigrateDiscardQuotaThreshold != nil {
		if err := json.Unmarshal([]byte(*config.MigrateDiscardQuotaThreshold), &migrateThreshold); err != nil {
			r.log.Errorf("failed to unmarshal migrate_discard_quota_threshold: %v", err)
		}
	}

	return &biz.EngineQuotaConfig{
		ID:                              config.ID,
		Name:                           config.Name,
		Configs:                        config.Configs,
		QuotaSet:                       quotaSet,
		Cost:                           config.Cost,
		Speed:                          config.Speed,
		DiskRatio:                      config.DiskRatio,
		DiskQuota:                      config.DiskQuota,
		CustomParam:                    config.CustomParam,
		MaxMigrateRetry:                config.MaxMigrateRetry,
		MigrateDiscardQuotaThreshold:   migrateThreshold,
	}, nil
}

func (r *jobAnalysisRepo) SaveEngineQuotaConfig(ctx context.Context, config *biz.EngineQuotaConfig) error {
	// 序列化 quota_set
	quotaSetJSON, err := json.Marshal(config.QuotaSet)
	if err != nil {
		return err
	}

	// 序列化 migrate_discard_quota_threshold
	var migrateThresholdJSON *string
	if config.MigrateDiscardQuotaThreshold != nil {
		data, err := json.Marshal(config.MigrateDiscardQuotaThreshold)
		if err != nil {
			return err
		}
		str := string(data)
		migrateThresholdJSON = &str
	}

	dbConfig := &MpsEngineQuotaConfig{
		ID:                              config.ID,
		GmtCreate:                      time.Now(),
		GmtModified:                    time.Now(),
		Name:                           config.Name,
		Configs:                        config.Configs,
		QuotaSet:                       string(quotaSetJSON),
		Cost:                           config.Cost,
		Speed:                          config.Speed,
		DiskRatio:                      config.DiskRatio,
		DiskQuota:                      config.DiskQuota,
		CustomParam:                    config.CustomParam,
		MaxMigrateRetry:                config.MaxMigrateRetry,
		MigrateDiscardQuotaThreshold:   migrateThresholdJSON,
	}

	if config.ID == 0 {
		return r.data.db.WithContext(ctx).Create(dbConfig).Error
	} else {
		dbConfig.GmtModified = time.Now()
		return r.data.db.WithContext(ctx).Save(dbConfig).Error
	}
}

func (r *jobAnalysisRepo) ListEngineQuotaConfigs(ctx context.Context, limit, offset int) ([]*biz.EngineQuotaConfig, error) {
	var configs []MpsEngineQuotaConfig
	if err := r.data.db.WithContext(ctx).Limit(limit).Offset(offset).Find(&configs).Error; err != nil {
		return nil, err
	}

	result := make([]*biz.EngineQuotaConfig, 0, len(configs))
	for _, config := range configs {
		// 解析 quota_set JSON
		var quotaSet map[string]int64
		if err := json.Unmarshal([]byte(config.QuotaSet), &quotaSet); err != nil {
			r.log.Errorf("failed to unmarshal quota_set for config %s: %v", config.Name, err)
			continue
		}

		// 解析 migrate_discard_quota_threshold JSON
		var migrateThreshold map[string]int64
		if config.MigrateDiscardQuotaThreshold != nil {
			if err := json.Unmarshal([]byte(*config.MigrateDiscardQuotaThreshold), &migrateThreshold); err != nil {
				r.log.Errorf("failed to unmarshal migrate_discard_quota_threshold for config %s: %v", config.Name, err)
			}
		}

		result = append(result, &biz.EngineQuotaConfig{
			ID:                              config.ID,
			Name:                           config.Name,
			Configs:                        config.Configs,
			QuotaSet:                       quotaSet,
			Cost:                           config.Cost,
			Speed:                          config.Speed,
			DiskRatio:                      config.DiskRatio,
			DiskQuota:                      config.DiskQuota,
			CustomParam:                    config.CustomParam,
			MaxMigrateRetry:                config.MaxMigrateRetry,
			MigrateDiscardQuotaThreshold:   migrateThreshold,
		})
	}

	return result, nil
}

func (r *jobAnalysisRepo) SaveJobResult(ctx context.Context, param *biz.JobExecutionResultReportParam) error {
	// 这里可以将作业结果保存到数据库或发送到消息队列
	// 目前只记录日志
	r.log.WithContext(ctx).Infof("job result reported: jobId=%s, realCostTime=%v, expectCostTime=%v", 
		param.JobID, param.RealCostTime, param.ExpectCostTime)
	
	// 可以在这里实现具体的存储逻辑，比如：
	// 1. 保存到专门的作业结果表
	// 2. 发送到消息队列进行异步处理
	// 3. 调用外部服务进行数据收集
	
	return nil
}

func (r *jobAnalysisRepo) GetCachedResult(ctx context.Context, key string) (*biz.JobAnalysisResult, error) {
	val, err := r.data.rdb.Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	var result biz.JobAnalysisResult
	if err := json.Unmarshal([]byte(val), &result); err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *jobAnalysisRepo) SetCachedResult(ctx context.Context, key string, result *biz.JobAnalysisResult, expiration time.Duration) error {
	data, err := json.Marshal(result)
	if err != nil {
		return err
	}

	return r.data.rdb.Set(ctx, key, data, expiration).Err()
}

func (r *jobAnalysisRepo) GenerateCacheKey(param *biz.JobAnalysisParam) string {
	// 生成缓存键，基于关键参数
	return fmt.Sprintf("job_analysis:%s:%s:%s", param.EngineModel, param.AnalysisMode, param.JobID)
}
