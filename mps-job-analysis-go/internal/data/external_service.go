package data

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"mps-job-analysis-go/internal/biz"
	"mps-job-analysis-go/internal/conf"
)

type externalServiceRepo struct {
	conf   *conf.ExternalServices
	client *http.Client
	log    *log.Helper
}

// NewExternalServiceRepo .
func NewExternalServiceRepo(c *conf.ExternalServices, logger log.Logger) biz.ExternalServiceRepo {
	return &externalServiceRepo{
		conf: c,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		log: log.NewHelper(logger),
	}
}

func (r *externalServiceRepo) CallDMES(ctx context.Context, param interface{}) (interface{}, error) {
	url := r.conf.Dmes.Url + "api/v1/estimate"
	return r.makeHTTPRequest(ctx, "POST", url, param)
}

func (r *externalServiceRepo) CallWorkerBrain(ctx context.Context, engineModel string, param interface{}) (interface{}, error) {
	var url string
	switch engineModel {
	case "mps-transcode-new":
		url = r.conf.WorkerBrain.MpsTranscodeNewUrl + "api/v1/analysis"
	case "mps-editing":
		url = r.conf.WorkerBrain.MpsEditingUrl + "api/v1/analysis"
	default:
		url = r.conf.WorkerBrain.MpsTranscodeNewUrl + "api/v1/analysis"
	}
	
	return r.makeHTTPRequest(ctx, "POST", url, param)
}

func (r *externalServiceRepo) CallSLA(ctx context.Context, param interface{}) (interface{}, error) {
	url := r.conf.Sla.Url + "api/v1/analysis"
	return r.makeHTTPRequest(ctx, "POST", url, param)
}

func (r *externalServiceRepo) CallMediaMeta(ctx context.Context, param interface{}) (interface{}, error) {
	url := r.conf.MediaMeta.Url + "api/v1/probe"
	return r.makeHTTPRequest(ctx, "POST", url, param)
}

func (r *externalServiceRepo) makeHTTPRequest(ctx context.Context, method, url string, param interface{}) (interface{}, error) {
	var body io.Reader
	if param != nil {
		jsonData, err := json.Marshal(param)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	req.Header.Set("User-Agent", "mps-job-analysis-go/1.0")

	r.log.WithContext(ctx).Infof("calling external service: %s %s", method, url)

	resp, err := r.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	var result interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	r.log.WithContext(ctx).Infof("external service call completed: %s", url)
	return result, nil
}
