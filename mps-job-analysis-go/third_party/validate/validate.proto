syntax = "proto2";
package validate;

import "google/protobuf/descriptor.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/envoyproxy/protoc-gen-validate/validate";
option java_package = "io.envoyproxy.pgv.validate";

// Validation rules applied at the message level
extend google.protobuf.MessageOptions {
  // Disabled nullifies any validation rules for this message, including any
  // message fields associated with it that do support validation.
  optional bool disabled = 1071;
  // Ignore skips generation of validation methods for this message.
  optional bool ignored = 1072;
}

// Validation rules applied at the oneof level
extend google.protobuf.OneofOptions {
  // Required ensures that exactly one the field options in a oneof is set;
  // validation fails if no fields in the oneof are set.
  optional bool required = 1071;
}

// Validation rules applied at the field level
extend google.protobuf.FieldOptions {
  // Rules specify the validations to be performed on this field. By default,
  // no validation is performed against a field.
  optional FieldRules rules = 1071;
}

// FieldRules encapsulates the rules for each type of field. Depending on the
// field, the correct set should be used to ensure proper validations.
message FieldRules {
  optional MessageRules message = 17;
  oneof type {
    // Scalar Field Types
    FloatRules    float    = 1;
    DoubleRules   double   = 2;
    Int32Rules    int32    = 3;
    Int64Rules    int64    = 4;
    UInt32Rules   uint32   = 5;
    UInt64Rules   uint64   = 6;
    SInt32Rules   sint32   = 7;
    SInt64Rules   sint64   = 8;
    Fixed32Rules  fixed32  = 9;
    Fixed64Rules  fixed64  = 10;
    SFixed32Rules sfixed32 = 11;
    SFixed64Rules sfixed64 = 12;
    BoolRules     bool     = 13;
    StringRules   string   = 14;
    BytesRules    bytes    = 15;

    // Complex Field Types
    EnumRules     enum     = 16;
    RepeatedRules repeated = 18;
    MapRules      map      = 19;

    // Well-Known Field Types
    AnyRules       any       = 20;
    DurationRules  duration  = 21;
    TimestampRules timestamp = 22;
  }
}

// FloatRules describes the constraints applied to `float` values
message FloatRules {
  // Const specifies that this field must be exactly the specified value
  optional float const = 1;

  // Lt specifies that this field must be less than the specified value,
  // exclusive
  optional float lt = 2;

  // Lte specifies that this field must be less than or equal to the
  // specified value, inclusive
  optional float lte = 3;

  // Gt specifies that this field must be greater than the specified value,
  // exclusive. If the value of Gt is larger than a specified Lt or Lte, the
  // range is reversed.
  optional float gt = 4;

  // Gte specifies that this field must be greater than or equal to the
  // specified value, inclusive. If the value of Gte is larger than a
  // specified Lt or Lte, the range is reversed.
  optional float gte = 5;

  // In specifies that this field must be equal to one of the specified
  // values
  repeated float in = 6;

  // NotIn specifies that this field cannot be equal to one of the specified
  // values
  repeated float not_in = 7;

  // IgnoreEmpty specifies that the validation rules of this field should be
  // evaluated only if the field is not empty
  optional bool ignore_empty = 8;
}

// DoubleRules describes the constraints applied to `double` values
message DoubleRules {
  // Const specifies that this field must be exactly the specified value
  optional double const = 1;

  // Lt specifies that this field must be less than the specified value,
  // exclusive
  optional double lt = 2;

  // Lte specifies that this field must be less than or equal to the
  // specified value, inclusive
  optional double lte = 3;

  // Gt specifies that this field must be greater than the specified value,
  // exclusive. If the value of Gt is larger than a specified Lt or Lte, the
  // range is reversed.
  optional double gt = 4;

  // Gte specifies that this field must be greater than or equal to the
  // specified value, inclusive. If the value of Gte is larger than a
  // specified Lt or Lte, the range is reversed.
  optional double gte = 5;

  // In specifies that this field must be equal to one of the specified
  // values
  repeated double in = 6;

  // NotIn specifies that this field cannot be equal to one of the specified
  // values
  repeated double not_in = 7;

  // IgnoreEmpty specifies that the validation rules of this field should be
  // evaluated only if the field is not empty
  optional bool ignore_empty = 8;
}

// Int32Rules describes the constraints applied to `int32` values
message Int32Rules {
  // Const specifies that this field must be exactly the specified value
  optional int32 const = 1;

  // Lt specifies that this field must be less than the specified value,
  // exclusive
  optional int32 lt = 2;

  // Lte specifies that this field must be less than or equal to the
  // specified value, inclusive
  optional int32 lte = 3;

  // Gt specifies that this field must be greater than the specified value,
  // exclusive. If the value of Gt is larger than a specified Lt or Lte, the
  // range is reversed.
  optional int32 gt = 4;

  // Gte specifies that this field must be greater than or equal to the
  // specified value, inclusive. If the value of Gte is larger than a
  // specified Lt or Lte, the range is reversed.
  optional int32 gte = 5;

  // In specifies that this field must be equal to one of the specified
  // values
  repeated int32 in = 6;

  // NotIn specifies that this field cannot be equal to one of the specified
  // values
  repeated int32 not_in = 7;

  // IgnoreEmpty specifies that the validation rules of this field should be
  // evaluated only if the field is not empty
  optional bool ignore_empty = 8;
}

// Int64Rules describes the constraints applied to `int64` values
message Int64Rules {
  // Const specifies that this field must be exactly the specified value
  optional int64 const = 1;

  // Lt specifies that this field must be less than the specified value,
  // exclusive
  optional int64 lt = 2;

  // Lte specifies that this field must be less than or equal to the
  // specified value, inclusive
  optional int64 lte = 3;

  // Gt specifies that this field must be greater than the specified value,
  // exclusive. If the value of Gt is larger than a specified Lt or Lte, the
  // range is reversed.
  optional int64 gt = 4;

  // Gte specifies that this field must be greater than or equal to the
  // specified value, inclusive. If the value of Gte is larger than a
  // specified Lt or Lte, the range is reversed.
  optional int64 gte = 5;

  // In specifies that this field must be equal to one of the specified
  // values
  repeated int64 in = 6;

  // NotIn specifies that this field cannot be equal to one of the specified
  // values
  repeated int64 not_in = 7;

  // IgnoreEmpty specifies that the validation rules of this field should be
  // evaluated only if the field is not empty
  optional bool ignore_empty = 8;
}

// UInt32Rules describes the constraints applied to `uint32` values
message UInt32Rules {
  // Const specifies that this field must be exactly the specified value
  optional uint32 const = 1;

  // Lt specifies that this field must be less than the specified value,
  // exclusive
  optional uint32 lt = 2;

  // Lte specifies that this field must be less than or equal to the
  // specified value, inclusive
  optional uint32 lte = 3;

  // Gt specifies that this field must be greater than the specified value,
  // exclusive. If the value of Gt is larger than a specified Lt or Lte, the
  // range is reversed.
  optional uint32 gt = 4;

  // Gte specifies that this field must be greater than or equal to the
  // specified value, inclusive. If the value of Gte is larger than a
  // specified Lt or Lte, the range is reversed.
  optional uint32 gte = 5;

  // In specifies that this field must be equal to one of the specified
  // values
  repeated uint32 in = 6;

  // NotIn specifies that this field cannot be equal to one of the specified
  // values
  repeated uint32 not_in = 7;

  // IgnoreEmpty specifies that the validation rules of this field should be
  // evaluated only if the field is not empty
  optional bool ignore_empty = 8;
}

// UInt64Rules describes the constraints applied to `uint64` values
message UInt64Rules {
  // Const specifies that this field must be exactly the specified value
  optional uint64 const = 1;

  // Lt specifies that this field must be less than the specified value,
  // exclusive
  optional uint64 lt = 2;

  // Lte specifies that this field must be less than or equal to the
  // specified value, inclusive
  optional uint64 lte = 3;

  // Gt specifies that this field must be greater than the specified value,
  // exclusive. If the value of Gt is larger than a specified Lt or Lte, the
  // range is reversed.
  optional uint64 gt = 4;

  // Gte specifies that this field must be greater than or equal to the
  // specified value, inclusive. If the value of Gte is larger than a
  // specified Lt or Lte, the range is reversed.
  optional uint64 gte = 5;

  // In specifies that this field must be equal to one of the specified
  // values
  repeated uint64 in = 6;

  // NotIn specifies that this field cannot be equal to one of the specified
  // values
  repeated uint64 not_in = 7;

  // IgnoreEmpty specifies that the validation rules of this field should be
  // evaluated only if the field is not empty
  optional bool ignore_empty = 8;
}

// SInt32Rules describes the constraints applied to `sint32` values
message SInt32Rules {
  // Const specifies that this field must be exactly the specified value
  optional sint32 const = 1;

  // Lt specifies that this field must be less than the specified value,
  // exclusive
  optional sint32 lt = 2;

  // Lte specifies that this field must be less than or equal to the
  // specified value, inclusive
  optional sint32 lte = 3;

  // Gt specifies that this field must be greater than the specified value,
  // exclusive. If the value of Gt is larger than a specified Lt or Lte, the
  // range is reversed.
  optional sint32 gt = 4;

  // Gte specifies that this field must be greater than or equal to the
  // specified value, inclusive. If the value of Gte is larger than a
  // specified Lt or Lte, the range is reversed.
  optional sint32 gte = 5;

  // In specifies that this field must be equal to one of the specified
  // values
  repeated sint32 in = 6;

  // NotIn specifies that this field cannot be equal to one of the specified
  // values
  repeated sint32 not_in = 7;

  // IgnoreEmpty specifies that the validation rules of this field should be
  // evaluated only if the field is not empty
  optional bool ignore_empty = 8;
}

// SInt64Rules describes the constraints applied to `sint64` values
message SInt64Rules {
  // Const specifies that this field must be exactly the specified value
  optional sint64 const = 1;

  // Lt specifies that this field must be less than the specified value,
  // exclusive
  optional sint64 lt = 2;

  // Lte specifies that this field must be less than or equal to the
  // specified value, inclusive
  optional sint64 lte = 3;

  // Gt specifies that this field must be greater than the specified value,
  // exclusive. If the value of Gt is larger than a specified Lt or Lte, the
  // range is reversed.
  optional sint64 gt = 4;

  // Gte specifies that this field must be greater than or equal to the
  // specified value, inclusive. If the value of Gte is larger than a
  // specified Lt or Lte, the range is reversed.
  optional sint64 gte = 5;

  // In specifies that this field must be equal to one of the specified
  // values
  repeated sint64 in = 6;

  // NotIn specifies that this field cannot be equal to one of the specified
  // values
  repeated sint64 not_in = 7;

  // IgnoreEmpty specifies that the validation rules of this field should be
  // evaluated only if the field is not empty
  optional bool ignore_empty = 8;
}

// Fixed32Rules describes the constraints applied to `fixed32` values
message Fixed32Rules {
  optional fixed32 const = 1;
  optional fixed32 lt = 2;
  optional fixed32 lte = 3;
  optional fixed32 gt = 4;
  optional fixed32 gte = 5;
  repeated fixed32 in = 6;
  repeated fixed32 not_in = 7;
  optional bool ignore_empty = 8;
}

// Fixed64Rules describes the constraints applied to `fixed64` values
message Fixed64Rules {
  optional fixed64 const = 1;
  optional fixed64 lt = 2;
  optional fixed64 lte = 3;
  optional fixed64 gt = 4;
  optional fixed64 gte = 5;
  repeated fixed64 in = 6;
  repeated fixed64 not_in = 7;
  optional bool ignore_empty = 8;
}

// SFixed32Rules describes the constraints applied to `sfixed32` values
message SFixed32Rules {
  optional sfixed32 const = 1;
  optional sfixed32 lt = 2;
  optional sfixed32 lte = 3;
  optional sfixed32 gt = 4;
  optional sfixed32 gte = 5;
  repeated sfixed32 in = 6;
  repeated sfixed32 not_in = 7;
  optional bool ignore_empty = 8;
}

// SFixed64Rules describes the constraints applied to `sfixed64` values
message SFixed64Rules {
  optional sfixed64 const = 1;
  optional sfixed64 lt = 2;
  optional sfixed64 lte = 3;
  optional sfixed64 gt = 4;
  optional sfixed64 gte = 5;
  repeated sfixed64 in = 6;
  repeated sfixed64 not_in = 7;
  optional bool ignore_empty = 8;
}

// BoolRules describes the constraints applied to `bool` values
message BoolRules {
  optional bool const = 1;
}

// StringRules describe the constraints applied to `string` values
message StringRules {
  optional string const = 1;
  optional uint64 len = 19;
  optional uint64 min_len = 2;
  optional uint64 max_len = 3;
  optional uint64 len_bytes = 20;
  optional uint64 min_bytes = 4;
  optional uint64 max_bytes = 5;
  optional string pattern = 6;
  optional string prefix = 7;
  optional string suffix = 8;
  optional string contains = 9;
  optional string not_contains = 23;
  repeated string in = 10;
  repeated string not_in = 11;
  optional bool ignore_empty = 24;

  oneof well_known {
    bool email = 12;
    bool hostname = 13;
    bool ip = 14;
    bool ipv4 = 15;
    bool ipv6 = 16;
    bool uri = 17;
    bool uri_ref = 18;
    bool address = 21;
    bool uuid = 22;
    WellKnownRegex well_known_regex = 25;
  }

  optional bool strict = 26 [default = true];
}

// WellKnownRegex contain some well-known patterns.
enum WellKnownRegex {
  UNKNOWN = 0;
  HTTP_HEADER_NAME = 1;
  HTTP_HEADER_VALUE = 2;
}

// BytesRules describe the constraints applied to `bytes` values
message BytesRules {
  optional bytes const = 1;
  optional uint64 len = 13;
  optional uint64 min_len = 2;
  optional uint64 max_len = 3;
  optional string pattern = 4;
  optional bytes prefix = 5;
  optional bytes suffix = 6;
  optional bytes contains = 7;
  repeated bytes in = 8;
  repeated bytes not_in = 9;
  optional bool ignore_empty = 14;

  oneof well_known {
    bool ip = 10;
    bool ipv4 = 11;
    bool ipv6 = 12;
  }
}

// EnumRules describe the constraints applied to enum values
message EnumRules {
  optional int32 const = 1;
  repeated int32 in = 2;
  repeated int32 not_in = 3;
  optional bool defined_only = 4;
}

// MessageRules describe the constraints applied to embedded message values.
// For message-type fields, validation is performed recursively.
message MessageRules {
  optional bool skip = 1;
  optional bool required = 2;
}

// RepeatedRules describe the constraints applied to `repeated` values
message RepeatedRules {
  optional uint64 min_items = 1;
  optional uint64 max_items = 2;
  optional bool unique = 3;
  optional FieldRules items = 4;
  optional bool ignore_empty = 5;
}

// MapRules describe the constraints applied to `map` values
message MapRules {
  optional uint64 min_pairs = 1;
  optional uint64 max_pairs = 2;
  optional bool no_sparse = 3;
  optional FieldRules keys = 4;
  optional FieldRules values = 5;
  optional bool ignore_empty = 6;
}

// AnyRules describe constraints applied exclusively to the
// `google.protobuf.Any` well-known type
message AnyRules {
  optional bool required = 1;
  repeated string in = 2;
  repeated string not_in = 3;
}

// DurationRules describe the constraints applied exclusively to the
// `google.protobuf.Duration` well-known type
message DurationRules {
  optional bool required = 1;
  optional google.protobuf.Duration const = 2;
  optional google.protobuf.Duration lt = 3;
  optional google.protobuf.Duration lte = 4;
  optional google.protobuf.Duration gt = 5;
  optional google.protobuf.Duration gte = 6;
  repeated google.protobuf.Duration in = 7;
  repeated google.protobuf.Duration not_in = 8;
}

// TimestampRules describe the constraints applied exclusively to the
// `google.protobuf.Timestamp` well-known type
message TimestampRules {
  optional bool required = 1;
  optional google.protobuf.Timestamp const = 2;
  optional google.protobuf.Timestamp lt = 3;
  optional google.protobuf.Timestamp lte = 4;
  optional google.protobuf.Timestamp gt = 5;
  optional google.protobuf.Timestamp gte = 6;
  optional bool lt_now = 7;
  optional bool gt_now = 8;
  optional google.protobuf.Duration within = 9;
}
