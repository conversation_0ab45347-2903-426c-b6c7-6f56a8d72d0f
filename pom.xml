<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.aliyun.mpp</groupId>
    <artifactId>mps-job-analysis</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <mockito-all.version>1.10.19</mockito-all.version>
        <spring-boot.version>2.0.1.RELEASE</spring-boot.version>
        <maven-antrun.version>1.8</maven-antrun.version>
        <autoconfig-maven-plugin.version>1.2-fixcompress-SNAPSHOT</autoconfig-maven-plugin.version>
        <velocity.starter.version>1.0.2</velocity.starter.version>
        <jmockit.version>1.48</jmockit.version>
        <mybatis.starter.version>1.3.1</mybatis.starter.version>
        <jacoco.version>0.8.5</jacoco.version>
    </properties>


    <modules>
        <module>mps-job-analysis-core</module>
        <module>mps-job-analysis-controller</module>
        <module>mps-job-analysis-start</module>
    </modules>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.aliyun.mpp</groupId>
                <artifactId>mps-job-analysis-start</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.mpp</groupId>
                <artifactId>mps-job-analysis-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.mpp</groupId>
                <artifactId>mps-job-analysis-controller</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.securitysdk</groupId>
                <artifactId>rass-spring-context</artifactId>
                <version>2.9.5</version>
            </dependency>

            <dependency>
                <groupId>org.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${jmockit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.6</version>
            </dependency>


            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>


            <dependency>
                <groupId>com.aliyun.mns</groupId>
                <artifactId>aliyun-sdk-mns</artifactId>
                <version>1.1.8</version>
                <classifier>jar-with-dependencies</classifier>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>


            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.4</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.6</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.starter.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>