package com.aliyun.mpp.analysis.domain.service.impl.analysis;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.service.impl.config.MemTranscodeResourceCostConfigRepository;
import com.aliyun.mpp.analysis.domain.service.impl.config.TranscodeResourceCostConfigRepository;
import com.aliyun.mpp.analysis.domain.service.impl.config.TranscodeSpecification;
import com.aliyun.mpp.analysis.domain.types.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by lihe.lh on 2020/2/13.
 */
@Component
public class SliceTranscodeSupportAnalyzer {
    private final int DEFAULT_MIN_SLICE_INPUT_DURATION = 5 * 60;
    private final int DEFAULT_MIN_SLICE_ACTIVITY_DURATION = 2 * 60;
    private final int MAX_SLICE_COUNT = 20;
    public Integer AUDIO_SLICE_ID = 0, MERGE_SLICE_ID = -1;

    @Resource
    private TranscodeResourceCostConfigRepository transcodeResourceCostConfigRepository;

    public boolean supportSliceTranscode(JobParam jobParam, VideoInfo videoInfo) {
        if (videoInfo.getDuration() < DEFAULT_MIN_SLICE_INPUT_DURATION) {
            return false;
        }

        if (videoInfo.isAudioOnly()) {
            return false;
        }

        if (!isOutputConfigSupportSlice(jobParam)) {
            return false;
        }

        return true;
    }


    /*public JobAnalysisResult generateSliceTranscodeDAG(JobParam jobParam, VideoInfo videoInfo) {
        double duration = videoInfo.getDuration();
        int minSliceDuration = DEFAULT_MIN_SLICE_ACTIVITY_DURATION;
        int maxSliceCount = MAX_SLICE_COUNT;

        int sliceCount = Double.valueOf(Math.min(duration / minSliceDuration, maxSliceCount)).intValue();

        boolean hasAudio = videoInfo.isVideoOnly();

        if (hasAudio && jobParam.getTranscodeAudioConfig() == null) {
            hasAudio = false;
        }

        boolean isM3u8 = false;
        if (videoInfo.getFile().isM3u8()) {
            isM3u8 = true;
            hasAudio = false;
        }

        String sliceMode = "slice_v2_mp4";

        JobAnalysisResult result = new JobAnalysisResult();
        result.setCode("Success");
        result.setExecuteMode(JobAnalysisResult.EnumExecuteMode.DAG);


        Map<String, Task> taskMap = new HashMap<>();
        Map<String, List<String>> dependencies = new HashMap<>();
        JSONObject extendInfo = new JSONObject();
        extendInfo.put("hasAudio", hasAudio);
        extendInfo.put("ossTempPrefix", "parallel/output/" + jobParam.getJobId() + "/" + UUID.randomUUID());
        extendInfo.put("isM3u8", isM3u8);
        extendInfo.put("inputDur", videoInfo.getDuration());
        if (null != sliceMode) {
            extendInfo.put("sliceVer", sliceMode);
        }

        for (int i = 0; i < sliceCount; i++) {
            Task task = generateTask(jobParam, sliceCount, extendInfo, "video_" + i, i + 1, "transcode", videoInfo);
            taskMap.put(task.getName(), task);
            dependencies.put(task.getName(), new ArrayList<>());
        }

        if (hasAudio) {
            Task task = generateTask(jobParam, sliceCount, extendInfo, "audio", AUDIO_SLICE_ID, "transcode", videoInfo);
            taskMap.put(task.getName(), task);
            dependencies.put(task.getName(), new ArrayList<>());
        }


        Task mergeTask = generateTask(jobParam, sliceCount, extendInfo, "merge", MERGE_SLICE_ID, "merge", videoInfo);
        dependencies.put(mergeTask.getName(), new ArrayList<>(taskMap.keySet()));
        taskMap.put(mergeTask.getName(), mergeTask);

        return JobAnalysisResult.generateDAGModeResult(taskMap, dependencies);
    }*/

    private Task generateTask(JobParam jobParam, int sliceCount, JSONObject extendInfo,
                              String audio, int index, String type, VideoInfo videoInfo) {
        Task task = new Task();
        task.setName(audio);
        task.setTaskId(UUID.randomUUID().toString().replace("-", ""));

        JobParam taskParam = JobParam.parseParam(jobParam.getSourceEngineParams());
        setSliceTranscodeParam(taskParam, "sliceTranscode", sliceCount, index, extendInfo);
        updateParamUserData(jobParam, type, task.getTaskId());

        JSONObject newEngineParams = JSONObject.parseObject(jobParam.getSourceEngineParams());
        JSONObject params = jobParam.getParams();
        params.put("arguments", jobParam.getTranscodeArguments().toJSONString());
        params.put("userData", jobParam.getUserData());
        params.put(JobParam.PROCESS_LOGIC, jobParam.getTranscodeProcessLogic());

        newEngineParams.put("params", params);
        task.setEngineParams(newEngineParams.toJSONString());
        jobParam = JobParam.parseParam(task.getEngineParams());

        TranscodeSpecification specification = null;

        if (isVideoSlice(index)) {
            specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        }else if(index == AUDIO_SLICE_ID){
            specification = new TranscodeSpecification(EnumTranscodeCodec.H_264, EnumTranscodeType.NORMAL,
                    EnumResolution.AUDIO);
        }else {
            specification = new TranscodeSpecification(EnumTranscodeCodec.COPY, null, null);
        }

        TranscodeResourceCostConfig costConfig =
                transcodeResourceCostConfigRepository.find(specification.getResolution(),
                        specification.getTranscodeCodec(), specification.getTranscodeType());

        double fpsRatio = videoInfo.getFps() / MemTranscodeResourceCostConfigRepository.BASE_FPS;
        double localCost = Double.valueOf(videoInfo.getDuration() * fpsRatio
                / costConfig.getSpeed()).intValue();
        if(isVideoSlice(index)){
            localCost /= sliceCount;
        }
        task.setQuotaSet(costConfig.getQuotaSet());
        task.setExpectCostTime(Double.valueOf(localCost).intValue());

        return task;

    }

    private boolean isVideoSlice(int index){
        return index != AUDIO_SLICE_ID && index != MERGE_SLICE_ID;
    }

    private void setSliceTranscodeParam(JobParam jobParam, String taskType, int sliceCount, int index, JSONObject extendInfo) {
        JSONObject parallelEnv = new JSONObject();
        parallelEnv.put("taskType", taskType);
        parallelEnv.put("sliceCount", sliceCount);
        parallelEnv.put("instanceId", index);
        parallelEnv.put("hasAudio", extendInfo.getBoolean("hasAudio"));
        parallelEnv.put("ossTempPrefix", extendInfo.getString("ossTempPrefix"));
        parallelEnv.put("sliceVer", extendInfo.getString("sliceVer"));

        JSONObject argumentsArr = jobParam.getTranscodeArguments();
        argumentsArr.put("parallelEnv", parallelEnv);

        jobParam.setTranscodeArguments(argumentsArr);
        jobParam.setTranscodeProcessLogic("sliceTranscode");
    }

    private void updateParamUserData(JobParam jobParam, String activityType, String taskId) {
        JSONObject userData = jobParam.getUserData();
        userData.put("sliceActivityId", taskId);
        userData.put("type", "sliceTranscode");
        userData.put("activityType", activityType);
    }


    private boolean isOutputConfigSupportSlice(JobParam jobParam) {
        JSONObject transcodeVideoConfig = jobParam.getTranscodeVideoConfig();

        if (transcodeVideoConfig == null || transcodeVideoConfig.get("copy") != null) {
            return false;
        }

        JSONObject transFeatures = jobParam.getTranscodeFeatures();
        if (transFeatures != null) {
            for (String not_support_transcode_feature : NOT_SUPPORT_TRANSCODE_FEATURES) {
                if (transFeatures.containsKey(not_support_transcode_feature)) {
                    return false;
                }
            }
        }

        JSONObject transConfig = jobParam.getTranscodeTransConfig();
        if (transConfig != null) {
            for (String key : NOT_SUPPORT_TRANSCODE_CONFIG) {
                if (transConfig.containsKey(key)) {
                    return false;
                }
            }
            String transMode = transConfig.getString("transMode");
            if (transMode != null && !transMode.equals("onepass")) {
                return false;
            }
        }
        return true;
    }


    private Set<String> NOT_SUPPORT_TRANSCODE_FEATURES = new HashSet<String>(Arrays.asList(
            "merge",
            "digiWatermark",
            "extSubtitle",
            "watermark",
            "opening",
            "tailSlate",
            "logo",
            "text",
            "tailSlate"
    ));


    private Set<String> NOT_SUPPORT_TRANSCODE_CONFIG = new HashSet<String>(Arrays.asList(
            "seek",
            "seekInput",
            "ffmpegVersion"
    ));
}
