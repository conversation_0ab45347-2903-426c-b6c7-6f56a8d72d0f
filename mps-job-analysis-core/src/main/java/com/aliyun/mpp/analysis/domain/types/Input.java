package com.aliyun.mpp.analysis.domain.types;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * Created by lihe.lh on 2020/9/22.
 */
@Data
public class Input {
    private Integer width;
    private Integer height;
    private String videoCodec;
    private String audioCodec;
    private String format;
    private Double duration;
    private Long size;
    private Double fps;
    private Double avgFPS;
    private Double videoBitrate;
    private Double audioBitrate;
    private String resolution;

    @JSONField(serialize = false)
    public boolean hasVideo(){
        return width != null && width.intValue() > 0;
    }

}
