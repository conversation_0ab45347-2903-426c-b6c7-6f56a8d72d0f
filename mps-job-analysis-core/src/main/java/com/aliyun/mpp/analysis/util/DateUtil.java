package com.aliyun.mpp.analysis.util;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;

/**
 * Created by lihe.lh on 2020/8/4.
 */
public class DateUtil {
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static String format(Date date){
        if(date == null){
            return null;
        }else {
            return DateFormatUtils.format(date, DATE_FORMAT);
        }
    }

    public static String formatUTC8ToUTC(Date date){
        if(date == null){
            return null;
        }else {
            return DateFormatUtils.format(new Date(date.getTime() - 8 * 3600 * 1000L), DATE_FORMAT);
        }
    }

    public static void main(String[] args) {
        System.out.println(format(new Date()));
    }
}
