package com.aliyun.mpp.analysis.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.param.JobExecutionResultReportParam;
import com.aliyun.mpp.analysis.domain.service.JobAnalysisService;
import com.aliyun.mpp.analysis.domain.service.impl.analysis.TranscodeSpecificationAnalyzer;
import com.aliyun.mpp.analysis.domain.service.impl.config.NewEngineTaskQuotaEstimator;
import com.aliyun.mpp.analysis.domain.service.impl.config.OldEngineTaskQuotaEstimator;
import com.aliyun.mpp.analysis.domain.types.*;
import com.aliyun.mpp.analysis.util.DateUtil;
import com.aliyun.mpp.analysis.util.http.HttpUtil;
import com.aliyun.mpp.analysis.util.log.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

import static com.aliyun.mpp.analysis.domain.types.JobParam.SLICE_AUDIO_INDEX;


/**
 * Created by lihe.lh on 2019/12/30.
 */
@Slf4j
@Service
public class JobAnalysisServiceImpl implements JobAnalysisService {
    public static final String DEFAULT_SLA_LEVEL = "SLA_1M";
    @Resource
    private JobAnalysisUtil jobAnalysisUtil;

    @Resource
    private OldEngineTaskQuotaEstimator oldEngineTaskQuotaEstimator;
    @Resource
    private NewEngineTaskQuotaEstimator newEngineTaskQuotaEstimator;

    @Resource
    private GlobalVars globalVars;

    @Resource
    private HttpUtil httpUtil;

    private String dmesUrl = "http://dmes:/";

    private String workerBrainMpsTranscodeNewUrl = "http://mps-transcode-new/worker_brain";

    private boolean useOldEstimate = true;

    @PostConstruct
    public void init() {
        dmesUrl = globalVars.getDmesServiceUrl() + "/";
        workerBrainMpsTranscodeNewUrl = globalVars.getWorkerBrainMpsTranscodeNewUrl();

        if(globalVars.getEngineServiceServerUrl().equals("notUsed")){
            useOldEstimate = false;
        } else {
            oldEngineTaskQuotaEstimator.init();
        }
    }

    @Override
    public JobAnalysisResult analysisByParseParam(JobAnalysisParam param) {
        try {
            JobParam jobParam = JobParam.parseParam(param.getEngineParams());
            if (jobParam.isSimpleJob()) {
                return JobAnalysisResult.generateSingleModeResult(JobAnalysisUtil.QUOTA_SET_FOR_SIMPLE_TASK,
                        JobAnalysisUtil.SIMPLE_TASK_EXPECT_COST_TIME);
            }

            if (jobParam.isTranscodeMeta()) {
                return JobAnalysisResult.generateSingleModeResult(JobAnalysisUtil.QUOTA_SET_FOR_META,
                        JobAnalysisUtil.META_EXPECT_COST_TIME);
            }

            if (jobParam.isSingleFrameSnapshot()) {
                return JobAnalysisResult.generateSingleModeResult(JobAnalysisUtil.QUOTA_SET_FOR_SINGLE_SNAPSHOT,
                        JobAnalysisUtil.DEFAULT_PROCESS_COST_FOR_SINGLE_SNAPSHOT);
            }

            if (jobParam.isSliceSnapshot()) {
                return JobAnalysisResult.generateSingleModeResult(JobAnalysisUtil.QUOTA_SET_FOR_SLICE_SNAPSHOT,
                        JobAnalysisUtil.DEFAULT_PROCESS_COST_FOR_SLICE_SNAPSHOT);
            }

            if (jobParam.isLocalParallelSnapshot()) {
                return JobAnalysisResult.generateSingleModeResult(JobAnalysisUtil.QUOTA_SET_FOR_LOCAL_PARALLEL_SNAPSHOT,
                        JobAnalysisUtil.DEFAULT_PROCESS_COST_FOR_SLICE_SNAPSHOT);
            }


            if (jobParam.isTranscode()) {
                File inputVideo = jobAnalysisUtil.parseTranscodeInputFile(jobParam);
                VideoInfo inputVideoInfo = jobAnalysisUtil.getInputVideoInfoInParams(jobParam);
                if (inputVideo == null && inputVideoInfo == null) {
                    inputVideoInfo = VideoInfo.generateDefaultVideoInfo();
                }

                if ((inputVideoInfo != null)) {
                    inputVideoInfo.setFile(inputVideo);
                    JobAnalysisResult analysisResult = jobAnalysisUtil.analysisJobSingleModeExecute(param, jobParam, inputVideoInfo);
                    //todo fix transcode with mergeList/mergeConfigUrl
                    if (jobParam.isTranscodeWithMultiInputFiles()) {
                        Long disk = analysisResult.getQuotaSet().get("disk");
                        if (disk == null) {
                            disk = globalVars.getMaxDiskForMultiTranscode();
                        } else {
                            disk = disk * globalVars.getDefaultDiskTimesForMultiTranscode();
                            if (disk < globalVars.getMinDiskForMultiTranscode()) {
                                disk = globalVars.getMinDiskForMultiTranscode();
                            } else if (disk > globalVars.getMaxDiskForMultiTranscode()) {
                                disk = globalVars.getMaxDiskForMultiTranscode();
                            }
                        }
                        analysisResult.getQuotaSet().put("disk", disk);
                    }
                    return analysisResult;
                } else {
                    VideoInfo videoInfo = VideoInfo.generateDefaultVideoInfo();
                    videoInfo.setFile(inputVideo);
                    return jobAnalysisUtil.analysisJobSingleModeExecute(param, jobParam, videoInfo);
                }
            } else {
                return JobAnalysisResult.generateSingleModeResult(JobAnalysisUtil.DEFAULT_QUOTA_SET, JobAnalysisUtil.DEFAULT_COST);
            }
        } catch (Exception e) {
            log.error("fail@analysisByParseParam, param:" + JSONObject.toJSONString(param), e);
            return JobAnalysisResult.generateSingleModeResult(JobAnalysisUtil.DEFAULT_QUOTA_SET, JobAnalysisUtil.DEFAULT_COST);
        }
    }

    @Override
    public JobAnalysisResult analysisByProbeMeta(JobAnalysisParam param) {
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        File inputVideo = jobAnalysisUtil.parseTranscodeInputFile(jobParam);
        VideoInfo videoInfo = VideoInfo.generateDefaultVideoInfo();
        videoInfo.setFile(inputVideo);
        return jobAnalysisUtil.analysisJobSingleModeExecute(param, jobParam, videoInfo);
    }


    @Override
    public JobAnalysisResult estimateJobWithScheduleParams(JobAnalysisParam param) {
        if (param.getEngineModel().equals("mpp-integration-test-engine")) {
            return mockTestEngineAnalysisResult(param);
        }

        JobAnalysisResult jobAnalysisResult = new JobAnalysisResult();
        WorkerBrainAnalysisResult analysisByWorkerBrainResult = this.analysisByWorkerBrain(param);
        boolean isUseWorkerBrainResult = false;
        if (param.getEngineModel().equals("mps-transcode-new")) {
            isUseWorkerBrainResult = param.parseUseWorkBrainResultFromAnalysisParam();
        } else {
            isUseWorkerBrainResult = param.parseUseWorkBrainResultFromScheduleParams();
        }


        if (analysisByWorkerBrainResult != null && "Success".equals(analysisByWorkerBrainResult.getCode())
                && !analysisByWorkerBrainResult.isUseMachineTypeOnly()
                && (analysisByWorkerBrainResult.getResourceRequests() != null)) {
            jobAnalysisResult.setCode(analysisByWorkerBrainResult.getCode());
            jobAnalysisResult.setMessage(analysisByWorkerBrainResult.getMessage());
            jobAnalysisResult.setExecuteMode(JobAnalysisResult.EnumExecuteMode.SINGLE);
            jobAnalysisResult.setResourceRequests(analysisByWorkerBrainResult.getResourceRequests());

            if (isUseWorkerBrainResult && analysisByWorkerBrainResult != null
                    && analysisByWorkerBrainResult.getResourceRequests() !=
                    null && !analysisByWorkerBrainResult.getResourceRequests().isEmpty()) {
                for (Map.Entry<String, TaskResourceRequest> entry : analysisByWorkerBrainResult.getResourceRequests().entrySet()) {
                    jobAnalysisResult.setQuotaSet(new HashMap<>(entry.getValue().getQuotaSet()));
                    jobAnalysisResult.setExpectCostTime(entry.getValue().getExpectCostTime());
                    break;
                }

                if ("mps-editing".equals(param.getEngineModel())) {
                    jobAnalysisResult.setMaxMigrateRetry(3);
                }

                return jobAnalysisResult;
            }
        }

        //如果调用workerbrain返回dag任务采用dag
        if (analysisByWorkerBrainResult != null && "Success".equals(analysisByWorkerBrainResult.getCode())
                && WorkerBrainAnalysisResult.EnumExecuteMode.DAG.equals(analysisByWorkerBrainResult.getExecuteMode())
                && analysisByWorkerBrainResult.getGraphMap() != null
                && CollectionUtils.isEmpty(param.getScheduleParams().getSpeedXRange())
                && param.isSliceProcess()) {
            try {
                //返回结果
                jobAnalysisResult.setCode(analysisByWorkerBrainResult.getCode());
                jobAnalysisResult.setMessage(analysisByWorkerBrainResult.getMessage());
                jobAnalysisResult.setExecuteMode(JobAnalysisResult.EnumExecuteMode.DAG);
                DagJobGraph firstDagJobGraph = analysisByWorkerBrainResult.getGraphMap().values().stream().findFirst().orElse(null);
                jobAnalysisResult.setGraph(firstDagJobGraph);
                //判断是否分片
                int parallelNumFromScheduleParams = getParallelNumFromScheduleParams(param);
                int sliceNum = 1;
                if (parallelNumFromScheduleParams <= 1) {
                    boolean canSlice = canSlice(param);
                    if (canSlice) {
                        sliceNum = calculateSliceNum(param);
                    }
                } else {
                    sliceNum = parallelNumFromScheduleParams;
                }
                GenerateSliceTranscodeDAGParam dagParam = buildDagParam(param, sliceNum);
                //dag任务资源预估
                return getJobAnalysisResultByGraph(param, dagParam, jobAnalysisResult);
            } catch (Exception e) {
                log.error("fail@workerbrainDag@estimateJobWithScheduleParams, param:" + JSONObject.toJSONString(param), e);
                return constructDefaultAnalysisResultForMpsTranscodeNew();
            }
        }

        SLSLogInfo slsLogInfo = new SLSLogInfo();
        slsLogInfo.setEvent("estimateByScheduleParams");
        slsLogInfo.setUserId(param.getUserId());
        slsLogInfo.setJobId(param.getJobId());
        slsLogInfo.setTrace(param.getTrace());
        ScheduleParams scheduleParams = param.getScheduleParams();


        //转码新引擎，只使用workerBrain返回的资源类型
        if(isUseWorkerBrainResult &&
                analysisByWorkerBrainResult != null  && "Success".equals(analysisByWorkerBrainResult.getCode())
                && analysisByWorkerBrainResult.isUseMachineTypeOnly()
                && analysisByWorkerBrainResult.getResourceRequests()!=null
                && !analysisByWorkerBrainResult.getResourceRequests().isEmpty()){
            Map<String, TaskResourceRequest> resourceRequestMap = analysisByWorkerBrainResult.getResourceRequests();
            if(resourceRequestMap.containsKey("arm") && !resourceRequestMap.containsKey("cpu")){
                resourceRequestMap.put("cpu", new TaskResourceRequest());
            }

            try {
                for (Map.Entry<String, TaskResourceRequest> entry : resourceRequestMap.entrySet()) {
                    ScheduleParam scheduleParam = new ScheduleParam();
                    scheduleParam.setConfig(scheduleParams.getConfigs().get(0));
                    scheduleParam.setInput(scheduleParams.getInputs().get(0));
                    scheduleParam.getConfig().setArch(entry.getKey());

                    EstimateResult estimateResult = this.estimateJob(param, param.getJobId(), param.getJobId(),
                            scheduleParam, (JSONObject) JSONObject.toJSON(scheduleParams), generateCustomParam(param));
                    entry.getValue().setQuotaSet(new HashMap<>(estimateResult.getQuotaSet()));
                    entry.getValue().setExpectCostTime(estimateResult.getExpectCostTime());

                    jobAnalysisResult.setExecuteMode(JobAnalysisResult.EnumExecuteMode.SINGLE);
                    jobAnalysisResult.setMigrateDiscardQuotaThreshold(estimateResult.getMigrateDiscardQuotaThreshold());
                    jobAnalysisResult.setMaxMigrateRetry(estimateResult.getMaxMigrateRetry());
                    if (!entry.getKey().equals("arm")) {
                        jobAnalysisResult.setQuotaSet(new HashMap<>(estimateResult.getQuotaSet()));
                        jobAnalysisResult.setExpectCostTime(estimateResult.getExpectCostTime());
                    }

                }
                jobAnalysisResult.setResourceRequests(resourceRequestMap);

                SLSLogUtil.info(slsLogInfo);
                return jobAnalysisResult;
            } catch (Exception e) {
                log.info("fail@estimateJob, param:" + JSONObject.toJSONString(param), e);
                if (scheduleParams.getConfigs() != null && !scheduleParams.getConfigs().isEmpty()) {
                    scheduleParams.getConfigs().get(0).setArch(null);
                }
            }
        }

        //以下针对workerBrain失败，或者后续调用失败，退避成旧逻辑
        try {
            if (param.getEngineModel().equals("mps-transcode-new")) {
                try {
                    return analysisMpsTranscodeNewScheduleParams(param);
                } catch (Exception e) {
                    log.info("fail@estimateJobWithScheduleParams, param:" + JSONObject.toJSONString(param), e);
                    return constructDefaultAnalysisResultForMpsTranscodeNew();
                }
            }

            Input input = null;
            if (scheduleParams.getInputs() != null && !scheduleParams.getInputs().isEmpty()) {
                input = scheduleParams.getInputs().get(0);
            }

            CustomParam customParam = generateCustomParam(param);

            for (Config config1 : scheduleParams.getConfigs()) {
                ScheduleParam scheduleParam = new ScheduleParam();
                scheduleParam.setInput(input);
                scheduleParam.setConfig(config1);

                EstimateResult estimateResult = this.estimateJob(param, param.getJobId(), param.getJobId(),
                        scheduleParam, param.getScheduleParamsByJson(), customParam);
                if (config1.getParallelNum() != null && config1.getParallelNum() > 1 && (config1.getVideoCodec() != null && !OldEngineTaskQuotaEstimator.COPY.equals(config1.getVideoCodec()))) {
                    estimateResult.setExpectCostTime(estimateResult.getExpectCostTime() / config1.getParallelNum());
                    Long disk = estimateResult.getQuotaSet().get("disk");
                    if (disk != null) {
                        estimateResult.getQuotaSet().put("disk", disk / config1.getParallelNum());
                    }
                } else if (config1.getParallelNum() != null && config1.getParallelNum() > 1 && config1.getJobType().equals("transcode") && config1.getVideoCodec() == null) {
                    //音频分片转码，disk quota大小最大为10 GB
                    Long disk = estimateResult.getQuotaSet().get("disk");
                    if (disk != null) {
                        estimateResult.getQuotaSet().put("disk", Math.min(10 * 1000L, disk));
                    }
                }
                slsLogInfo.setResult(JSONObject.toJSONString(estimateResult));
                JobAnalysisResult result = JobAnalysisResult.generateSingleModeResult(estimateResult.getQuotaSet(), estimateResult.getExpectCostTime());
                result.setMigrateDiscardQuotaThreshold(estimateResult.getMigrateDiscardQuotaThreshold());
                result.setMaxMigrateRetry(estimateResult.getMaxMigrateRetry());
                return result;
            }
            throw new RuntimeException("fail@estimateJobWithScheduleParams, invalid inputs or configs");
        } catch (Exception e) {
            slsLogInfo.setCode(e.getClass().getName());
            slsLogInfo.setMessage(e.getMessage());
            log.info("fail@estimateJobWithScheduleParams", e);
            throw new RuntimeException(e);
        } finally {
            SLSLogUtil.info(slsLogInfo);
        }
    }

    private CustomParam generateCustomParam(JobAnalysisParam param) {
        CustomParam customParam = new CustomParam();
        customParam.setUserId(param.getUserId());
        customParam.setPipelineId(param.getPipelineId());
        customParam.setTag(param.getTag());
        return customParam;
    }

    @Override
    public SlaAnalysisResult analysisJobSla(JobAnalysisParam param) {
        if (globalVars.getSlaServiceUrl().equals("notUsed")) {
            return SlaAnalysisResult.NOT_SUPPORT_RESULT;
        }
        long begin = System.currentTimeMillis();

        AnalysisJobSlaParam analysisJobSlaParam = new AnalysisJobSlaParam();
        analysisJobSlaParam.setEngineModel(param.getEngineModel());
        analysisJobSlaParam.setJobId(param.getJobId());
        if (param.getScheduleParams().getSlaLevel() == null) {
            param.getScheduleParams().setSlaLevel(DEFAULT_SLA_LEVEL);
        }
        analysisJobSlaParam.setScheduleParams(param.getScheduleParams());
        analysisJobSlaParam.setTag(param.getTag());
        analysisJobSlaParam.setUserId(param.getUserId());
        analysisJobSlaParam.setTaskId(param.getJobId());
        analysisJobSlaParam.setTrace(param.getTrace());

        String url = globalVars.getSlaServiceUrl() + "/job/sla";
        SLSLogInfo slsLogInfo = new SLSLogInfo();
        slsLogInfo.setEngineModel(param.getEngineModel());
        slsLogInfo.setEvent("sla");
        slsLogInfo.setFunction(url);
        slsLogInfo.setUserId(param.getUserId());
        slsLogInfo.setJobId(param.getJobId());
        slsLogInfo.setParam(JSONObject.toJSONString(analysisJobSlaParam));

        Exception exception = null;
        SlaAnalysisResult analysisResult = null;
        try {
            String result = httpUtil.sendPostRequest(url, param);
//            String result = "{\"code\":\"Success\",\"slaTime\":10000}";
            analysisResult = JSONObject.parseObject(result, SlaAnalysisResult.class);
            slsLogInfo.setCode(analysisResult.getCode());
            slsLogInfo.setResult(result);
        } catch (Exception e) {
            exception = e;
            slsLogInfo.setCode(e.getClass().getName());
            slsLogInfo.setMessage(e.getMessage());
        } finally {
            slsLogInfo.setCostTime(System.currentTimeMillis() - begin);
            if (exception == null) {
                SLSLogUtil.info(slsLogInfo);
            } else {
                SLSLogUtil.warn(slsLogInfo);
            }
        }
        return analysisResult;

    }

    @Override
    public WorkerBrainAnalysisResult analysisByWorkerBrain(JobAnalysisParam param) {
        if(!param.isInvokeWorkerBrain()){
            return null;
        }
        if (!"mps-editing".equals(param.getEngineModel()) && !"mps-transcode-new".equals(param.getEngineModel())) {
            return null;
        }

        if("mps-editing".equals(param.getEngineModel()) && StringUtils.isBlank(globalVars.getWorkerBrainMpsEditingUrl())){
            return null;
        }

        if ("mps-transcode-new".equals(param.getEngineModel()) && StringUtils.isBlank(globalVars.getWorkerBrainMpsTranscodeNewUrl())) {
            return null;
        }

        if("mps-transcode-new".equals(param.getEngineModel()) && isParallelTranscode(param)){
            return null;
        }

        long begin = System.currentTimeMillis();

        WorkerBrainAnalysisJobParam analysisJobSlaParam = new WorkerBrainAnalysisJobParam();
        analysisJobSlaParam.setEngineModel(param.getEngineModel());
        analysisJobSlaParam.setJobId(param.getJobId());
        analysisJobSlaParam.setEngineParams(param.getEngineParams());
        analysisJobSlaParam.setScheduleParams((JSONObject) JSONObject.toJSON(param.getScheduleParams()));
        analysisJobSlaParam.setUserId(param.getUserId());
        analysisJobSlaParam.setTrace(param.getTrace());

        String url = null;
        if(param.getEngineModel().equals("mps-editing")){
            url = globalVars.getWorkerBrainMpsEditingUrl();
        } else {
            url = globalVars.getWorkerBrainMpsTranscodeNewUrl();
        }
        SLSLogInfo slsLogInfo = new SLSLogInfo();
        slsLogInfo.setEngineModel(param.getEngineModel());
        slsLogInfo.setEvent("worker_brain");
        slsLogInfo.setFunction(url);
        slsLogInfo.setUserId(param.getUserId());
        slsLogInfo.setJobId(param.getJobId());
        slsLogInfo.setParam(JSONObject.toJSONString(analysisJobSlaParam));

        Exception exception = null;
        WorkerBrainAnalysisResult analysisResult = null;
        try {
            String result = httpUtil.sendPostRequest(url, analysisJobSlaParam);
            analysisResult = JSONObject.parseObject(result, WorkerBrainAnalysisResult.class);
            slsLogInfo.setCode(analysisResult.getCode());
            slsLogInfo.setResult(result);

            log.info("info@analysisByWorkerBrain, analysisResult:" + JSONObject.toJSONString(analysisResult) + ", jobId:"
                    + param.getJobId());
            return analysisResult;
        } catch (Exception e) {
            exception = e;
            slsLogInfo.setCode(e.getClass().getName());
            slsLogInfo.setMessage(e.getMessage());
        } finally {
            slsLogInfo.setCostTime(System.currentTimeMillis() - begin);
            if (exception == null) {
                SLSLogUtil.info(slsLogInfo);
            } else {
                SLSLogUtil.warn(slsLogInfo);
            }
        }
        return null;
    }

    private boolean isParallelTranscode(JobAnalysisParam param){
        try {
            int parallelNumFromScheduleParams = getParallelNumFromScheduleParams(param);
            if(parallelNumFromScheduleParams > 1){
                return true;
            }
            if(!CollectionUtils.isEmpty(param.getScheduleParams().getSpeedXRange())){
                return true;
            }
            return canSlice(param);
        }catch (Exception e){
            log.error("fail@isParallelTranscode, param:" + JSONObject.toJSONString(param), e);
            return false;
        }
    }

    private JobAnalysisResult analysisMpsTranscodeNewSingleTask(JobAnalysisParam param,JobAnalysisResult jobAnalysisResult) {
        JobAnalysisResult result = null;
        if (isOldNvencCodecScheduleParams(param.getScheduleParams())) {
            result = analysisNvencCodec(param.getScheduleParams());
        } else if (isNewNvencCodecScheduleParams(param.getScheduleParams())) {
            result = analysisNvencCodec(param.getScheduleParams());
        } else {
            result = analysisByScheduleParamsForMpsTranscodeNew(param);
        }
        if(!CollectionUtils.isEmpty(param.getScheduleParams().getSpeedXRange())){
            result.setSpeedXMessage(jobAnalysisResult.getSpeedXMessage());
        }
        return result;
    }

    private JobAnalysisResult analysisByScheduleParamsForMpsTranscodeNew(JobAnalysisParam param) {
        ScheduleParams scheduleParams = param.getScheduleParams();
        ScheduleParam scheduleParam = new ScheduleParam();
        scheduleParam.setConfig(scheduleParams.getConfigs().get(0));
        if (scheduleParams.getInputs() != null && !scheduleParams.getInputs().isEmpty()) {
            scheduleParam.setInput(scheduleParams.getInputs().get(0));
        }
        if (!StringUtils.isBlank(scheduleParam.getConfig().getUhd()) &&
                (StringUtils.isBlank(scheduleParam.getConfig().getSubJobType()) || "Normal".equals(scheduleParam.getConfig().getSubJobType()))) {
            scheduleParam.getConfig().setUhd(null);
            param.getScheduleParamsByJson().getJSONArray("configs").getJSONObject(0).remove("uhd");
        }
        CustomParam customParam = generateCustomParam(param);
        EstimateResult estimateResult = this.estimateJob(param, param.getJobId(), param.getJobId(),
                scheduleParam, param.getScheduleParamsByJson(), customParam);
        JobAnalysisResult result = new JobAnalysisResult();
        result.setExecuteMode(JobAnalysisResult.EnumExecuteMode.SINGLE);
        result.setQuotaSet(estimateResult.getQuotaSet());
        result.setExpectCostTime(estimateResult.getExpectCostTime());
        result.setMigrateDiscardQuotaThreshold(estimateResult.getMigrateDiscardQuotaThreshold());
        result.setMaxMigrateRetry(estimateResult.getMaxMigrateRetry());

        return result;
    }

    private boolean isOldNvencCodecScheduleParams(ScheduleParams scheduleParams) {
        List<Config> configs = scheduleParams.getConfigs();
        if (configs == null || configs.isEmpty()) {
            return false;
        }

        String oldNvencCodec = "H.264_NVENC";
        for (Config config : configs) {
            if (oldNvencCodec.equals(config.getVideoCodec())) {
                return true;
            }
        }

        return false;
    }

    private JobAnalysisResult analysisNvencCodec(ScheduleParams scheduleParams) {
        Map<String, Long> quotaSet = new HashMap<>();
        int num = scheduleParams.getConfigs().size();
        if (num == 1) {
            quotaSet.put("gpu", 100000L);
        } else {
            quotaSet.put("gpu", 330000L);
        }

        Input input = scheduleParams.getInputs().get(0);
        Long fileSize = input.getSize();
        quotaSet.put("disk", (fileSize >> 20) * (num + 1));
        Double duration = input.getDuration();

        JobAnalysisResult jobAnalysisResult = new JobAnalysisResult();
        jobAnalysisResult.setQuotaSet(quotaSet);
        jobAnalysisResult.setExpectCostTime(Double.valueOf(duration / 10).longValue());

        jobAnalysisResult.setExecuteMode(JobAnalysisResult.EnumExecuteMode.SINGLE);
        return jobAnalysisResult;
    }

    private boolean isNewNvencCodecScheduleParams(ScheduleParams scheduleParams) {
        List<Config> configs = scheduleParams.getConfigs();
        if (configs == null || configs.isEmpty()) {
            return false;
        }

        String oldNvencCodec = "h264_nvenc";
        for (Config config : configs) {
            if (oldNvencCodec.equals(config.getVideoCodec())) {
                return true;
            }
        }

        return false;
    }


    private JobAnalysisResult constructDefaultAnalysisResultForMpsTranscodeNew() {
        JobAnalysisResult result = new JobAnalysisResult();
        result.setExecuteMode(JobAnalysisResult.EnumExecuteMode.SINGLE);
        result.setQuotaSet(Collections.singletonMap("cpu", 8000L));
        result.setExpectCostTime(1800L);
        return result;
    }

    private JobAnalysisResult mockTestEngineAnalysisResult(JobAnalysisParam param) {
        CustomParam customParam = generateCustomParam(param);
        if (param.isSliceProcess()) {
            try {
                JSONObject engineParams = JSONObject.parseObject(param.getEngineParams());
                if (engineParams.get("jobGraph") != null) {
                    DagJobGraph dagjobGraph = engineParams.getObject("jobGraph", DagJobGraph.class);
                    if (dagjobGraph != null) {
                        for (DagJobVertex jobVertex : dagjobGraph.getJobVertexs()) {
                            if (jobVertex.getResourceRequest() == null) {
                                ScheduleParams scheduleParams = jobVertex.getScheduleParams();
                                ScheduleParam scheduleParam = new ScheduleParam();
                                scheduleParam.setInput(scheduleParams.getInputs().get(0));
                                scheduleParam.setConfig(scheduleParams.getConfigs().get(0));

                                EstimateResult estimateResult = this.estimateJob(param, param.getJobId(), param.getJobId() + "_" + jobVertex.getName(),
                                        scheduleParam, param.getScheduleParamsByJson(), customParam);
                                TaskResourceRequest resourceRequest = new TaskResourceRequest();
                                resourceRequest.setQuotaSet(estimateResult.getQuotaSet());
                                resourceRequest.setExpectCostTime(estimateResult.getExpectCostTime());
                                jobVertex.setResourceRequest(resourceRequest);
                            }
                        }
                    }
                    JobAnalysisResult result = new JobAnalysisResult();
                    result.setGraph(dagjobGraph);
                    result.setExecuteMode(JobAnalysisResult.EnumExecuteMode.DAG);
                    return result;
                }
            } catch (Exception e) {
                log.error("fail@mockTestEngineAnalysisResult", e);
            }
        }
        JobAnalysisResult result = new JobAnalysisResult();
        result.setExecuteMode(JobAnalysisResult.EnumExecuteMode.SINGLE);
        result.setExpectCostTime(10L);
        result.setQuotaSet(Collections.singletonMap("cpu", 100L));
        return result;
    }

    private JobAnalysisResult analysisMpsTranscodeNewScheduleParams(JobAnalysisParam param) {
        int parallelNumFromScheduleParams = getParallelNumFromScheduleParams(param);
        int sliceNum = 1;
        boolean canSlice;
        if (parallelNumFromScheduleParams <= 1) {
            canSlice = canSlice(param);
            if (canSlice) {
                sliceNum = calculateSliceNum(param);
            }
        } else {
            canSlice = true;
            sliceNum = parallelNumFromScheduleParams;
        }

        if (sliceNum > 1 || (sliceNum == 1 && canSlice && mustSlice(param)) || !CollectionUtils.isEmpty(param.getScheduleParams().getSpeedXRange())) {
            //调用dmes
            GenerateSliceTranscodeDAGParam dagParam = buildDagParam(param,sliceNum);

            SLSLogInfo slsLogInfo = new SLSLogInfo();
            slsLogInfo.setEngineModel(param.getEngineModel());
            slsLogInfo.setEvent("dmes");
            if(CollectionUtils.isEmpty(param.getScheduleParams().getSpeedXRange())){
                slsLogInfo.setFunction(dmesUrl);
            }else {
                slsLogInfo.setFunction(workerBrainMpsTranscodeNewUrl);
            }
            slsLogInfo.setUserId(param.getUserId());
            slsLogInfo.setJobId(param.getJobId());
            slsLogInfo.getParams().put("sliceNum", dagParam.getSliceNum());
            slsLogInfo.setParam(JSONObject.toJSONString(dagParam));
            slsLogInfo.setTrace(dagParam.getTrace());
            if(CollectionUtils.isEmpty(param.getScheduleParams().getSpeedXRange())){
                slsLogInfo.setSpeedXRange(JSONObject.toJSONString(param.getScheduleParams().getSpeedXRange()));
            }
            slsLogInfo.setIsAutoSpeedX(param.isAutoSpeedX());

            Exception exception = null;
            long begin = System.currentTimeMillis();

            JobAnalysisResult jobAnalysisResult = null;
            try {
                String result = null;
                if(CollectionUtils.isEmpty(param.getScheduleParams().getSpeedXRange())){
                     result = httpUtil.sendPostRequest(dmesUrl, dagParam);
                }else {
                    result = httpUtil.sendPostRequest(workerBrainMpsTranscodeNewUrl, dagParam);
                }
                jobAnalysisResult = JSONObject.parseObject(result, JobAnalysisResult.class);
                slsLogInfo.setCode(jobAnalysisResult.getCode());
                slsLogInfo.setResult(result);
            } catch (Exception e) {
                exception = e;
                slsLogInfo.setCode(e.getClass().getName());
                slsLogInfo.setMessage(e.getMessage());
            } finally {
                slsLogInfo.setCostTime(System.currentTimeMillis() - begin);
                if (exception == null) {
                    SLSLogUtil.info(slsLogInfo);
                } else {
                    SLSLogUtil.warn(slsLogInfo);
                }
            }

            //普通DAG 旧版DAG 返回结果中不包含excuteMode, 需要根据graph判断
            if (jobAnalysisResult != null && jobAnalysisResult.getGraph() != null) {
                return getJobAnalysisResultByGraph(param,dagParam,jobAnalysisResult);
            }
            //倍速转码DAG处理 新版DAG 返回结果中包含excuteMode graphMap
            if (jobAnalysisResult != null && jobAnalysisResult.getGraphMap() != null && JobAnalysisResult.EnumExecuteMode.DAG.equals(jobAnalysisResult.getExecuteMode())) {
                // 支持多资源类型DAG
                String speedX = getMultiResourcetypeResult(param, jobAnalysisResult);
                if (null != speedX) {
                    if (JobAnalysisResult.EnumExecuteMode.SINGLE.toString().equals(speedX)) {
                        return analysisMpsTranscodeNewSingleTask(param,jobAnalysisResult);
                    } else {
                        log.info("job:{} speedX to normal dag. key:{} graph:{}", param.getJobId(), speedX, jobAnalysisResult.getGraphMap().get(speedX));
                        jobAnalysisResult.setGraph(jobAnalysisResult.getGraphMap().get(speedX));
                        jobAnalysisResult.setGraphMap(null);
                        jobAnalysisResult.setSpeedXMessage(null);
                        return getJobAnalysisResultByGraph(param, dagParam, jobAnalysisResult);
                    }
                } else {
                    return getJobAnalysisResultByGraphMap(param, dagParam, jobAnalysisResult);
                }
            }
            return analysisMpsTranscodeNewSingleTask(param,jobAnalysisResult);
        } else {
            //使用调度参数预估，并兜底
            return analysisMpsTranscodeNewSingleTask(param,new JobAnalysisResult());
        }
    }

    private String getMultiResourcetypeResult(JobAnalysisParam param, JobAnalysisResult jobAnalysisResult) {
        try {
            if (null != jobAnalysisResult.getResourceRequests() && jobAnalysisResult.getResourceRequests().containsKey("v100")) {
                if (null != jobAnalysisResult.getSpeedXMessage()) {
                    for (Map.Entry<String, Message> entry : jobAnalysisResult.getSpeedXMessage().entrySet()) {
                        if (entry.getValue().getCode() == 0) {
                            return entry.getKey();
                        }
                    }

                    return JobAnalysisResult.EnumExecuteMode.SINGLE.toString();
                } else {
                    return JobAnalysisResult.EnumExecuteMode.SINGLE.toString();
                }
            }
        } catch (Exception e) {
            log.warn("fail@getMultiResourcetypeResult, param:" + JSONObject.toJSONString(param) + ", result:" +
                    JSONObject.toJSONString(jobAnalysisResult), e);
        }

        return null;
    }

    private GenerateSliceTranscodeDAGParam buildDagParam(JobAnalysisParam param,int sliceNum){
        GenerateSliceTranscodeDAGParam dagParam = new GenerateSliceTranscodeDAGParam();
        dagParam.setEngineModel(param.getEngineModel());
        dagParam.setEngineParams(param.getEngineParams());
        dagParam.setJobId(param.getJobId());
        dagParam.setRequestId(UUID.randomUUID().toString());
        dagParam.setUserId(param.getUserId());
        dagParam.setSliceNum(sliceNum);
        dagParam.setScheduleParams(param.getScheduleParams());
        dagParam.setTrace(param.getTrace());
        dagParam.setSpeedXRange(param.getScheduleParams().getSpeedXRange());
        dagParam.setAutoSpeedX(param.isAutoSpeedX());
        return dagParam;
    }

    //普通DAG处理
    private JobAnalysisResult getJobAnalysisResultByGraph(JobAnalysisParam param,GenerateSliceTranscodeDAGParam dagParam,
                                                          JobAnalysisResult jobAnalysisResult){
        jobAnalysisResult.setExecuteMode(JobAnalysisResult.EnumExecuteMode.DAG);
        analysisDAGTaskResourceRequest(param, jobAnalysisResult.getGraph());

        if (isUhdTranscode(param.getScheduleParams(), param.getJobId())) {
            refineUhdTranscodeDisk(jobAnalysisResult.getGraph(), param.getJobId(), dagParam.getSliceNum());
        }
        return jobAnalysisResult;
    }

    private void analysisDAGTaskResourceRequest(JobAnalysisParam param, DagJobGraph graph) {
        // 检查 graph 和 jobVertexs 是否为空
//        if (graph == null || graph.getJobVertexs() == null) {
//            log.warn("graph or jobVertexs is null, skipping analysis.");
//            return;
//        }
        for (DagJobVertex jobVertex : graph.getJobVertexs()) {
            if (jobVertex.getResourceRequest() == null) {
                ScheduleParams scheduleParams = jobVertex.getScheduleParams();
                ScheduleParam scheduleParam = new ScheduleParam();
                scheduleParam.setConfig(scheduleParams.getConfigs().get(0));
                scheduleParam.setInput(scheduleParams.getInputs().get(0));
                try {
                    EstimateResult estimateResult = this.estimateJob(param, param.getJobId(), param.getJobId() + "_" + jobVertex.getName(),
                            scheduleParam, (JSONObject) JSONObject.toJSON(scheduleParams), generateCustomParam(param));
                    jobVertex.setResourceRequest(new TaskResourceRequest(estimateResult.getQuotaSet(), estimateResult.getExpectCostTime()));
                    jobVertex.setMaxMigrateRetry(estimateResult.getMaxMigrateRetry());
                    jobVertex.setMigrateDiscardQuotaThreshold(estimateResult.getMigrateDiscardQuotaThreshold());
                } catch (Exception e) {
                    log.info("fail@estimateJob, param:" + JSONObject.toJSONString(scheduleParam), e);
                    throw new RuntimeException(e);
                }
            }
            if (jobVertex.getEngineModel() == null) {
                jobVertex.setEngineModel(param.getEngineModel());
            }
        }
    }

    //倍速转码DAG处理
    private JobAnalysisResult getJobAnalysisResultByGraphMap(JobAnalysisParam param,GenerateSliceTranscodeDAGParam dagParam,
                                                             JobAnalysisResult jobAnalysisResult){
        jobAnalysisResult.setExecuteMode(JobAnalysisResult.EnumExecuteMode.DAG);
        Map<String,DagJobGraph> graphMap = jobAnalysisResult.getGraphMap();
        //todo 空指针修复 遍历map,如果某些key中的节点的结果为空，则从map中删除该key
        Iterator<Map.Entry<String, DagJobGraph>> iterator = graphMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, DagJobGraph> entry = iterator.next();
            DagJobGraph graph = entry.getValue();
            if (graph != null && graph.getJobVertexs() == null) {
                log.warn("info@getJobAnalysisResultByGraphMap Graph is null for key: " + entry.getKey() + ", removing from graphMap,jobId:"+param.getJobId()+" ,JobAnalysisResult: " + jobAnalysisResult);
                iterator.remove();
            }
        }
        graphMap.forEach((speedX,graph)->{
            analysisDAGTaskResourceRequest(param, graph);

            if (isUhdTranscode(param.getScheduleParams(), param.getJobId())) {
                refineUhdTranscodeDisk(graph, param.getJobId(), dagParam.getSliceNum());
            }
        });
        return jobAnalysisResult;
    }

    private EstimateResult estimateJob(JobAnalysisParam param, String jobId, String taskId, ScheduleParam scheduleParam, JSONObject scheduleParamsByJson, CustomParam customParam) {
        EstimateResult newResult = null;
        try {
            newResult = newEngineTaskQuotaEstimator.analysis(scheduleParamsByJson, customParam);
        } catch (Exception e){
            log.error("fail@estimateByNew, scheduleParam:" + JSONObject.toJSONString(scheduleParam), e);
            SLSLogInfo slsLogInfo = new SLSLogInfo();
            slsLogInfo.setEngineModel(param.getEngineModel());
            slsLogInfo.setEvent("estimateByNew");
            slsLogInfo.setTaskId(taskId);
            slsLogInfo.setJobId(jobId);
            slsLogInfo.setCode(e.getClass().getName());
            slsLogInfo.setMessage(e.getMessage());
            slsLogInfo.setUserId(param.getUserId());
            if(param.getScheduleParams() != null) {
                slsLogInfo.setPipelineId(param.getScheduleParams().getPipelineId());
            }
            slsLogInfo.setParam(JSONObject.toJSONString(scheduleParam));
            SLSLogUtil.error(slsLogInfo);
        }

        if(!useOldEstimate){
            return newResult;
        }


        EstimateResult oldResult = null;
        try {
            oldResult = oldEngineTaskQuotaEstimator.estimateJob(scheduleParam);
        }catch (Exception e){
            if (newResult == null) {
                //only log warn if both estimate by new and old are failed
                SLSLogInfo slsLogInfo = new SLSLogInfo();
                slsLogInfo.setEvent("estimateByOld");
                slsLogInfo.setTaskId(taskId);
                slsLogInfo.setJobId(jobId);
                slsLogInfo.setCode(e.getClass().getName());
                slsLogInfo.setMessage(e.getMessage());
                slsLogInfo.setParam(JSONObject.toJSONString(scheduleParam));
                SLSLogUtil.error(slsLogInfo);
            }
        }

        SLSLogInfo slsLogInfo = new SLSLogInfo();
        slsLogInfo.setEvent("compareEstimate");
        slsLogInfo.getParams().put("scheduleParams", scheduleParam);
        slsLogInfo.getParams().put("old", oldResult);
        slsLogInfo.getParams().put("new", newResult);
        slsLogInfo.setJobId(jobId);
        slsLogInfo.setTaskId(taskId);
        SLSLogUtil.info(slsLogInfo);

        if(newResult != null){
            return newResult;
        } else {
            return oldResult;
        }
    }

    private int getParallelNumFromScheduleParams(JobAnalysisParam analysisParam) {
        if (!analysisParam.isSliceProcess()) {
            return 0;
        }
        ScheduleParams scheduleParams = analysisParam.getScheduleParams();
        if(scheduleParams != null && scheduleParams.getSliceNum() != null){
            return scheduleParams.getSliceNum();
        }
        List<Config> configs = scheduleParams.getConfigs();
        if (configs == null || configs.isEmpty()) {
            return 0;
        }
        Config config = scheduleParams.getConfigs().get(0);
        return config.getParallelNum() != null ? config.getParallelNum() : 0;
    }


    private Integer calculateSliceNum(JobAnalysisParam param) {
        Input input = param.getScheduleParams().getInputs().get(0);
        int maxSliceNum = param.getMaxSliceNum() == null ? 10 : param.getMaxSliceNum();
        int minSliceDuration = param.getMinSliceDuration() == null ? 60 : param.getMinSliceDuration();

        int sliceNum = Math.min(maxSliceNum, Double.valueOf(input.getDuration() / minSliceDuration).intValue());
        return sliceNum > 0 ? sliceNum : 1;
    }

    private void refineUhdTranscodeDisk(DagJobGraph graph, String jobId, int sliceNum) {
        try {
            for (DagJobVertex jobVertex : graph.getJobVertexs()) {
                Config config = jobVertex.getScheduleParams().getConfigs().get(0);
                Input input = jobVertex.getScheduleParams().getInputs().get(0);
                Long size = input.getSize() >> 20;
                if(size == null || size == 0){
                    continue;
                }
                long yuvSize = calSourceYuvSize(input);
                long outputFileSize = calOutputSize(input, config);
                Map<String, Long> quotaSet = new HashMap<>(jobVertex.getResourceRequest().getQuotaSet());
                if (isDecode(config)) {
                    quotaSet.put("disk", size * 2);
                } else if (isUhdTask(config) || isFrcTask(config) || isHdrTask(config)) {
                    quotaSet.put("disk", yuvSize / sliceNum);
                } else if (isMergeUhdTask(config)) {
                    quotaSet.put("disk", outputFileSize * 2);
                } else {
                    quotaSet.put("disk", 2 * outputFileSize / sliceNum);
                }
                jobVertex.getResourceRequest().setQuotaSet(quotaSet);
            }
        } catch (Exception e) {
            log.info("refineUhdTranscodeDisk,jobId:" + jobId, e);
        }
    }

    private boolean isDecode(Config config) {
        return config.getJobType().equals("decode");
    }

    private boolean isUhdTask(Config config) {
        return !StringUtils.isEmpty(config.getUhd());
    }

    private boolean isHdrTask(Config config) {
        return !StringUtils.isEmpty(config.getHdr());
    }

    private boolean isFrcTask(Config config) {
        return !StringUtils.isEmpty(config.getFrc());
    }

    private boolean isMergeUhdTask(Config config) {
        return "copy".equals(config.getVideoCodec());
    }

    private Long calSourceYuvSize(Input input) {
        Double fps = input.getAvgFPS() != null ? input.getAvgFPS() : input.getFps();
        return Double.valueOf(1.0 * input.getWidth() * input.getHeight() * 3 * input.getDuration() * fps).longValue() >> 20;
    }

    private Long calOutputSize(Input input, Config config) {
        //outputBitrate bps, 20Mbps 作为默认
        long outputBitrate = 20L << 20;
        if (config != null && config.getVideoBitrate() != null) {
            outputBitrate = Double.valueOf(config.getVideoBitrate()).longValue() << 10;
        }
        //outputFileSize MB
        long outputFileSize = Double.valueOf(1.0 * outputBitrate / 8 * input.getDuration()).longValue() >> 20;
        if (input != null && input.getSize() != null){
            //guess output file size max 2 size larger than input, input size use byte
            outputFileSize = Math.min((input.getSize() >> 20) * 2, outputFileSize);
        }
        return outputFileSize;
    }

    private boolean canSlice(JobAnalysisParam param) {
        if (!param.isSliceProcess()) {
            return false;
        }

        if (param.getMaxSliceNum() != null && param.getMaxSliceNum() < 1) {
            return false;
        }

        ScheduleParams scheduleParams = param.getScheduleParams();
        if (isUhdTranscode(scheduleParams, param.getJobId())) {
            return true;
        }

        return false;
    }

    private boolean mustSlice(JobAnalysisParam param) {
        try {
            ScheduleParams scheduleParams = param.getScheduleParams();
            Config config = scheduleParams.getConfigs().get(0);

            if (StringUtils.isBlank(config.getSubJobType()) || "Normal".equals(config.getSubJobType())) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("fail@mustSlice", e);
            return false;
        }
    }

    private boolean isUhdTranscode(ScheduleParams scheduleParams, String jobId) {
        try {
            if (scheduleParams.getConfigs() != null && !scheduleParams.getConfigs().isEmpty()) {
                Config config = scheduleParams.getConfigs().get(0);
                if (!StringUtils.isEmpty(config.getUhd())) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.info("fail@isUHDJob, jobId:" + jobId, e);
        }
        return false;
    }

    @Override
    public void reportResult(JobExecutionResultReportParam param) {
        collectJobScheduleInfo(param);
    }

    @Override
    public ParseScheduleParamsResult parseJobScheduleParams(JobAnalysisResult result, JobAnalysisParam param) {
        try {
            if (result.getExecuteMode() == JobAnalysisResult.EnumExecuteMode.PROBE_FIRST) {
                return null;
            }

            ParseScheduleParamsResult parseScheduleParamsResult = parseScheduleParams(param.getEngineParams());


            if (parseScheduleParamsResult.getScheduleParams().getConfigs() != null || parseScheduleParamsResult.getScheduleParams().getInputs() != null) {
                result.setScheduleParams(parseScheduleParamsResult.getScheduleParams());
            }

            JobResultCollectInfo jobResultCollectInfo = new JobResultCollectInfo();
            jobResultCollectInfo.setRegion(globalVars.getRegion());
            jobResultCollectInfo.setEngineModel(param.getEngineModel());
            jobResultCollectInfo.setJobId(param.getJobId());
            jobResultCollectInfo.setExpectCostTime(result.getExpectCostTime());
            Map<String, Long> quotaSet = new HashMap<>();
            if (result.getQuotaSet() != null) {
                for (Map.Entry<String, Long> entry : result.getQuotaSet().entrySet()) {
                    quotaSet.put(entry.getKey(), Long.valueOf(entry.getValue()));
                }
            }
            jobResultCollectInfo.setAllocQuotaSet(quotaSet);
            jobResultCollectInfo.setUserId(param.getUserId());
            jobResultCollectInfo.setPipelineId(param.getPipelineId());
            jobResultCollectInfo.setTag(param.getTag());
            jobResultCollectInfo.setCreateTime(param.getCreateTime());


            jobResultCollectInfo.setInputs(parseScheduleParamsResult.getScheduleParamsByJson().getJSONArray("inputs"));
            jobResultCollectInfo.setConfigs(parseScheduleParamsResult.getScheduleParamsByJson().getJSONArray("configs"));

            jobResultCollectInfo.setEvent("addJob");
            JobResultCollectLogUtil.log(jobResultCollectInfo);

            return parseScheduleParamsResult;
        } catch (Exception e) {
            log.error("fail@parseJobScheduleParams", e);
            return null;
        }
    }

    private ParseScheduleParamsResult parseScheduleParams(String engineParams) {
        JobParam jobParam = JobParam.parseParam(engineParams);
        ScheduleParams scheduleParams = new ScheduleParams();
        List<Input> inputs = parseJobInputInfo(jobParam);
        scheduleParams.setInputs(inputs);

        List<Config> configs = parseJobConfigInfo(jobParam);
        scheduleParams.setConfigs(configs);

        ParseScheduleParamsResult result = new ParseScheduleParamsResult();

        JSONObject scheduleParamsByJson = (JSONObject) (JSONObject.toJSON(scheduleParams));
        if (jobParam.getScheduleParams() != null) {
            JSONArray jsonArray = jobParam.getScheduleParams().getJSONArray("configs");
            if (jsonArray != null) {
                JSONObject config = jsonArray.getJSONObject(0);
                if (scheduleParams.getConfigs() != null && scheduleParams.getConfigs().size() > 0) {
                    JSONObject source = scheduleParamsByJson.getJSONArray("configs").getJSONObject(0);
                    for (Map.Entry<String, Object> entry : config.entrySet()) {
                        source.put(entry.getKey(), entry.getValue());
                    }
                    scheduleParamsByJson.getJSONArray("configs").set(0, source);
                }

            }
        }
        result.setScheduleParamsByJson(scheduleParamsByJson);
        result.setScheduleParams(scheduleParams);
        return result;
    }

    private void collectJobScheduleInfo(JobExecutionResultReportParam param) {
        try {
            JobResultCollectInfo jobResultCollectInfo = new JobResultCollectInfo();
            jobResultCollectInfo.setRegion(globalVars.getRegion());
            jobResultCollectInfo.setCreateTime(param.getCreateTime());
            jobResultCollectInfo.setSubmitTime(param.getSubmitTime());
            jobResultCollectInfo.setEngineModel(param.getEngineModel());
            jobResultCollectInfo.setJobId(param.getJobId());
            jobResultCollectInfo.setExpectCostTime(param.getExpectCostTime());
            jobResultCollectInfo.setAllocQuotaSet(param.getAllocQuotaSet());
            jobResultCollectInfo.setRealCostTime(param.getRealCostTime());
            jobResultCollectInfo.setAvgQuotaSet(param.getAvgQuotaSet());
            jobResultCollectInfo.setMaxQuotaSet(param.getMaxQuotaSet());
            jobResultCollectInfo.setTag(param.getTag());
            jobResultCollectInfo.setUserId(param.getUserId());
            jobResultCollectInfo.setPipelineId(param.getPipelineId());
            jobResultCollectInfo.setEnv(param.getEnv());
            jobResultCollectInfo.setStation(param.getStation());

            JSONObject result = (JSONObject) JSON.toJSON(param.getResultData());

            JSONObject outputParam = result.getJSONObject("output_param");
            JSONObject resultParam = outputParam.getJSONObject("param");

            jobResultCollectInfo.setStartTime(DateUtil.formatUTC8ToUTC(result.getDate("start_time")));
            jobResultCollectInfo.setFinishTime(DateUtil.formatUTC8ToUTC(result.getDate("finish_time")));


            JobParam jobParam = JobParam.parseParam(param.getEngineParams());

            ParseScheduleParamsResult parseScheduleParamsResult = parseScheduleParams(param.getEngineParams());
            jobResultCollectInfo.setInputs(parseScheduleParamsResult.getScheduleParamsByJson().getJSONArray("inputs"));
            jobResultCollectInfo.setConfigs(parseScheduleParamsResult.getScheduleParamsByJson().getJSONArray("configs"));

            parseJobOutputInfo(jobResultCollectInfo, jobParam, resultParam);

            jobResultCollectInfo.setEvent("finishJob");
            JobResultCollectLogUtil.log(jobResultCollectInfo);
        } catch (Exception e) {
            SLSLogInfo slsLogInfo = new SLSLogInfo();
            slsLogInfo.setEvent("parseJobResult");
            slsLogInfo.setCode(e.getClass().getName());
            slsLogInfo.setMessage(e.getMessage());
            slsLogInfo.setJobId(param.getJobId());
            slsLogInfo.setUserId(param.getUserId());
            slsLogInfo.setParam(JSONObject.toJSONString(param));
            SLSLogUtil.info(slsLogInfo);
            log.error("fail@collectJobScheduleInfo, param:" + JSONObject.toJSONString(param), e);
        }
    }


    private List<Input> parseJobInputInfo(JobParam jobParam) {
        try {
            VideoInfo inputVideoInfo = jobAnalysisUtil.getInputVideoInfoInParams(jobParam);
            if (inputVideoInfo == null) {
                inputVideoInfo = VideoInfo.generateDefaultVideoInfo();
            }
            Input input = new Input();
            input.setWidth(inputVideoInfo.getWidth());
            input.setHeight(inputVideoInfo.getHeight());
            input.setVideoCodec(inputVideoInfo.getVideoCodecName());
            input.setAudioCodec(inputVideoInfo.getAudioCodecName());
            input.setFormat(inputVideoInfo.getFormatName());
            input.setDuration(inputVideoInfo.getDuration());
            input.setSize(inputVideoInfo.getFileSize());
            input.setFps(inputVideoInfo.getFps());
            input.setVideoBitrate(inputVideoInfo.getVideoBitrate());
            input.setAudioBitrate(inputVideoInfo.getAudioBitrate());

            EnumResolution resolution = EnumResolution.parseResoByLongShortEgde(input.getWidth(), input.getHeight());
            input.setResolution(resolution.name());
            return Collections.singletonList(input);
        } catch (Exception e) {
            log.error("fail@parseJobInputInfo", e);
        }
        return null;
    }

    private List<Config> parseJobConfigInfo(JobParam jobParam) {
        try {
            Config config = new Config();
            VideoInfo inputVideoInfo = jobAnalysisUtil.getInputVideoInfoInParams(jobParam);
            if (inputVideoInfo == null) {
                inputVideoInfo = VideoInfo.generateDefaultVideoInfo();
            }
            config.setId(jobParam.getJobId());

            parseJobType(jobParam, config);

            if (jobParam.isCustomTranscode()) {
                parseCustomTranscodeConfig(jobParam, config, inputVideoInfo);
            } else if (jobParam.isSystemDynamicTranscode()) {
                parseDynamicTranscodeConfig(jobParam, config);
            } else if (jobParam.isSliceTranscode()) {
                parseSliceTranscodeConfig(jobParam, config, inputVideoInfo);
            } else if (jobParam.isTranscodeSnapshot()) {
                parseAsyncSnapshotConfig(jobParam, config);
            }
            if (jobParam.isTranscodeWithMultiInputFiles()) {
                config.setWithMultiFiles(true);
            }
            return Collections.singletonList(config);
        } catch (Exception e) {
            log.error("fail@parseJobConfigInfo", e);
        }
        return null;
    }

    private void parseCustomTranscodeConfig(JobParam jobParam, Config config, VideoInfo inputVideoInfo) {
        JSONObject videoConfig = jobParam.getTranscodeVideoConfig();
        JSONObject audioConfig = jobParam.getTranscodeAudioConfig();
        JSONObject transConfig = jobParam.getTranscodeTransConfig();
        JSONObject container = jobParam.getContainer();
        if (videoConfig != null) {
            if (videoConfig.get("params") != null) {
                String params = videoConfig.getString("params");
                if (params.indexOf("sharp=") > 0) {
                    config.setNhVersion("1.5");
                }
                for (String s : params.split(":")) {
                    String str[] = s.split("=");
                    config.addExtend("video_params_" + str[0], str[1]);
                }
            }

            config.setVideoBitrate(videoConfig.getDouble("bitrate"));
            config.setVideoCodec(videoConfig.getString("codec"));
            config.setFps(videoConfig.getDouble("fps"));

            if (videoConfig.containsKey("DnCNNP4")) {
                config.setNhVersion("2.0");
            }
            if (videoConfig.containsKey("DnCNN")) {
                config.setNhVersion("2.0");
            }
            if (videoConfig.containsKey("RCANP4")) {
                config.setNhVersion("RCANP4");
            }
            if (videoConfig.containsKey("UHDV1")) {
                config.setNhVersion("UHDV1");
            }

            EnumTranscodeCodec codec = TranscodeSpecificationAnalyzer.parseTranscodeCodec(jobParam, inputVideoInfo);
            if (codec == EnumTranscodeCodec.AV1) {
                config.setVideoCodec(OldEngineTaskQuotaEstimator.AOM_AV_1);
            } else if (codec == EnumTranscodeCodec.COPY) {
                config.setVideoCodec(OldEngineTaskQuotaEstimator.COPY);
                config.setNhVersion(null);
            } else if (codec == EnumTranscodeCodec.NVENC) {
                config.setVideoCodec(OldEngineTaskQuotaEstimator.H_264_NVENC);
            } else if (codec == EnumTranscodeCodec.H_265) {
                config.setVideoCodec(OldEngineTaskQuotaEstimator.X_265);
            } else if (codec == EnumTranscodeCodec.H_264) {
                String videoCodec = videoConfig.getString("codec");
                if ("S.265".equals(videoCodec)) {
                    config.setVideoCodec(OldEngineTaskQuotaEstimator.S_265);
                    config.setNhVersion("1.6");
                } else {
                    config.setVideoCodec(OldEngineTaskQuotaEstimator.X_264);
                }
            }

            for (String videoInterestKey : VIDEO_INTEREST_KEYS) {
                if (videoConfig.containsKey(videoInterestKey)) {
                    config.addExtend("video_" + videoInterestKey, videoConfig.get(videoInterestKey));
                }
            }
            config.setMediaType("video");
        } else {
            config.setMediaType("audio");
        }

        parseJobConfigReso(config, inputVideoInfo, jobParam);
        EnumResolution resolution = EnumResolution.parseResoByLongShortEgde(config.getWidth(), config.getHeight());
        config.setResolution(resolution.name());

        if (transConfig != null) {
            String filterExtend = transConfig.getString("filterExtend");
            if (filterExtend != null) {
                for (String s : filterExtend.split(",")) {
                    String str[] = s.split("=");
                    String key = str[0];
                    if (str.length > 1) {
                        String value = s.substring(key.length() + 1).replace("=", "_");
                        config.addExtend("video_fileter_" + key, value);
                    }
                }
            }

            for (String transconfigInterestKey : TRANSCONFIG_INTEREST_KEYS) {
                config.addExtend("transConfig_" + transconfigInterestKey, transConfig.get(transconfigInterestKey));
            }
        }

        if (audioConfig != null) {
            config.setAudioBitrate(audioConfig.getDouble("bitrate"));
            config.setAudioCodec(audioConfig.getString("codec"));
        }

        if (container != null && container.getString("format") != null) {
            config.setFormat(container.getString("format"));
        } else {
            config.setFormat("mp4");
        }
    }

    private void parseDynamicTranscodeConfig(JobParam jobParam, Config config) {
        String processLogic = jobParam.getTranscodeProcessLogic();
        Integer width = null;
        Integer height = null;
        if (processLogic.endsWith("xld")) {
            width = 480;
            height = 270;
        } else if (processLogic.endsWith("ld")) {
            width = 640;
            height = 360;
        } else if (processLogic.endsWith("sd")) {
            width = 848;
            height = 480;
        } else if (processLogic.endsWith("hd")) {
            width = 1280;
            height = 720;
        } else if (processLogic.endsWith("ud")) {
            width = 1920;
            height = 1080;
        }

        config.setHeight(height);
        config.setWidth(width);
        config.setVideoCodec(OldEngineTaskQuotaEstimator.X_264);

        EnumResolution resolution = EnumResolution.parseResoByLongShortEgde(config.getWidth(), config.getHeight());
        config.setResolution(resolution.name());

        if (processLogic.startsWith("flv")) {
            config.setFormat("flv");
        } else if (processLogic.startsWith("mp4")) {
            config.setFormat("mp4");
        } else if (processLogic.startsWith("m3u8")) {
            config.setFormat("hls");
        }
    }

    private void parseSliceTranscodeConfig(JobParam jobParam, Config config, VideoInfo inputVideoInfo) {
        JSONObject parallelEnv = jobParam.getTranscodeArguments().getJSONObject("parallelEnv");
        int sliceNum = parallelEnv.getInteger("sliceCount");
        String type = parallelEnv.getString("taskType");

        config.setParallelNum(sliceNum);
        if (type.equals("merge")) {
            config.setVideoCodec("copy");
        } else {
            int sliceIndex = parallelEnv.getInteger("instanceId");
            if (sliceIndex == SLICE_AUDIO_INDEX) {
                config.setVideoCodec(null);
            }
        }

        parseCustomTranscodeConfig(jobParam, config, inputVideoInfo);

    }

    private void parseAsyncSnapshotConfig(JobParam jobParam, Config config) {
        if (jobParam.isSingleFrameSnapshot()) {
            config.setSnapshotType("single");
        } else {
            config.setSnapshotType("multi");
        }
    }

    private void parseJobType(JobParam jobParam, Config config) {
        if (jobParam.isTranscodeMeta()) {
            config.setJobType(OldEngineTaskQuotaEstimator.META);
        } else if (jobParam.isTranscodeSnapshot()) {
            config.setJobType(OldEngineTaskQuotaEstimator.SNAPSHOT);
        } else if (jobParam.isConvertSubtitle()) {
            config.setJobType(OldEngineTaskQuotaEstimator.CONVERT_SUBTITLE);
        } else if (jobParam.isPackage()) {
            config.setJobType(OldEngineTaskQuotaEstimator.PACKAGE);
        } else if (jobParam.isTranscodeAnalysis()) {
            config.setJobType(OldEngineTaskQuotaEstimator.ANALYSIS);
        } else if (jobParam.isEditing()) {
            config.setJobType(OldEngineTaskQuotaEstimator.EDITING);
        } else {
            config.setJobType(OldEngineTaskQuotaEstimator.TRANSCODE);
        }

    }


    private void parseJobConfigReso(Config config, VideoInfo inputVideoInfo, JobParam jobParam) {
        Pair<Integer, Integer> reso = TranscodeSpecificationAnalyzer.parseTranscodeReso(jobParam, inputVideoInfo);
        config.setWidth(reso.getLeft());
        config.setHeight(reso.getRight());
    }

    private void parseJobOutputInfo(JobResultCollectInfo jobResultCollectInfo, JobParam jobParam, JSONObject resultParam) {
        try {
            JSONObject userData = jobParam.getUserData();
            String type = userData.getString("type");

            int errorCode = resultParam.getIntValue("error_code");
            JobResultCollectInfo.Output output = new JobResultCollectInfo.Output();
            float processTime = 1.0f * resultParam.getIntValue("transcode_time") / 1000;
            float totalTime = 1.0f * resultParam.getIntValue("total_time") / 1000;
            float uploadTime = 1.0f * resultParam.getIntValue("upload_time") / 1000;
            float downloadTime = totalTime - processTime - uploadTime;
            output.setId(jobParam.getJobId());
            jobResultCollectInfo.setTotalTime(totalTime);
            if (type.equals("transcode")) {
                if (errorCode == 0) {
                    JSONObject outputVideoInfo = resultParam.getJSONObject("output_video_info");
                    JSONObject format = outputVideoInfo.getJSONObject("format");


                    output.setWidth(outputVideoInfo.getInteger("width"));
                    output.setHeight(outputVideoInfo.getInteger("height"));
                    output.setVideoCodec(outputVideoInfo.getString("video_codec_name"));
                    output.setAudioCodec(outputVideoInfo.getString("audio_codec_name"));
                    output.setFormat(outputVideoInfo.getString("format_name"));
                    output.setDuration(format.getFloat("duration"));
                    output.setFps(outputVideoInfo.getFloat("fps"));
                    output.setVideoBitrate(outputVideoInfo.getFloat("video_bitrate"));
                    output.setAudioBitrate(outputVideoInfo.getFloat("audio_bitrate"));
                    output.setSize(outputVideoInfo.getLong("length"));

                    if (output.getWidth() == null && output.getHeight() == null) {
                        output.setMediaType("audio");
                    } else {
                        output.setMediaType("video");
                    }
                    EnumResolution resolution = EnumResolution.parseResoByLongShortEgde(output.getWidth(), output.getHeight());
                    output.setResolution(resolution.name());
                }
            }

            output.setStatus(errorCode == 0 ? "success" : "failed");
            if (output.getDuration() != null && processTime > 0.0f) {
                output.setProcessTime(processTime);
                output.setTotalTime(totalTime);
                output.setDownloadTime(downloadTime);
                output.setUploadTime(uploadTime);
                output.setProcessSpeed(1.0f * output.getDuration() / processTime);
                output.setTotalSpeed(1.0f * output.getDuration() / totalTime);
            }
            jobResultCollectInfo.addOutput(output);

        } catch (Exception e) {
            log.error("fail@parseJobOutputConfig", e);
        }
    }

    private Set<String> VIDEO_INTEREST_KEYS = new HashSet<String>(Arrays.asList(
            "preset",
            "maxrate",
            "crf",
            "gop",
            "scanMode"
    ));

    private Set<String> TRANSCONFIG_INTEREST_KEYS = new HashSet<String>(Arrays.asList(
            "transMode",
            "threads"
    ));


    @Data
    class GenerateSliceTranscodeDAGParam {
        private String jobId;
        private String engineModel;
        private String engineParams;
        private String userId;
        private Integer sliceNum = 2;
        private String requestId;
        private ScheduleParams scheduleParams;
        private JSONObject trace;
        private List<String> speedXRange;
        private Boolean isAutoSpeedX;

        public String getJobId() {
            return jobId;
        }

        public void setJobId(String jobId) {
            this.jobId = jobId;
        }

        public String getEngineModel() {
            return engineModel;
        }

        public void setEngineModel(String engineModel) {
            this.engineModel = engineModel;
        }

        public String getEngineParams() {
            return engineParams;
        }

        public void setEngineParams(String engineParams) {
            this.engineParams = engineParams;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }

        public Integer getSliceNum() {
            return sliceNum;
        }

        public void setSliceNum(Integer sliceNum) {
            this.sliceNum = sliceNum;
        }

        public ScheduleParams getScheduleParams() {
            return scheduleParams;
        }

        public void setScheduleParams(ScheduleParams scheduleParams) {
            this.scheduleParams = scheduleParams;
        }

        public JSONObject getTrace() {
            return trace;
        }

        public void setTrace(JSONObject trace) {
            this.trace = trace;
        }

        public List<String> getSpeedXRange() {
            return speedXRange;
        }

        public void setSpeedXRange(List<String> speedXRange) {
            this.speedXRange = speedXRange;
        }

        public Boolean getAutoSpeedX() {
            return isAutoSpeedX;
        }

        public void setAutoSpeedX(Boolean autoSpeedX) {
            isAutoSpeedX = autoSpeedX;
        }
    }

    @Data
    class AnalysisJobSlaParam {
        private String jobId;
        private String taskId;
        private ScheduleParams scheduleParams;
        private String engineModel;
        private String userId;
        private JSONObject trace;
        private String tag;
    }


    @Data
    class WorkerBrainAnalysisJobParam {
        private String jobId;
        private JSONObject trace;
        private String engineModel;
        private String engineParams;
        private String userId;
        private int sliceNum;
        private JSONObject scheduleParams;
        private String requestId;
        private String product;
    }
}
