package com.aliyun.mpp.analysis.util.log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Data
@Slf4j(topic = "trace-log")
public class SLSLogUtil {
    public static void info(SLSLogInfo logInfo) {
        try {
            logInfo.setLogLevel("info");

            log.info(getLogContent(logInfo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void warn(SLSLogInfo logInfo) {
        try {
            logInfo.setLogLevel("warn");

            log.info(getLogContent(logInfo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void error(SLSLogInfo logInfo) {
        try {
            logInfo.setLogLevel("error");
            log.info(getLogContent(logInfo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String getLogContent(SLSLogInfo logInfo){
        logInfo.initCurrentTime();
        JSONObject jsonObject = (JSONObject) JSON.toJSON(logInfo);
        for (Map.Entry<String, Object> entry : logInfo.getParams().entrySet()) {
            jsonObject.put(entry.getKey(), entry.getValue());
        }
        String jsonStr = JSON.toJSONString(jsonObject, SerializerFeature.DisableCircularReferenceDetect);

        return jsonStr;
    }

    public static void debug(SLSLogInfo info){
        try {
            log.debug(JSON.toJSONString(info));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

