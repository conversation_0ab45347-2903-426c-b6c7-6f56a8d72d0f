package com.aliyun.mpp.analysis.infrastructure.repository;

import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface EngineQuotaRefineWithInputConfigMapper {
    @Results({
            @Result(column = "config_name", property = "configName"),
            @Result(column = "input_name", property = "inputName"),
            @Result(column = "inputs", property = "inputs"),
            @Result(column = "cost", property = "cost"),
            @Result(column = "speed", property = "speed"),
            @Result(column = "quota_set", property = "quotaSet"),
    })
    @Select("select * from mps_engine_quota_refine_with_input_config")
    List<EngineQuotaRefineWithInputConfigDO> loadAllConfigs();

}
