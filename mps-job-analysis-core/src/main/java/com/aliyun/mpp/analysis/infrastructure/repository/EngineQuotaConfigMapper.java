package com.aliyun.mpp.analysis.infrastructure.repository;

import com.aliyun.mpp.analysis.domain.service.impl.config.EngineQuotaConfig;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface EngineQuotaConfigMapper {
    @Results({
            @Result(column = "name", property = "name"),
            @Result(column = "configs", property = "configs"),
            @Result(column = "quota_set", property = "quotaSet"),
            @Result(column = "cost", property = "cost"),
            @Result(column = "speed", property = "speed"),
            @Result(column = "disk_ratio", property = "diskRatio"),
            @Result(column = "disk_quota", property = "diskQuota"),
            @Result(column = "custom_param", property = "customParam"),
            @Result(column = "max_migrate_retry", property = "maxMigrateRetry"),
            @Result(column = "migrate_discard_quota_threshold", property = "migrateDiscardQuotaThreshold"),
    })
    @Select("select * from mps_engine_quota_config")
    List<EngineQuotaConfigDO> loadAllConfigs();

    @Results({
            @Result(column = "name", property = "name"),
            @Result(column = "configs", property = "configs"),
            @Result(column = "quotaSet", property = "quota_set"),
            @Result(column = "cost", property = "cost"),
            @Result(column = "speed", property = "speed"),
            @Result(column = "disk_ratio", property = "diskRatio"),
            @Result(column = "disk_quota", property = "diskQuota"),
            @Result(column = "custom_param", property = "customParam"),
            @Result(column = "max_migrate_retry", property = "maxMigrateRetry"),
            @Result(column = "migrate_discard_quota_threshold", property = "migrateDiscardQuotaThreshold"),
    })
    @Select("select * from mps_engine_quota_config where name = #{name}")
    EngineQuotaConfigDO queryByName(@Param("name") String name);

    @Insert("insert into mps_engine_quota_config(gmt_create, gmt_modified, name, configs, " +
            "quota_set, cost, speed, disk_ratio, disk_quota, custom_param) " +
            "values(now(), now(), #{name}, #{configs}, #{quotaSet}, #{cost}, " +
            "#{speed}, #{diskRatio}, #{diskQuota}, #{customParam})")
    void create(EngineQuotaConfigDO configDO);


    @Update("update mps_engine_quota_config set configs = #{configs}, quota_set = #{quotaSet}, " +
            "cost = #{cost}, speed = #{speed}, disk_ratio = #{diskRatio}, " +
            "disk_quota = #{diskQuota}, custom_param = #{customParam}, " +
            " max_migrate_retry = #{maxMigrateRetry}, migrate_discard_quota_threshold = #{migrateDiscardQuotaThreshold}" +
            " where name = #{name})")
    void update(EngineQuotaConfigDO configDO);

}