package com.aliyun.mpp.analysis.domain.service.impl.config;

import com.aliyun.mpp.analysis.domain.types.EnumResolution;
import com.aliyun.mpp.analysis.domain.types.EnumTranscodeCodec;
import com.aliyun.mpp.analysis.domain.types.EnumTranscodeType;
import com.aliyun.mpp.analysis.domain.types.TranscodeResourceCostConfig;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * Created by lihe.lh on 2020/2/11.
 */
@Repository
public class MemTranscodeResourceCostConfigRepository implements TranscodeResourceCostConfigRepository {
    private Map<String, TranscodeResourceCostConfig> transcodeResourceCostConfigMap = new HashMap<>();
    private final int BASE_CPU_CORE_NUM = 16;
    private final int BASE_CUDA = 1000000;
    public static final int BASE_FPS = 30;


    //https://yuque.antfin-inc.com/livevideo/ksimrd/yykofn
    //2.0时只有一组(不考虑codec）
    //265编码 nv1 和 普通成本一致， 265没有音频
    @PostConstruct
    public void initConfigs(){
        addNormalTranscodeConfigs();
        addNvencTranscodeConfigs();
        addNarrowBandV1Transconfigs();
        addNarrowBandV2Transconfig();
    }

    private void addNormalTranscodeConfigs(){
        addNormalH264TranscodeConfigs();
        addNormalAV1TranscodeConfigs();
        addNormalH265TranscodeConfigs();
        addTranscodeCopyConfigs();
    }

    private void addNvencTranscodeConfigs(){
        EnumTranscodeType transcodeType = EnumTranscodeType.NORMAL;
        EnumTranscodeCodec codec = EnumTranscodeCodec.NVENC;
        float cpuDecodeCost = 1600f;
        int gpuEncode = 100;
        float nvencCudaResource = 100000;
        addConfig(EnumResolution._4K, codec, transcodeType,
                0.857f, cpuDecodeCost, 5907, nvencCudaResource, 87, gpuEncode);
        addConfig(EnumResolution._2K, codec, transcodeType,
                0.188f, cpuDecodeCost, 3152, nvencCudaResource, 81, gpuEncode);
        addConfig(EnumResolution.FHD, codec, transcodeType,
                0.341f, cpuDecodeCost, 2260, nvencCudaResource, 75, gpuEncode);
        addConfig(EnumResolution.HD, codec, transcodeType,
                0.772f, cpuDecodeCost, 1636, nvencCudaResource, 65, gpuEncode);
        addConfig(EnumResolution.SD, codec, transcodeType,
                1.659f, cpuDecodeCost, 1313, nvencCudaResource, 49, gpuEncode);
        addConfig(EnumResolution.LD, codec, transcodeType,
                2.650f, cpuDecodeCost, 1313, nvencCudaResource, 49, gpuEncode);

    }


    private void addNormalH264TranscodeConfigs() {
        EnumTranscodeType transcodeType = EnumTranscodeType.NORMAL;
        addConfig(EnumResolution._4K, EnumTranscodeCodec.H_264, transcodeType,
                0.712f, 13715f, 3529);
        addConfig(EnumResolution._2K, EnumTranscodeCodec.H_264, transcodeType,
                2.27f, 12672f, 1680);
        addConfig(EnumResolution.FHD, EnumTranscodeCodec.H_264, transcodeType,
                2.832f, 12832f, 1033);
        addConfig(EnumResolution.HD, EnumTranscodeCodec.H_264, transcodeType,
                4.796f, 10524f, 535);
        addConfig(EnumResolution.SD, EnumTranscodeCodec.H_264, transcodeType,
                6.509f, 9372f, 300);
        addConfig(EnumResolution.LD, EnumTranscodeCodec.H_264, transcodeType,
                8.11f, 5793f, 150);
        addConfig(EnumResolution.AUDIO, EnumTranscodeCodec.H_264, transcodeType,
                30f, 1000f, 100);
    }

    private void addNormalAV1TranscodeConfigs() {
        EnumTranscodeType transcodeType = EnumTranscodeType.NORMAL;
        addConfig(EnumResolution._4K, EnumTranscodeCodec.AV1, transcodeType,
                0.066f, 2000f, 3529);
        addConfig(EnumResolution._2K, EnumTranscodeCodec.AV1, transcodeType,
                0.133f, 1800f, 1680);
        addConfig(EnumResolution.FHD, EnumTranscodeCodec.AV1, transcodeType,
                0.200f, 1600f, 1033);
        addConfig(EnumResolution.HD, EnumTranscodeCodec.AV1, transcodeType,
                0.266f, 1400f, 535);
        addConfig(EnumResolution.SD, EnumTranscodeCodec.AV1, transcodeType,
                0.5333f, 1200f, 300);
        addConfig(EnumResolution.LD, EnumTranscodeCodec.AV1, transcodeType,
                1.067f, 1000f, 150);
        addConfig(EnumResolution.AUDIO, EnumTranscodeCodec.AV1, transcodeType,
                30f, 1000f, 100);
    }

    public static void main(String[] args) {
//        List<Float> values = Arrays.asList(2.57f, 5.64f, 10.22f, 23.16f, 49.78f, 79.52f);
//        List<Float> values = Arrays.asList(34.49f, 37.12f, 34.96f, 38.34f, 39.99f, 39.56f);
        List<Float> values = Arrays.asList(2f, 4f, 6f, 8f, 16f, 32f);
        for (Float value : values) {
//            System.out.println(Double.valueOf(16 * value * 10).intValue());
//            System.out.println(Double.valueOf(100000 * value / 10).intValue());
            System.out.println(Double.valueOf(value/30).doubleValue());
        }

    }

    private void addNormalH265TranscodeConfigs() {
        EnumTranscodeType transcodeType = EnumTranscodeType.NORMAL;
        addConfig(EnumResolution._4K, EnumTranscodeCodec.H_265, transcodeType,
                0.199f, 12001f, 2107);
        addConfig(EnumResolution._2K, EnumTranscodeCodec.H_265, transcodeType,
                0.463f, 11688f, 1088);
        addConfig(EnumResolution.FHD, EnumTranscodeCodec.H_265, transcodeType,
                0.965f, 11052f, 707);
        addConfig(EnumResolution.HD, EnumTranscodeCodec.H_265, transcodeType,
                1.988f, 9532f, 440);
        addConfig(EnumResolution.SD, EnumTranscodeCodec.H_265, transcodeType,
                2.673f, 7288f, 282);
        addConfig(EnumResolution.LD, EnumTranscodeCodec.H_265, transcodeType,
                3.733f, 5176f, 282);

    }

    private void addTranscodeCopyConfigs() {
        addConfig(null, EnumTranscodeCodec.COPY, null,
                80f, 1000f, 100);
    }


    private void addNarrowBandV1Transconfigs(){
        EnumTranscodeType transcodeType = EnumTranscodeType.NH_V1;
        addConfig(EnumResolution._4K, EnumTranscodeCodec.H_264, transcodeType,
                0.216f, 15192f, 5358);
        addConfig(EnumResolution._2K, EnumTranscodeCodec.H_264, transcodeType,
                0.867f, 13873f, 2510);
        addConfig(EnumResolution.FHD, EnumTranscodeCodec.H_264, transcodeType,
                1.063f, 13259f, 2510);
        addConfig(EnumResolution.HD, EnumTranscodeCodec.H_264, transcodeType,
                2.467f, 9531f, 734);
        addConfig(EnumResolution.SD, EnumTranscodeCodec.H_264, transcodeType,
                4.40f, 8168f, 317);
        addConfig(EnumResolution.LD, EnumTranscodeCodec.H_264, transcodeType,
                6.00f, 6862f, 200);
    }




    private void addNarrowBandV2Transconfig(){
        EnumTranscodeType transcodeType = EnumTranscodeType.NH_V2;
        addConfig(EnumResolution._4K, EnumTranscodeCodec.H_264, transcodeType,
                0.086f, BASE_CPU_CORE_NUM * 34.49f * 10, 5907, BASE_CUDA * 96.33f / 100, 87, null);
        addConfig(EnumResolution._2K, EnumTranscodeCodec.H_264, transcodeType,
                0.188f, BASE_CPU_CORE_NUM * 37.12f * 10, 3152, BASE_CUDA * 96.03f / 100, 81, null);
        addConfig(EnumResolution.FHD, EnumTranscodeCodec.H_264, transcodeType,
                0.341f, BASE_CPU_CORE_NUM * 34.96f * 10, 2260, BASE_CUDA * 95.08f / 100, 75, null);
        addConfig(EnumResolution.HD, EnumTranscodeCodec.H_264, transcodeType,
                0.772f, BASE_CPU_CORE_NUM * 38.34f * 10, 1636, BASE_CUDA * 92.23f / 100, 65, null);
        addConfig(EnumResolution.SD, EnumTranscodeCodec.H_264, transcodeType,
                1.659f, BASE_CPU_CORE_NUM * 39.99f * 10, 1313, BASE_CUDA * 83.50f / 100, 49, null);
        addConfig(EnumResolution.LD, EnumTranscodeCodec.H_264, transcodeType,
                2.650f, BASE_CPU_CORE_NUM * 39.56f * 10, 1313, BASE_CUDA * 72.00f / 100, 49, null);

    }


    private void addConfig(EnumResolution resolution, EnumTranscodeCodec codec, EnumTranscodeType transcodeType,
                           Float speed, Float cpu, Integer mem){
        String key = key(resolution, codec, transcodeType);
        transcodeResourceCostConfigMap.put(key,
                new TranscodeResourceCostConfig(resolution, codec, transcodeType, speed, cpu.intValue(),
                        mem));
    }

    private void addConfig(EnumResolution resolution, EnumTranscodeCodec codec, EnumTranscodeType transcodeType,
                           Float speed, Float cpu, Integer mem, Float cuda, Integer gpuMem, Integer gpuEncode){
        String key = key(resolution, codec, transcodeType);
        transcodeResourceCostConfigMap.put(key,
                new TranscodeResourceCostConfig(resolution, codec, transcodeType, speed, cpu.intValue(),
                        mem, cuda == null ? null : cuda.intValue(), gpuMem, gpuEncode));
    }


    @Override
    public TranscodeResourceCostConfig find(EnumResolution resolution, EnumTranscodeCodec transcodeCodec, EnumTranscodeType transcodeType) {
        if(transcodeCodec == EnumTranscodeCodec.COPY){
            //copy 只有264 normal
            resolution = null;
            transcodeType = null;
        }else if(transcodeType == EnumTranscodeType.NH_V2){
            //nhv2 只有264
            transcodeCodec = EnumTranscodeCodec.H_264;
        } else if(transcodeCodec == EnumTranscodeCodec.H_265){
            //265不区分是否窄带高清，都按普通编码类型
            transcodeType = EnumTranscodeType.NORMAL;
        } else if(transcodeCodec == EnumTranscodeCodec.AV1){
            transcodeType = EnumTranscodeType.NORMAL;
        }

        String key = key(resolution, transcodeCodec, transcodeType);
        TranscodeResourceCostConfig costConfig = transcodeResourceCostConfigMap.get(key);
        if(costConfig == null){
            return findClosestConfig(resolution, transcodeCodec, transcodeType);
        }else {
            return costConfig;
        }
    }

    private TranscodeResourceCostConfig findClosestConfig(EnumResolution resolution, EnumTranscodeCodec transcodeCodec, EnumTranscodeType transcodeType){
        if(transcodeCodec == EnumTranscodeCodec.H_265){
            resolution = EnumResolution.HD;
            transcodeType = EnumTranscodeType.NORMAL;
        }else if(transcodeType == EnumTranscodeType.NH_V2){
            resolution = EnumResolution.HD;
            transcodeCodec = EnumTranscodeCodec.H_264;
        }
        String key = key(resolution, transcodeCodec, transcodeType);
        return transcodeResourceCostConfigMap.get(key);
    }

    private String key(EnumResolution resolution, EnumTranscodeCodec transcodeCodec, EnumTranscodeType transcodeType){
        return (resolution == null ? "" : resolution.name()) +
                (transcodeCodec == null ? "" : transcodeCodec.name()) +
                (transcodeType == null ? "" : transcodeType.name());
    }
}
