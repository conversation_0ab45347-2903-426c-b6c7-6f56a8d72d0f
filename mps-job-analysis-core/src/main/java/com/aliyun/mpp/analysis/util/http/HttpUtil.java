package com.aliyun.mpp.analysis.util.http;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URI;

/**
 * Created by lihe.lh on 2019/12/30.
 */
@Slf4j
@Component
public class HttpUtil {

    @Resource
    private RestTemplate restTemplate;

    public String sendPostRequest(String url, Object object) {
        try {
            URI uri = new URI(url);
            ResponseEntity<String> result = this.restTemplate.postForEntity(uri, object, String.class);
            return result.getBody();
        } catch (Exception e) {
            log.info("info@sendPostRequest, url:" + url + object, e);
            throw new RuntimeException(e);
        }

    }

}
