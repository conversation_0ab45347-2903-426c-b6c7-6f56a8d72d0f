package com.aliyun.mpp.analysis.domain.service.impl.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.types.Config;
import com.aliyun.mpp.analysis.domain.types.CustomParam;
import com.aliyun.mpp.analysis.domain.types.EstimateResult;
import com.aliyun.mpp.analysis.domain.types.Input;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class NewEngineTaskQuotaEstimator {
    private final int DEFAULT_SCORE = 10;
    private final int CUSTOM_PARAM_WILDCARD_SCORE = 1;
    private final int CUSTOM_PARAM_SPECIFIED_SCORE = 2;
    private final String NULL_VALUE = "_";
    private static final String NH_VERSION = "nhVersion";
    private static final String VIDEO_CODEC = "videoCodec";
    private static final String GPU_RESTORE = "gpuRestore";
    private static final String FRC = "frc";
    private static final String UHD = "uhd";
    private static final String HDR = "hdr";
    private static final String ARCH = "arch";
    private static final String ARM = "arm";
    private static final String NETINT = "netint";

    private final Map<String, Integer> SPECIALIZED_SCORE_MAP = new HashMap<String, Integer>() {{
        put("jobType", 100000);
        //arch不作为得分的依据，而是作为最高级别的过滤，如果输入是arm，配置不是arm，或者输入不是arm，配置是arm，则忽略
        put(ARCH, 0);
        put(NH_VERSION, 10000);
        put(GPU_RESTORE, 10000);
        put(FRC, 1000);
        put(HDR, 1000);
        put(UHD, 1000);
        put("mode", 1000);
        put(VIDEO_CODEC, 100);
    }};
    private final Set<String> SET_NULL_IF_NOX_EXIST_KEYS = new HashSet<String>() {{
        add(NH_VERSION);
        add(GPU_RESTORE);
        add(VIDEO_CODEC);
        add(FRC);
        add(UHD);
        add(HDR);
    }};

    @Resource
    private EngineQuotaConfigRepository engineQuotaConfigRepository;
    @Resource
    private EngineQuotaRefineWithInputRepository engineQuotaRefineWithInputRepository;

    private List<EngineQuotaConfig> quotaConfigs = new ArrayList<>();

    @PostConstruct
    public void init() {
        quotaConfigs = loadEngineQuotaConfigFromRepository();
        startUpdateConfigThread();
    }


    private void addDefaultKeyNullValue(List<EngineQuotaConfig> quotaConfigs){
        for (EngineQuotaConfig config : quotaConfigs) {
            for (String key : SET_NULL_IF_NOX_EXIST_KEYS) {
                if (!config.getConfigs().containsKey(key)) {
                    config.getConfigs().put(key, NULL_VALUE);
                }
            }
        }
    }

    private void startUpdateConfigThread(){
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true){
                    try {
                        quotaConfigs = loadEngineQuotaConfigFromRepository();
                        TimeUnit.SECONDS.sleep(60);
                    }catch (Exception e){
                        log.error("fail@startUpdateConfigThread", e);
                        try {
                            TimeUnit.SECONDS.sleep(10);
                        } catch (InterruptedException ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            }
        }).start();
    }

    private List<EngineQuotaConfig> loadEngineQuotaConfigFromRepository(){
        List<EngineQuotaConfig> configs = engineQuotaConfigRepository.loadAllConfigs();
        addDefaultKeyNullValue(configs);

        List<EngineQuotaRefineWithInputConfig> inputConfigs
                = engineQuotaRefineWithInputRepository.loadAllConfigs();
        addEngineQuotaRefineWithInputConfigs(configs, inputConfigs);
        return configs;
    }

    private void fixConfigResolution(JSONObject scheduleParamJson){
        try{
            JSONObject input = scheduleParamJson.getJSONObject("input");
            JSONObject config = scheduleParamJson.getJSONObject("config");
            if(config != null && input != null && config.getString("videoCodec") != null
                    && ((config.get("width") == null) ||(config.get("height") == null))){
                Integer width = input.getInteger("width");
                Integer height = input.getInteger("height");
                config.put("width", width);
                config.put("height", height);
                config.put("audioOnly", false);

                scheduleParamJson.put("config", config);
            }
        }catch (Exception e){
            log.error("fail@fixConfigResolution, scheduleParamJson:{}, e:{}", scheduleParamJson, e.getMessage());
        }
    }

    private void fixAudioOnlyTranscode(JSONObject scheduleParamJson){
        try{
            JSONObject config = scheduleParamJson.getJSONObject("config");
            JSONObject input = scheduleParamJson.getJSONObject("input");
            if(config != null && config.getString("videoCodec") != null
                    && config.getBooleanValue("audioOnly")){
                config.remove("videoCodec");
                scheduleParamJson.put("config", config);
            }
            if(config != null && config.get("videoCodec") != null && input != null &&
                    (input.get("height") == null || 0 == input.getInteger("height"))){
                config.remove("videoCodec");
                scheduleParamJson.put("config", config);
            }
            if("MP3".equals(config.getString("audioCodec"))){
                config.put("audioCodec", "mp3lame");
                scheduleParamJson.put("config", config);
            }
        }catch (Exception e){
            log.error("fail@fixAudioOnlyTranscode, scheduleParamJson:{}, e:{}", scheduleParamJson, e.getMessage());
        }
    }

    private void fixInputSize(JSONObject scheduleParamJson){
        try{
            JSONObject input = scheduleParamJson.getJSONObject("input");
            if(input != null && input.get("duration") != null && input.get("size") == null){
                input.put("size", Double.valueOf(input.getDouble("duration") * 1024) / 8);
                scheduleParamJson.put("input", input);
            }
        }catch (Exception e){
            log.error("fail@fixInputSize, scheduleParamJson:{}, e:{}", scheduleParamJson, e.getMessage());
        }
    }

    private void addEngineQuotaRefineWithInputConfigs(List<EngineQuotaConfig> configs, List<EngineQuotaRefineWithInputConfig> inputConfigs){
        if(inputConfigs == null || inputConfigs.isEmpty()){
            return;
        }

        for (EngineQuotaConfig config : configs) {
            List<EngineQuotaRefineWithInputConfig> temp = new ArrayList<>();
            for (EngineQuotaRefineWithInputConfig inputConfig : inputConfigs) {
                if(inputConfig.getConfigName().equals(config.getName())){
                    temp.add(inputConfig);
                }
            }
            if(!temp.isEmpty()){
                config.setInputConfigs(temp);
            }
        }
    }

    public EstimateResult analysis(JSONObject jsonObject, CustomParam customParam) {
        JSONObject scheduleParamJson = new JSONObject();
        JSONArray inputs = jsonObject.getJSONArray("inputs");
        if(inputs != null && inputs.size() > 0){
            scheduleParamJson.put("input", inputs.getJSONObject(0));
            scheduleParamJson.put("inputs", inputs);
        }
        scheduleParamJson.put("config",
                jsonObject.getJSONArray("configs").getJSONObject(0));
        fixConfigResolution(scheduleParamJson);
        fixAudioOnlyTranscode(scheduleParamJson);
        fixInputSize(scheduleParamJson);

        List<EngineQuotaConfig> configs = findMatchedConfigs(scheduleParamJson, customParam);
        if (configs == null || configs.isEmpty()) {
            return null;
        } else if (configs.size() == 1) {
            EstimateResult result = estimate(scheduleParamJson, configs.get(0));
            return result;
        } else if (configs.size() > 1) {
            return estimateWithConfigsOfDiffResolution(scheduleParamJson, configs);
        }
        return null;
    }


    private EstimateResult estimateWithConfigsOfDiffResolution(JSONObject jsonObject, List<EngineQuotaConfig> configs){
        Config config = jsonObject.getObject("config", Config.class);
        int configReso = config.getHeight() * config.getWidth();
        Pair<EngineQuotaConfig, EngineQuotaConfig> pair = findMaxLowerResolutionConfig(configReso, configs);
        if(pair.getRight() == pair.getLeft()){
            return estimate(jsonObject, pair.getLeft());
        } else {
            int minUpperReso = ((Integer)pair.getRight().getConfigs().get("width")) * ((Integer)pair.getRight().getConfigs().get("height"));
            int maxLowerReso = ((Integer)pair.getLeft().getConfigs().get("width")) * ((Integer)pair.getLeft().getConfigs().get("height"));

            EstimateResult minUpperResult = estimate(jsonObject, pair.getLeft());
            EstimateResult maxLowerResult = estimate(jsonObject, pair.getRight());
            EstimateResult result = new EstimateResult();
            result.setName(maxLowerResult.getName() + "_" + minUpperResult.getName());
            result.setMaxMigrateRetry(minUpperResult.getMaxMigrateRetry());
            result.setMigrateDiscardQuotaThreshold(minUpperResult.getMigrateDiscardQuotaThreshold());

            result.setExpectCostTime(Double.valueOf(linearFitting(maxLowerReso, minUpperReso,
                    maxLowerResult.getExpectCostTime(), minUpperResult.getExpectCostTime(), configReso)).longValue());

            Map<String, Long> quotaSet = new HashMap<>();
            for (Map.Entry<String, Long> entry : minUpperResult.getQuotaSet().entrySet()) {
                Long upperValue = entry.getValue();
                Long lowerValue = maxLowerResult.getQuotaSet().getOrDefault(entry.getKey(), upperValue);
                quotaSet.put(entry.getKey(), linearFitting(maxLowerReso, minUpperReso,
                        upperValue, lowerValue, configReso).longValue());
            }
            result.setQuotaSet(quotaSet);
            return result;
        }
    }

    private Pair<EngineQuotaConfig, EngineQuotaConfig> findMaxLowerResolutionConfig(int configReso, List<EngineQuotaConfig> configs){
        Integer curMaxLowerReso = null;
        Integer curMinUpperReso = null;
        EngineQuotaConfig curMaxLowerResoConfig = null;
        EngineQuotaConfig curMinUpperResoConfig = null;
        for (EngineQuotaConfig config : configs) {
            int reso = ((Integer)config.getConfigs().get("width")) * ((Integer)config.getConfigs().get("height"));
            if(reso == configReso){
                return new ImmutablePair<>(config, config);
            }
            if(reso <= configReso && (curMaxLowerReso == null || reso > curMaxLowerReso)){
                curMaxLowerReso = reso;
                curMaxLowerResoConfig = config;
            }
            if(reso >= configReso && (curMinUpperReso == null || reso < curMinUpperReso)){
                curMinUpperReso = reso;
                curMinUpperResoConfig = config;
            }
        }
        if(curMaxLowerResoConfig != null && curMinUpperResoConfig != null){
            return new ImmutablePair<>(curMaxLowerResoConfig, curMinUpperResoConfig);
        } else {
            EngineQuotaConfig config = curMaxLowerResoConfig != null ? curMaxLowerResoConfig : curMinUpperResoConfig;
            return new ImmutablePair<>(config, config);
        }
    }

    private EstimateResult estimate(JSONObject jsonObject, EngineQuotaConfig config) {
        if(config.getInputConfigs() != null && !config.getInputConfigs().isEmpty()) {
            EstimateResult estimateResult = estimateFromConfigAndInput(jsonObject, config);
            if(estimateResult != null){
                return estimateResult;
            }
        }

        return estimateFromOnlyConfig(jsonObject, config);

    }

    private EstimateResult estimateFromOnlyConfig(JSONObject jsonObject, EngineQuotaConfig config) {
        EstimateResult result = new EstimateResult();
        result.setName(config.getName());
        result.setQuotaSet(new HashMap<>(config.getQuotaSet()));
        Input input = jsonObject.getObject("input", Input.class);

        if (config.getCost() != null) {
            result.setExpectCostTime(config.getCost());
        } else {
            double duration = input.getDuration() == null ? 3600.0 : input.getDuration();
            result.setExpectCostTime(Double.valueOf(duration / config.getSpeed() + 0.5f).longValue());
            if (result.getExpectCostTime().longValue() <= 0L) {
                result.setExpectCostTime(1L);
            }
        }
        if (config.getDiskQuota() != null) {
            result.getQuotaSet().put("disk", config.getDiskQuota());
        } else if (config.getDiskRatio() != null) {
            result.getQuotaSet().put("disk", Double.valueOf(config.getDiskRatio() * calculateInputsSize(jsonObject)).longValue() >> 20);
        }
        result.setMaxMigrateRetry(config.getMaxMigrateRetry());
        result.setMigrateDiscardQuotaThreshold(config.getMigrateDiscardQuotaThreshold());
        return result;
    }

    private long calculateInputsSize(JSONObject jsonObject){
        if(jsonObject == null && (jsonObject.get("inputs") == null && jsonObject.get("input") == null)){
            return 0L;
        }
        JSONArray inputs = jsonObject.getJSONArray("inputs");
        if(inputs != null) {
            long totalInput = 0L;
            for (int i = 0; i < inputs.size(); i++) {
                Input input = inputs.getObject(i, Input.class);
                if (input != null && input.getSize() != null) {
                    totalInput += input.getSize();
                }
            }
            if(totalInput > 0) {
                return totalInput;
            }
        }
        Input input = jsonObject.getObject("input", Input.class);
        return input.getSize();
    }

    private Pair<EngineQuotaRefineWithInputConfig, EngineQuotaRefineWithInputConfig> findInputConfigPair(JSONObject jsonObject, EngineQuotaConfig config){
        Input input = jsonObject.getObject("input", Input.class);
        if(input.hasVideo()){
            Integer inputReso = input.getWidth() * input.getHeight();
            Integer lowerReso = null;
            Integer upperReso = null;
            EngineQuotaRefineWithInputConfig lower = null;
            EngineQuotaRefineWithInputConfig upper = null;
            for (EngineQuotaRefineWithInputConfig inputConfig : config.getInputConfigs()) {
                if(inputConfig.getInputs().get("width") == null){
                    continue;
                }
                Integer reso = (Integer) inputConfig.getInputs().get("width")
                        * (Integer) inputConfig.getInputs().get("height");
                if(reso.intValue() == inputReso.intValue()){
                    return new ImmutablePair<>(inputConfig, inputConfig);
                }
                if(reso <= inputReso && (lowerReso == null || reso > lowerReso)){
                    lowerReso = reso;
                    lower = inputConfig;
                }
                if(reso >= inputReso && (upperReso == null || reso < upperReso)){
                    upper = inputConfig;
                    upperReso = reso;
                }
            }
            if(lower != null && upper != null){
                return new ImmutablePair<>(lower, upper);
            }else if(lower == null && upper == null){
                return null;
            } else {
                EngineQuotaRefineWithInputConfig temp = lower == null ? upper : lower;
                return new ImmutablePair<>(temp, temp);
            }
        } else {
            EngineQuotaRefineWithInputConfig audioInputConfig = findAudioConfig(config.getInputConfigs());
            if(audioInputConfig == null){
                return null;
            }else {
                return new ImmutablePair<>(audioInputConfig, audioInputConfig);
            }
        }
    }

    private EngineQuotaRefineWithInputConfig findAudioConfig(List<EngineQuotaRefineWithInputConfig> inputConfigs){
        for (EngineQuotaRefineWithInputConfig inputConfig : inputConfigs) {
            if(inputConfig.getInputs().get("width") == null){
                return inputConfig;
            }
        }
        return null;
    }

    private EstimateResult estimateFromConfigAndInput(JSONObject jsonObject, EngineQuotaConfig config) {
        try {
            Pair<EngineQuotaRefineWithInputConfig, EngineQuotaRefineWithInputConfig> inputPair =
                    findInputConfigPair(jsonObject, config);
            if(inputPair == null){
                return null;
            }
            Input input = jsonObject.getObject("input", Input.class);
            EstimateResult result = null;

            if(inputPair.getLeft() == inputPair.getRight()){
                result = calResultFromInputConfig(input, inputPair.getLeft());
                result.setName(config.getName() + "_" +inputPair.getLeft().getInputName());
            } else {
                EstimateResult lowerResult = calResultFromInputConfig(input, inputPair.getLeft());
                EstimateResult upperResult = calResultFromInputConfig(input, inputPair.getRight());
                result = mergeEstimateResult(input, inputPair, lowerResult, upperResult);
                result.setName(config.getName() +"_" + inputPair.getLeft().getInputName() + "_" + inputPair.getRight().getInputName());
            }

            if (config.getDiskQuota() != null) {
                result.getQuotaSet().put("disk", config.getDiskQuota());
            } else if (config.getDiskRatio() != null) {
                result.getQuotaSet().put("disk", Double.valueOf(config.getDiskRatio() * calculateInputsSize(jsonObject)).longValue() >> 20);
            }
            result.setMaxMigrateRetry(config.getMaxMigrateRetry());
            result.setMigrateDiscardQuotaThreshold(config.getMigrateDiscardQuotaThreshold());
            return result;
        }catch (Exception e){
            log.error("fail@estimateFromConfigAndInput, jsonObject:{}, config:{}, exception:{}", jsonObject, config, e.getMessage());
            return null;
        }
    }

    private EstimateResult calResultFromInputConfig(Input input, EngineQuotaRefineWithInputConfig inputConfig){
        EstimateResult result = new EstimateResult();
        result.setQuotaSet(new HashMap<>(inputConfig.getQuotaSet()));
        if (inputConfig.getCost() != null) {
            result.setExpectCostTime(inputConfig.getCost());
        } else {
            result.setExpectCostTime(Double.valueOf(input.getDuration() / inputConfig.getSpeed() + 0.5f).longValue());
            if (result.getExpectCostTime().longValue() <= 0L) {
                result.setExpectCostTime(1L);
            }
        }
        return result;
    }

    private EstimateResult mergeEstimateResult(Input input, Pair<EngineQuotaRefineWithInputConfig, EngineQuotaRefineWithInputConfig> inputPair,
                                               EstimateResult lowerResult, EstimateResult upperResult){
        EstimateResult result = new EstimateResult();
        Double lowerResoSqrt = Math.sqrt((Integer) inputPair.getLeft().getInputs().get("width")
                * (Integer)inputPair.getLeft().getInputs().get("height"));
        Double upperResoSqrt = Math.sqrt((Integer) inputPair.getRight().getInputs().get("width")
                * (Integer)inputPair.getRight().getInputs().get("height"));
        Double inputResoSqrt = Math.sqrt(input.getWidth() * input.getHeight());
        result.setExpectCostTime(Double.valueOf(linearFitting(lowerResoSqrt, upperResoSqrt, lowerResult.getExpectCostTime(),
                upperResult.getExpectCostTime(), inputResoSqrt)).longValue());
        Map<String, Long> quotaSet = new HashMap<>();
        for (Map.Entry<String, Long> entry : upperResult.getQuotaSet().entrySet()) {
            Long upperValue = entry.getValue();
            Long lowerValue = lowerResult.getQuotaSet().getOrDefault(entry.getKey(), upperValue);
            quotaSet.put(entry.getKey(),
                    Double.valueOf(linearFitting(lowerResoSqrt, upperResoSqrt, lowerValue,
                            upperValue, inputResoSqrt.doubleValue())).longValue());
        }
        result.setQuotaSet(quotaSet);
        return result;
    }

    // 线性调整，斜率：k = v(upper) - v(lower) /(upper - lower）, inputValue = k * (input - lower) + v(lower)
    private Double linearFitting(double lower, double upper, double lowerValue, double upperValue, double input){
        return lowerValue + (input - lower) * (upperValue - lowerValue) / (upper - lower);
    }

    private List<EngineQuotaConfig> findMatchedConfigs(JSONObject jsonObject, CustomParam customParam) {
        List<EngineQuotaConfig> matchedConfigs = new ArrayList<>();
        int maxScore = 0;

        Config config = jsonObject.getObject("config", Config.class);
        JSONObject configJson = jsonObject.getJSONObject("config");
        for (String key : SET_NULL_IF_NOX_EXIST_KEYS) {
            Object value = configJson.get(key);
            if (value == null) {
                configJson.put(key, NULL_VALUE);
            }
        }
        boolean isArm = ARM.equals(configJson.getString(ARCH));
        boolean isNetInt = NETINT.equals(configJson.getString(ARCH));
        boolean isCopy = "copy".equals(configJson.get("videoCodec"));
        for (EngineQuotaConfig quotaConfig : quotaConfigs) {
            int currentScore = 0;
            //如果输入的arch与配置的arch不一致，则忽略
            if(isArm && !ARM.equals(quotaConfig.getConfigs().get(ARCH))){
                continue;
            }
            if(!isArm && ARM.equals(quotaConfig.getConfigs().get(ARCH))){
                continue;
            }
            if(isNetInt && !NETINT.equals(quotaConfig.getConfigs().get(ARCH))){
                continue;
            }
            if(!isNetInt && NETINT.equals(quotaConfig.getConfigs().get(ARCH))){
                continue;
            }

            for (Map.Entry<String, Object> entry : quotaConfig.getConfigs().entrySet()) {
                if (entry.getKey().equals("width") || entry.getKey().equals("height")) {
                    continue;
                }
                Object value = configJson.get(entry.getKey());
                if (value != null && value.equals(entry.getValue())) {
                    int score = SPECIALIZED_SCORE_MAP.getOrDefault(entry.getKey(), DEFAULT_SCORE);
                    currentScore += score;
                }
            }
            if (currentScore <= 0) {
                continue;
            }
            if(quotaConfig.isCustomParamConfig()) {
                int customScore = calCustomParamScore(customParam, quotaConfig.getCustomParam());
                if(customScore <= 0){
                    continue;
                }else {
                    currentScore += customScore;
                }
            }
            if (currentScore > maxScore) {
                maxScore = currentScore;
                matchedConfigs.clear();
                matchedConfigs.add(quotaConfig);
            } else if (currentScore == maxScore) {
                matchedConfigs.add(quotaConfig);
            }
        }
        if (matchedConfigs.size() > 1) {
            Integer width = config.getWidth();
            Integer height = config.getHeight();
            if (width < height) {
                Integer temp = height;
                height = width;
                width = temp;
            }
            EngineQuotaConfig resoMatchedConfig = null;
            for (EngineQuotaConfig matchedConfig : matchedConfigs) {
                if (matchedConfig.getConfigs().get("width").equals(width) &&
                        matchedConfig.getConfigs().get("height").equals(height)) {
                    resoMatchedConfig = matchedConfig;
                    break;
                }
            }
            if (resoMatchedConfig != null) {
                matchedConfigs.clear();
                matchedConfigs.add(resoMatchedConfig);
            }
        }
        return matchedConfigs;
    }

    private int calCustomParamScore(CustomParam taskCustomParam, CustomParam configCustomParam){
        if(taskCustomParam == null || configCustomParam == null){
            return 0;
        }
        //tag不为通配符，且tag不匹配
        if(!configCustomParam.getTag().equals("*") && !configCustomParam.getTag().equals(taskCustomParam.getTag()) ){
            return 0;
        }
        //userId不为通配符，且userId不匹配
        if(!configCustomParam.getUserId().equals("*") && !configCustomParam.getUserId().equals(taskCustomParam.getUserId()) ){
            return 0;
        }
        if(!configCustomParam.getPipelineId().equals("*") && !configCustomParam.getPipelineId().equals(taskCustomParam.getPipelineId()) ){
            return 0;
        }
        return configCustomParam.getTag().equals("*") || configCustomParam.getUserId().equals("*") || configCustomParam.getPipelineId().equals("*") ?
                CUSTOM_PARAM_WILDCARD_SCORE : CUSTOM_PARAM_SPECIFIED_SCORE;
    }
}
