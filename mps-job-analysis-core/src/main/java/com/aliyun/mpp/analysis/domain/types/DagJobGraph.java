package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

import java.util.List;

/**
 * Created by lihe.lh on 2021/1/4.
 */
@Data
public class DagJobGraph {
    private List<DagJobVertex> jobVertexs;
    private List<DagJobEdge> jobEdges;

    private ScheduleMode scheduleMode = ScheduleMode.LAZY;
    private MigratePolicy migratePolicy = MigratePolicy.RESTART_ALL;

    public enum ScheduleMode {
        LAZY, LAZY_ONE, EAGER
    }

    public enum MigratePolicy {
        RESTART_ALL, NONE
    }
}
