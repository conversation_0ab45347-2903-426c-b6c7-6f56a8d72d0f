package com.aliyun.mpp.analysis.util.log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Data
@Slf4j(topic = "job_result_report_log")
public class SLSJobResultReportLogUtil {
    public static void info(SLSLogInfo logInfo) {
        try {
            logInfo.setLogLevel("info");

            log.info(getLogContent(logInfo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private static String getLogContent(SLSLogInfo logInfo){
        logInfo.setCode(null);
        JSONObject jsonObject = (JSONObject) JSON.toJSON(logInfo);
        for (Map.Entry<String, Object> entry : logInfo.getParams().entrySet()) {
            jsonObject.put(entry.getKey(), entry.getValue());
        }
        return jsonObject.toJSONString();
    }


}

