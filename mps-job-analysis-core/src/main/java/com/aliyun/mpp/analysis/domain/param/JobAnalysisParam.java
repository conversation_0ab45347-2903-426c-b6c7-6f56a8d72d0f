package com.aliyun.mpp.analysis.domain.param;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.aliyun.mpp.analysis.domain.types.Config;
import com.aliyun.mpp.analysis.domain.types.ScheduleParams;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by lihe.lh on 2020/2/9.
 */
@Data
public class JobAnalysisParam {
    private String engineModel;
    private JSONObject trace;
    private String jobId;
    private String engineParams;
    private String userId;
    private boolean isSliceProcess;
    private String analysisMode;
    private String pipelineId;
    private String tag;
    private ScheduleParams scheduleParams;
    private Date createTime;
    private Integer maxSliceNum;
    private Integer minSliceDuration;
    private JSONObject scheduleParamsByJson;
    private boolean invokeWorkerBrain;
    private boolean useWorkerBrainResult;
    private List<String> speedXRange;
    private boolean isAutoSpeedX;

    public static final String BY_PARSE_ENGINE_PARAMS = "byParseEngineParams";
    public static final String BY_PROBE_META = "byProbeMeta";
    public static final String BY_SCHEDULE_PARAMS = "byScheduleParams";

    public boolean parseUseWorkBrainResultFromAnalysisParam(){
        return useWorkerBrainResult;
    }

    public boolean parseUseWorkBrainResultFromScheduleParams(){
        if(scheduleParams == null || scheduleParams.getConfigs() == null || scheduleParams.getConfigs().isEmpty()){
            return false;
        }
        Config config = scheduleParams.getConfigs().get(0);
        return Boolean.TRUE.equals(config.getByWorkerBrain());
    }
}
