package com.aliyun.mpp.analysis.domain.service;


import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.param.JobExecutionResultReportParam;
import com.aliyun.mpp.analysis.domain.types.*;

/**
 * Created by lihe.lh on 2019/12/30.
 */
public interface JobAnalysisService {
    JobAnalysisResult analysisByParseParam(JobAnalysisParam param);

    @Deprecated
    //analysis will not probe media meta anymore, because analysisByParseParam will not return probe_meta model
    //mps job business should have enough media meta info
    JobAnalysisResult analysisByProbeMeta(JobAnalysisParam param);

    JobAnalysisResult estimateJobWithScheduleParams(JobAnalysisParam param);

    SlaAnalysisResult analysisJobSla(JobAnalysisParam param);

    WorkerBrainAnalysisResult analysisByWorkerBrain(JobAnalysisParam param);

    void reportResult(JobExecutionResultReportParam param);

    ParseScheduleParamsResult parseJobScheduleParams(JobAnalysisResult result, JobAnalysisParam param);
}
