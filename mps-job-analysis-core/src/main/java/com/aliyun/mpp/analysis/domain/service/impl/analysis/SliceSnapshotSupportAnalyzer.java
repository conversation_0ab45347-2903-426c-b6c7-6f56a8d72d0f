package com.aliyun.mpp.analysis.domain.service.impl.analysis;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.service.impl.JobAnalysisUtil;
import com.aliyun.mpp.analysis.domain.types.*;
import org.springframework.stereotype.Component;


import java.util.*;

/**
 * Created by lihe.lh on 2020/2/13.
 */
@Component
public class SliceSnapshotSupportAnalyzer {
    private static final int SNAP_SLICE_ACTIVITY_DURATION = 30 * 60;
    private static final int SNAP_MAX_SLICE_COUNT = 20;

    public boolean supportSliceSnapshot(JobParam jobParam, VideoInfo videoInfo) {
        if (null == videoInfo.getDuration()) {
            return false;
        }
        if (videoInfo.getDuration() <= SNAP_SLICE_ACTIVITY_DURATION) {
            return false;
        }

        return true;
    }

/*
    public JobAnalysisResult generateSliceSnapshotDAG(JobParam jobParam, VideoInfo videoInfo) {
        int minSliceDuration = SNAP_SLICE_ACTIVITY_DURATION;
        int maxSliceCount = SNAP_MAX_SLICE_COUNT;

        int sliceCount = getSnapshotSliceCount(videoInfo.getDuration(), minSliceDuration, maxSliceCount);

        JSONObject extendInfo = new JSONObject();
        extendInfo.put("ossTempPrefix", "parallel/output/" + jobParam.getJobId() + "/" + UUID.randomUUID());
        extendInfo.put("inputDur", videoInfo.getDuration());

        Map<String, Task> taskMap = new HashMap<>();
        Map<String, List<String>> dependencies = new HashMap<>();
        for (int i = 0; i < sliceCount; i++) {
            Task task = generateTask(jobParam, sliceCount, extendInfo, i + 1, videoInfo);
            taskMap.put(task.getName(), task);
            dependencies.put(task.getName(), new ArrayList<>());
        }

        return JobAnalysisResult.generateDAGModeResult(taskMap, dependencies);
    }
*/

    private Task generateTask(JobParam jobParam, int sliceCount, JSONObject extendInfo,
                              int index, VideoInfo videoInfo) {
        Task task = new Task();
        task.setName("snapshot_" + index);
        task.setTaskId(UUID.randomUUID().toString().replace("-", ""));

        JobParam taskParam = JobParam.parseParam(jobParam.getSourceEngineParams());
        setSliceSnapshotParam(taskParam, sliceCount, extendInfo, index);
        updateSnapshotParamUserData(jobParam, task.getTaskId());

        JSONObject newEngineParams = JSONObject.parseObject(jobParam.getSourceEngineParams());
        JSONObject params = jobParam.getParams();
        params.put("arguments", jobParam.getTranscodeArguments().toJSONString());
        params.put("userData", jobParam.getUserData());
        params.put(JobParam.PROCESS_LOGIC, jobParam.getTranscodeProcessLogic());

        newEngineParams.put("params", params);
        task.setEngineParams(newEngineParams.toJSONString());

        long disk = Float.valueOf(videoInfo.getFileSize() / 1024 / 1024 / sliceCount).intValue();
        int localCost = Double.valueOf(videoInfo.getDuration() / JobAnalysisUtil.SNAPSHOT_SPEED / sliceCount).intValue();
        Map<String, Long> quotaSet = new HashMap<>();
        quotaSet.put(JobAnalysisUtil.CPU, 2000L);
        quotaSet.put(JobAnalysisUtil.DISK, disk);
        task.setQuotaSet(Collections.singletonMap("cpu", 2000L));
        task.setExpectCostTime(localCost);

        return task;

    }

    private void setSliceSnapshotParam(JobParam jobParam, int sliceCount, JSONObject extendInfo, int index) {
        JSONObject parallelEnv = new JSONObject();
        parallelEnv.put("taskType", "sliceSnapshot");
        parallelEnv.put("sliceCount", sliceCount);
        parallelEnv.put("instanceId", index);
        parallelEnv.put("ossTempPrefix", extendInfo.getString("ossTempPrefix"));
        parallelEnv.put("sliceVer", extendInfo.getString("sliceVer"));

        JSONObject argumentsArr = jobParam.getTranscodeArguments();
        argumentsArr.put("parallelEnv", parallelEnv);

        jobParam.setTranscodeArguments(argumentsArr);
        jobParam.setTranscodeProcessLogic("sliceSnapshot");
    }

    private void updateSnapshotParamUserData(JobParam jobParam, String taskId) {
        JSONObject userData = jobParam.getUserData();
        userData.put("sliceActivityId", taskId);
        userData.put("type", "sliceSnapshot");
        userData.put("activityType", "snapshot");
        jobParam.setUserData(userData);
    }


    private int getSnapshotSliceCount(Double inputFileDuration, int minSliceDuration, int maxSliceCount) {
        if (null == inputFileDuration) {
            return 1;
        }
        if (inputFileDuration <= minSliceDuration) {
            return 1;
        }

        int num = (int) (Math.ceil(inputFileDuration / minSliceDuration));

        if (num <= maxSliceCount) {
            return num;
        }

        return maxSliceCount;
    }
}
