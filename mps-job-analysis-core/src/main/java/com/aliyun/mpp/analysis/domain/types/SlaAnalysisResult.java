package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

/**
 * Created by lihe.lh on 2021/2/19.
 */
@Data
public class SlaAnalysisResult {
    private String code = "Success";
    private String message;
    private Long slaTime;

    public SlaAnalysisResult() {
    }

    public SlaAnalysisResult(String code) {
        this.code = code;
    }

    public static SlaAnalysisResult NOT_SUPPORT_RESULT = new SlaAnalysisResult("NotSupport");

    public boolean isSuccess(){
        return "Success".equals(code);
    }
}
