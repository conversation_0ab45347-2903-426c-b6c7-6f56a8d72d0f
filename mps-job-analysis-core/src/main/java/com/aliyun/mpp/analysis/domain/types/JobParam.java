package com.aliyun.mpp.analysis.domain.types;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;


/**
 * Created by lihe.lh on 2020/2/11.
 */
@Data
public class JobParam {
    private final static String META = "meta";
    private final static String ANALYSIS = "analyse";
    private final static String SNAPSHOT = "snapshot";
    private final static String SLICE_TRANSCODE = "sliceTranscode";
    private final static String SLICE_SNAPSHOT = "sliceSnapshot";
    private static final String CUSTOM_TRANSCODE = "customtranscode";
    private static final Set<String> DYNAMIC_TEMPLATE_TRANSCODE_LOGICS = new HashSet<>(Arrays.asList("flv-ld", "flv-sd", "flv-hd", "flv-ud",
            "m3u8-xld", "m3u8-ld", "m3u8-sd", "m3u8-hd", "m3u8-ud",
            "mp4-xld", "mp4-ld", "mp4-sd", "mp4-hd", "mp4-ud"));


    private static final int NOT_DOWNLOAD = 3;
    public static final String PROCESS_LOGIC = "process_logic";
    public static final int SLICE_AUDIO_INDEX = 0;
    public static final int SLICE_MERGE = -1;

    private String sourceEngineParams;
    private Long userId;
    private String jobId;
    private JSONObject params;
    private Type type;
    private String transcodeProcessLogic;
    private JSONObject transcodeArguments;
    private JSONObject transcodeInputParam;
    private JSONObject container;
    private JSONObject transcodeVideoConfig;
    private JSONObject transcodeAudioConfig;
    private JSONObject transcodeTransConfig;
    private JSONObject transcodeFeatures;
    private JSONObject transcodeMuxConfig;
    private JSONObject scheduleParams;
    private JSONObject userData;
    private String transcodeTaskType;
    private boolean isSlice = false;
    private int sliceCount = 1;
    private boolean isSliceAudio = false;
    private boolean isSliceVideo = false;
    private boolean isSliceMerger = false;
    //transcode meta param
    /*
    {
        "jobId": "1163888667191439360",
        "params": "{\"arguments\":\"{\\\"output_oss_object\\\":\\\"temp/2020-02-11/786df529fa2946a090ceebc484606384\\\",\\\"output_oss_bucket\\\":\\\"mts-log-param-cn-shanghai\\\",\\\"aliyunUid\\\":\\\"1018745413976365\\\"}\",\"callbackMns\":\"api-worker-job-completed\",\"expire\":1800,\"input_param\":\"{\\\"param\\\":\\\"{\\\\\\\"error_code\\\\\\\":0}\\\",\\\"data\\\":\\\"{\\\\\\\"type\\\\\\\":3,\\\\\\\"url\\\\\\\":\\\\\\\"http://live-aliyun-record-sh.oss-cn-shanghai.aliyuncs.com/2020-02-11%2F2846a479-25df-3ba9-afb6-f90d875c5311%2F1581388155268_1581388335268_3fe95063-e82d-4f51-9cd9-dbfe064b524c.m3u8\\\\\\\"}\\\",\\\"isSliceTrans\\\":false}\",\"mixEncodingSwitch\":0,\"mixTag\":0,\"nvencTag\":0,\"oHeight\":0,\"oWidth\":0,\"process_logic\":\"meta\",\"reportTranscodingMns\":\"api-worker-task-transcoding-report-queue\",\"userData\":{\"mtsJobId\":\"1163888667191439360\",\"requestId\":\"D6E38334-D130-4266-B86A-6C587CD4E2F8\",\"type\":\"meta\"}}",
        "priority": 10,
        "resource": {
            "cpu": 100,
            "exclusivePrefixKey": "",
            "io": false,
            "largeDisk": false,
            "preemptive": false,
            "tiny": true
        },
        "type": "Transcode",
        "userId": 1426105496228119
    }
     */


    public JobParam() {

    }

    public static JobParam parseParam(String engineParams) {
        JSONObject jsonObject = JSONObject.parseObject(engineParams);
        JobParam jobParam = new JobParam();
        jobParam.setSourceEngineParams(engineParams);
        jobParam.setType(JobParam.Type.parse(jsonObject.getString("type")));
        jobParam.setJobId(jsonObject.getString("jobId"));
        jobParam.setUserId(jsonObject.getLong("userId"));
        jobParam.setParams(JSONObject.parseObject(jsonObject.getString("params")));
        jobParam.setUserData(jobParam.getParams() != null ? jobParam.getParams().getJSONObject("userData") : null);

        if (jobParam.isTranscode()) {
            JSONObject params = jobParam.getParams();
            jobParam.setTranscodeProcessLogic(params.getString(PROCESS_LOGIC));
            jobParam.setTranscodeArguments(params.getJSONObject("arguments"));
            jobParam.setTranscodeInputParam(params.getJSONObject("input_param"));
            jobParam.setTranscodeTaskType(params.getString("taskType"));
            if (jobParam.getTranscodeProcessLogic().equals(CUSTOM_TRANSCODE) ||
                    jobParam.getTranscodeProcessLogic().equals(SLICE_TRANSCODE)) {
                JSONObject customParams = jobParam.getTranscodeArguments().getJSONObject("custom_param").getJSONObject("custom_params");
                jobParam.setTranscodeVideoConfig(customParams.getJSONObject("video"));
                jobParam.setTranscodeAudioConfig(customParams.getJSONObject("audio"));
                jobParam.setScheduleParams(customParams.getJSONObject("scheduleParams"));
                jobParam.setContainer(customParams.getJSONObject("container"));
                jobParam.setTranscodeFeatures(customParams.getJSONObject("transFeatures"));
                jobParam.setTranscodeTransConfig(customParams.getJSONObject("transConfig"));
                jobParam.setTranscodeMuxConfig(customParams.getJSONObject("muxConfig"));
            } else if (jobParam.getTranscodeProcessLogic().equals(SNAPSHOT)) {
                JSONObject customParams = jobParam.getTranscodeArguments().getJSONObject("custom_param");
                jobParam.setTranscodeMuxConfig(customParams.getJSONObject("muxConfig"));
            }

            if (jobParam.getTranscodeProcessLogic().equals(SLICE_TRANSCODE)) {
                jobParam.setSlice(true);
                JSONObject parallelEnv = jobParam.getTranscodeArguments().getJSONObject("parallelEnv");
                String taskType = parallelEnv.getString("taskType");
                if(taskType != null && taskType.equals("merge")){
                    jobParam.getTranscodeVideoConfig().put("copy", true);
                }else {
                    int sliceIndex = parallelEnv.getInteger("instanceId");
                    if (sliceIndex == SLICE_AUDIO_INDEX) {
                        jobParam.setTranscodeVideoConfig(null);
                        jobParam.setSliceCount(parallelEnv.getInteger("sliceCount"));
                        jobParam.setSliceAudio(true);
                    } else if (sliceIndex == SLICE_MERGE) {
                        jobParam.getTranscodeVideoConfig().put("copy", true);
                        jobParam.setSliceMerger(true);
                    } else {
                        jobParam.setSliceCount(parallelEnv.getInteger("sliceCount"));
                        jobParam.setSliceVideo(true);
                    }
                }
            }else if(jobParam.getTranscodeProcessLogic().equals(SLICE_SNAPSHOT)){
                JSONObject parallelEnv = jobParam.getTranscodeArguments().getJSONObject("parallelEnv");
                jobParam.setSlice(true);
                jobParam.setSliceCount(parallelEnv.getInteger("sliceCount"));
            }
        }
        return jobParam;
    }

    public boolean isSimpleJob() {
        return type == Type.ConvertSubtitle || type == Type.Package;
    }

    public boolean isConvertSubtitle(){
        return type == Type.ConvertSubtitle;
    }

    public boolean isPackage(){
        return type == Type.Package;
    }


    public boolean isTranscodeMeta() {
        return type == Type.Transcode && META.equals(transcodeProcessLogic);
    }

    public boolean isTranscodeAnalysis() {
        return type == Type.Transcode && ANALYSIS.equals(transcodeProcessLogic);
    }

    public boolean isSliceTranscode(){
        return type == Type.Transcode && SLICE_TRANSCODE.equals(transcodeProcessLogic);
    }


    public boolean isSystemDynamicTranscode() {
        return type == Type.Transcode && DYNAMIC_TEMPLATE_TRANSCODE_LOGICS.contains(transcodeProcessLogic);
    }

    public boolean isTranscodeSnapshot() {
        return type == Type.Transcode && SNAPSHOT.equals(transcodeProcessLogic);
    }

    public boolean isSliceSnapshot(){
        return type == Type.Transcode && SLICE_SNAPSHOT.equals(transcodeProcessLogic);
    }


    public boolean isCustomTranscode() {
        return type == Type.Transcode && CUSTOM_TRANSCODE.equals(transcodeProcessLogic);
    }

    public boolean isSingleFrameSnapshot() {
        if (type == Type.Transcode && isTranscodeSnapshot()) {
            JSONObject muxConfig = this.getTranscodeMuxConfig();
            JSONObject snapshotConfig = muxConfig.getJSONObject("snapshot");
            Integer number = snapshotConfig.getInteger("number");
            return number != null && number.equals(1);
        } else {
            return false;
        }
    }

    public boolean isLocalParallelSnapshot(){
        if(!isTranscodeSnapshot()){
            return false;
        }
        JSONObject muxConfig = this.getTranscodeMuxConfig();
        JSONObject snapshotConfig = muxConfig == null ? null : muxConfig.getJSONObject("snapshot");
        return snapshotConfig != null && "parallel".equals(snapshotConfig.getString("method"));
    }

    public boolean isDownloadInputVideo() {
        if (isTranscode()) {
            JSONObject data = transcodeInputParam.getJSONObject("data");
            Integer type = data.getInteger("type");
            return type != null && type.intValue() != NOT_DOWNLOAD;
        } else {
            return false;
        }
    }

    public boolean isTranscodeWithMultiInputFiles(){
        if(isTranscode() && transcodeFeatures != null){
            return transcodeFeatures.containsKey("merge") || transcodeFeatures.containsKey("mergeConfigUrl");
        }
        return false;
    }

    public boolean isTranscodeNeedProbeMeta(){
        if(isCustomTranscode()){
            //纯音频
            if(transcodeVideoConfig != null){
                Integer width = transcodeVideoConfig.getInteger("width");
                Integer height = transcodeVideoConfig.getInteger("height");
                boolean isCheckVideoReso = (transcodeTransConfig != null && transcodeTransConfig.getBooleanValue("isCheckReso"));
                if((width == null && height == null) || isCheckVideoReso){
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isTranscode() {
        return type == Type.Transcode;
    }

    public boolean isEditing() {
        return type == Type.Editing;
    }

    public boolean isM3u8Input(){
        try{
            JSONObject data = this.getTranscodeInputParam().getJSONObject("data");
            return data.getString("url").endsWith("m3u8");
        }catch (Exception e){
            return false;
        }
    }

    public enum Type {
        Transcode,
        Editing,
        Package,
        ConvertSubtitle,
        UnKnown;

        public static Type parse(String typeValue) {
            if (typeValue.equals(Transcode.name())) {
                return Transcode;
            } else if (typeValue.equals(Editing.name())) {
                return Editing;
            } else if (typeValue.equals(Package.name())) {
                return Package;
            } else if (typeValue.equals(ConvertSubtitle.name())) {
                return ConvertSubtitle;
            } else {
                return UnKnown;
            }
        }
    }
}
