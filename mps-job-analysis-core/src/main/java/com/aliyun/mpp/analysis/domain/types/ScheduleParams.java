package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by lihe.lh on 2020/2/3.
 */
@Data
public class ScheduleParams {
    private String pipelineId;
    private Map<String, Long> quotaSet;
    private Integer priority;
    private Integer expectCostTime;
    private List<Input> inputs;
    private List<Config> configs;
    private Integer parallelNum;
    private String slaLevel;
    private Integer sliceNum;
    private List<String> speedXRange;
}
