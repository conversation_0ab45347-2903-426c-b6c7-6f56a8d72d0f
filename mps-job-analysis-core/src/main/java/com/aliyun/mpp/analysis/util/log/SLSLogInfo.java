package com.aliyun.mpp.analysis.util.log;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
public class SLSLogInfo {
    @JSONField(name = "product")
    private String product;
    @JSONField(name = "userId")
    private String userId;
    @JSONField(name = "RequestId")
    private String requestId;
    @JSONField(name = "currentTime")
    private String currentTime;
    @JSONField(name = "costTime")
    private Long costTime;
    @J<PERSON>NField(name = "event")
    private String event;
    @JSONField(name = "sourceEngineParams")
    private String engineParams;
    @JSONField(name = "engineModel")
    private String engineModel;
    @JSONField(name = "service")
    private String service = "tc.analysis.mps";
    @JSONField(name = "code")
    private String code = "Success";
    @JSONField(name = "message")
    private String message;
    @JSONField(name = "function")
    private String function;
    @J<PERSON><PERSON>ield(name = "param")
    private String param;
    @J<PERSON><PERSON>ield(name = "result")
    private String result;
    @JSONField(name = "trace")
    private Object trace;
    @JSONField(name = "taskId")
    private String taskId;
    @JSONField(name = "allocQuotaSet")
    private Object allocQuotaSet;
    @JSONField(name = "tag")
    private String tag;
    @JSONField(name = "pipelineId")
    private String pipelineId;
    @JSONField(name = "subPipelineId")
    private String subPipelineId;
    @JSONField(name = "logLevel")
    private String logLevel;
    @JSONField(name = "jobId")
    private String jobId;
    @JSONField(name = "speedXRange")
    private String speedXRange;
    @JSONField(name = "isAutoSpeedX")
    private Boolean isAutoSpeedX;

    @JSONField(serialize = false)
    private Map<String, Object> params = new HashMap<>();

    public SLSLogInfo() {
    }

    public void initCurrentTime(){
        currentTime = DateFormatUtils.format(new Date(), DATE_FORMAT);
    }


    private static final String DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

}
