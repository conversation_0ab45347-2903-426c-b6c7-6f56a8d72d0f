package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by lihe.lh on 2020/2/11.
 */
@Data
public class TranscodeResourceCostConfig {
    private EnumResolution resolution;
    private EnumTranscodeCodec codec;
    private EnumTranscodeType transcodeType;
    private Float speed;
    //cpu定位0.001核
    private Long cpu;
    private Long mem;
    //gpu cuda 1000000 表示单卡
    private Long cuda;
    private Long gpuMem;
    private Long gpuEncode;


    public TranscodeResourceCostConfig(EnumResolution resolution, EnumTranscodeCodec codec, EnumTranscodeType transcodeType,
                                       Float speed, Integer cpu, Integer mem) {
        this.resolution = resolution;
        this.codec = codec;
        this.transcodeType = transcodeType;
        this.speed = speed;
        this.cpu = cpu == null ? null : cpu.longValue();
//        this.mem = mem;
    }

    public TranscodeResourceCostConfig(EnumResolution resolution, EnumTranscodeCodec codec, EnumTranscodeType transcodeType,
                                       Float speed, Integer cpu, Integer mem, Integer cuda, Integer gpuMem, Integer gpuEncode) {
        this.resolution = resolution;
        this.codec = codec;
        this.transcodeType = transcodeType;
        this.speed = speed;
        this.cpu = cpu == null ? null : cpu.longValue();
//        this.mem = mem;
        this.cuda = cuda == null ? null : cuda.longValue();
        this.gpuMem = gpuMem == null ? null : gpuMem.longValue();
        this.gpuEncode = gpuEncode == null ? null : gpuEncode.longValue();
    }

    public Map<String, Long> getQuotaSet(){
        Map<String, Long> quotaSet = new HashMap<>();
        quotaSet.put("cpu", cpu);
        if(mem != null){
            quotaSet.put("mem", mem);
        }
        if(cuda != null){
            quotaSet.put("gpu", cuda);
        }
        if(gpuMem != null) {
            quotaSet.put("gmem", gpuMem);
        }
        if(gpuEncode != null){
            quotaSet.put("gencode", gpuEncode);
        }
        return quotaSet;
    }
}
