package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

import java.util.Map;

/**
 * Created by lihe.lh on 2020/12/7.
 */
@Data
public class EstimateResult {
    private String name;
    private Map<String, Long> quotaSet;
    private Long expectCostTime;
    private String expectTimeUnit = "Second";
    private Integer maxMigrateRetry;
    private Map<String, Long> migrateDiscardQuotaThreshold;
}
