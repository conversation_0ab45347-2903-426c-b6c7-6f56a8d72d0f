package com.aliyun.mpp.analysis.infrastructure.repository;

import com.aliyun.mpp.analysis.domain.service.impl.config.EngineQuotaRefineWithInputConfig;
import com.aliyun.mpp.analysis.domain.service.impl.config.EngineQuotaRefineWithInputRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
public class IbatisEngineQuotaRefineWithInputRepository implements EngineQuotaRefineWithInputRepository {
    @Resource
    private EngineQuotaRefineWithInputConfigMapper engineQuotaRefineWithInputConfigMapper;
    @Override
    public List<EngineQuotaRefineWithInputConfig> loadAllConfigs() {
        List<EngineQuotaRefineWithInputConfigDO> configDOS =
                engineQuotaRefineWithInputConfigMapper.loadAllConfigs();
        List<EngineQuotaRefineWithInputConfig> result = new ArrayList<>();
        for (EngineQuotaRefineWithInputConfigDO configDO : configDOS) {
            result.add(EngineQuotaRefineWithInputConfigDO.toEngineQuotaRefineWithInputConfig(configDO));
        }
        return result;
    }
}
