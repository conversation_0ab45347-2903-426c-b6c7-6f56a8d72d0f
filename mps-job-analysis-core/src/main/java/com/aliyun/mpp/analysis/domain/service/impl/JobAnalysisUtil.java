package com.aliyun.mpp.analysis.domain.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.service.impl.analysis.SliceSnapshotSupportAnalyzer;
import com.aliyun.mpp.analysis.domain.service.impl.analysis.SliceTranscodeSupportAnalyzer;
import com.aliyun.mpp.analysis.domain.service.impl.analysis.TranscodeSpecificationAnalyzer;
import com.aliyun.mpp.analysis.domain.service.impl.config.TranscodeResourceCostConfigRepository;
import com.aliyun.mpp.analysis.domain.service.impl.config.MemTranscodeResourceCostConfigRepository;
import com.aliyun.mpp.analysis.domain.service.impl.config.TranscodeSpecification;
import com.aliyun.mpp.analysis.domain.types.*;
import com.aliyun.mpp.analysis.util.log.GlobalVars;
import com.aliyun.mpp.analysis.util.log.SLSLogInfo;
import com.aliyun.mpp.analysis.util.log.SLSLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


/**
 * Created by lihe.lh on 2020/2/11.
 */
@Slf4j
@Component
public class JobAnalysisUtil {
    public final static String CPU = "cpu";
    public final static String GPU = "gpu";

    public final static Map<String, Long> QUOTA_SET_FOR_SIMPLE_TASK = Collections.singletonMap(CPU, 10L);
    public final static Long SIMPLE_TASK_EXPECT_COST_TIME = 1L;

    public final static Map<String, Long> QUOTA_SET_FOR_META = Collections.singletonMap(CPU, 500L);
    public final static Long META_EXPECT_COST_TIME = 1L;

    public final static Map<String, Long> QUOTA_SET_FOR_ANALYSIS = Collections.singletonMap(CPU, 1000L);
    public final static Long ANALYSIS_LOCAL_PROCESS_SPEED = 20L;

    public final static Map<String, Long> QUOTA_SET_FOR_SINGLE_SNAPSHOT = Collections.singletonMap(CPU, 500L);
    public final static Long DEFAULT_PROCESS_COST_FOR_SINGLE_SNAPSHOT = 2L;

    public final static Map<String, Long> QUOTA_SET_FOR_SLICE_SNAPSHOT = Collections.singletonMap(CPU, 2000L);

    public final static Map<String, Long> QUOTA_SET_FOR_NORMAL_SNAPSHOT = new HashMap<String, Long>() {
        {
            put(CPU, 2000L);
            put(DISK, 1000L);
        }
    };

    public final static Map<String, Long> QUOTA_SET_FOR_LOCAL_PARALLEL_SNAPSHOT = new HashMap<String, Long>() {
        {
            put(CPU, 8000L);
            put(DISK, 2000L);
        }
    };
    public final static Long DEFAULT_PROCESS_COST_FOR_SLICE_SNAPSHOT = 1800L;


    //下载速度，B/s
    public final static Integer DOWNLOAD_SPEED = 20 * 1024 * 1024;

    public final static Map<String, Long> DEFAULT_QUOTA_SET = Collections.singletonMap(CPU, 8000L);
    public final static Long DEFAULT_COST = 3600L;


    public static final String DISK = "disk";
    public static final int SNAPSHOT_SPEED = 10;

    @Resource
    private TranscodeResourceCostConfigRepository transcodeResourceCostConfigRepository;

    @Resource
    private GlobalVars globalVars;

    public File parseTranscodeInputFile(JobParam jobParam) {
        Long userId = null;
        String roleArn = null;
        String url = null;
        if (jobParam.isTranscode()) {
            userId = jobParam.getTranscodeArguments().getLong("aliyunUid");
            roleArn = jobParam.getTranscodeArguments().getString("roleArn");
            JSONObject data = jobParam.getTranscodeInputParam().getJSONObject("data");
            url = data.getString("url");
        } else {
            return null;
        }

        try {
            OSSFile ossFile = new OSSFile(url, userId, roleArn);

            if (roleArn == null && !globalVars.isServiceBucket(ossFile.getBucket())) {
                ossFile.setRoleArn("acs:ram::" + userId + ":role/AliyunMTSDefaultRole");
            }
            return ossFile;
        } catch (Exception e) {
            log.error("fail@parseTranscodeInputFile", e);
            return null;
        }

    }

    public VideoInfo getInputVideoInfoInParams(JobParam jobParam) {
        if (jobParam.getTranscodeInputParam() != null) {
            JSONObject jsonObject = jobParam.getTranscodeInputParam().getJSONObject("videoInfo");
            if (jsonObject != null) {
                VideoInfo videoInfo = new VideoInfo();
                videoInfo.setFileSize(jsonObject.getLong("fileSize"));
                videoInfo.setDuration(jsonObject.getDouble("duration"));
                videoInfo.setWidth(jsonObject.getInteger("width"));
                videoInfo.setHeight(jsonObject.getInteger("height"));
                videoInfo.setBitrate(jsonObject.getDouble("bitRate"));
                videoInfo.setVideoBitrate(jsonObject.getDouble("bitRate"));
                if (jsonObject.getFloat("audio_bitrate") != null) {
                    videoInfo.setAudioBitrate(jsonObject.getDouble("audio_bitrate"));
                }
                videoInfo.setAudioCodecName(jsonObject.getString("audio_codec"));
                videoInfo.setVideoCodecName(jsonObject.getString("video_codec"));
                if (jsonObject.getFloat("video_bitrate") != null) {
                    videoInfo.setVideoBitrate(jsonObject.getDouble("video_bitrate"));
                }

                if (videoInfo.getBitrate() != null && videoInfo.getDuration() != null) {
                    Double fileSizeByBitrate = videoInfo.getDuration() * videoInfo.getBitrate() * 1000 / 8;
                    if (videoInfo.getFileSize() == null || videoInfo.getFileSize() / 1024 / 1024 < fileSizeByBitrate) {
                        videoInfo.setFileSize(fileSizeByBitrate.longValue());
                    }
                }

                return videoInfo;
            } else if (jobParam.isM3u8Input()) {
                return VideoInfo.generateDefaultVideoInfo();
            }
        }

        return null;
    }

    /**
     * 解析单任务资源预估
     *
     * @param jobParam
     * @param videoInfo
     * @return
     */
    public JobAnalysisResult analysisJobSingleModeExecute(JobAnalysisParam param, JobParam jobParam, VideoInfo videoInfo) {
        long downloadCost = downloadCost(jobParam, videoInfo);
        long diskQuota = parseJobDiskQuota(jobParam, videoInfo);
        if (jobParam.isTranscodeAnalysis()) {
            return generateAnalysisEstimateResult(param, videoInfo, downloadCost, diskQuota);
        } else if (jobParam.isTranscodeSnapshot()) {
            return generateSnapshotEstimateResult(param, jobParam, videoInfo, diskQuota, downloadCost);
        } else if (jobParam.isSliceSnapshot()) {
            return generateSliceSnapshotEstimateResult(param, jobParam, videoInfo, diskQuota, downloadCost);
        } else {
            //以下都为文件转码
            TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
            TranscodeResourceCostConfig costConfig =
                    transcodeResourceCostConfigRepository.find(specification.getResolution(),
                            specification.getTranscodeCodec(), specification.getTranscodeType());

            Map<String, Long> quotaSet = costConfig.getQuotaSet();
            if (jobParam.getSliceCount() > 1 && jobParam.isSliceVideo()) {
                quotaSet.put(DISK, diskQuota / jobParam.getSliceCount());
            } else if(jobParam.getSliceCount() > 1 && jobParam.isSliceAudio()){
                quotaSet.put(DISK, Math.min(diskQuota, 10 * 1000));
            } else {
                quotaSet.put(DISK, diskQuota);
            }
            Float outputFps = 30.f;
            if (jobParam.getTranscodeVideoConfig() != null && jobParam.getTranscodeVideoConfig().getFloat("fps") != null) {
                outputFps = jobParam.getTranscodeVideoConfig().getFloat("fps");
            }

            Long refinedCpuQuota = TranscodeSpecificationAnalyzer.refineTranscodeCpuQuota(specification, jobParam, videoInfo, transcodeResourceCostConfigRepository);
            Map<String, Long> quotaSetUnRefined = new HashMap<>(quotaSet);
            if (refinedCpuQuota != null) {
                quotaSet.put(CPU, refinedCpuQuota);
            }

            float fpsRatio = outputFps / MemTranscodeResourceCostConfigRepository.BASE_FPS;
            int localCost = Double.valueOf(videoInfo.getDuration() * fpsRatio / costConfig.getSpeed()).intValue();
            localCost /= jobParam.getSliceCount();
            logTranscodeAnalysisResult(param, quotaSet, videoInfo, downloadCost, localCost, specification, costConfig);
            return JobAnalysisResult.generateSingleModeResult(quotaSet, localCost + downloadCost, quotaSetUnRefined);
        }
    }


    private JobAnalysisResult generateAnalysisEstimateResult(JobAnalysisParam param, VideoInfo videoInfo, long downloadCost, long diskQuota) {
        int localProcessCost = Double.valueOf(videoInfo.getDuration() / ANALYSIS_LOCAL_PROCESS_SPEED).intValue();
        Map<String, Long> quotaSet = new HashMap<>(QUOTA_SET_FOR_ANALYSIS);
        quotaSet.put(DISK, diskQuota);
        logAnalysisResult(param, quotaSet, videoInfo, downloadCost, localProcessCost);
        return JobAnalysisResult.generateSingleModeResult(quotaSet, downloadCost + localProcessCost);
    }


    private Integer parseJobDiskQuota(JobParam jobParam, VideoInfo videoInfo) {
        if (!jobParam.isDownloadInputVideo() && (jobParam.isTranscodeAnalysis() || jobParam.isTranscodeSnapshot())) {
            return 0;
        }

        final int DISK_QUOTA_CONVERT = 1024 * 1024;

        if (jobParam.isTranscodeSnapshot() || jobParam.isTranscodeAnalysis()) {
            return Long.valueOf(videoInfo.getFileSize() / DISK_QUOTA_CONVERT).intValue();
        }
        if (jobParam.isTranscode()) {
            boolean isM3u8Input = videoInfo.getFile() != null && videoInfo.getFile().isM3u8();
            boolean isCopy = jobParam.getTranscodeVideoConfig() != null && jobParam.getTranscodeVideoConfig().getBooleanValue("copy");
            if (isM3u8Input && isCopy) {
                return Long.valueOf(videoInfo.getFileSize() * 4 / DISK_QUOTA_CONVERT).intValue();
            }
        }

        return Long.valueOf(videoInfo.getFileSize() * 2 / DISK_QUOTA_CONVERT).intValue();
    }

    private JobAnalysisResult generateSnapshotEstimateResult(JobAnalysisParam param, JobParam jobParam, VideoInfo videoInfo, Long diskQuota, Long downloadCost) {
        long cpuQuota = parseSnapshotQuota(videoInfo);
        int speed = SNAPSHOT_SPEED;

        JSONObject muxConfig = jobParam.getTranscodeMuxConfig();
        JSONObject snapshotConfig = muxConfig == null ? null : muxConfig.getJSONObject("snapshot");
        if (snapshotConfig != null && "parallel".equals(snapshotConfig.getString("method"))) {
            cpuQuota = 8000;
        }

        long localCost = Double.valueOf(videoInfo.getDuration() / speed).intValue();
        Map<String, Long> quotaSet = new HashMap<>();
        quotaSet.put(CPU, cpuQuota);
        quotaSet.put(DISK, diskQuota);
        logAnalysisResult(param, quotaSet, videoInfo, downloadCost, localCost);
        return JobAnalysisResult.generateSingleModeResult(quotaSet, localCost + downloadCost);
    }

    private JobAnalysisResult generateSliceSnapshotEstimateResult(JobAnalysisParam param, JobParam jobParam, VideoInfo videoInfo, Long diskQuota, Long downloadCost) {
        long cpuQuota = parseSnapshotQuota(videoInfo);
        int speed = SNAPSHOT_SPEED;
        int sliceCount = jobParam.getSliceCount();
        int localCost = Double.valueOf(videoInfo.getDuration() / speed).intValue() / sliceCount;
        Map<String, Long> quotaSet = new HashMap<>();
        quotaSet.put(CPU, cpuQuota);
        quotaSet.put(DISK, diskQuota / sliceCount);
        logAnalysisResult(param, quotaSet, videoInfo, downloadCost, localCost);
        return JobAnalysisResult.generateSingleModeResult(quotaSet, localCost + downloadCost);
    }


    private int parseSnapshotQuota(VideoInfo videoInfo) {
        EnumResolution reso = EnumResolution.parseReso(videoInfo.getWidth(), videoInfo.getHeight());
        int cpuQuota = 2000;
        if (reso == EnumResolution.SD) {
            cpuQuota = 1000;
        }
        return cpuQuota;
    }


    private int downloadCost(JobParam jobParam, VideoInfo videoInfo) {
        if (!jobParam.isDownloadInputVideo()) {
            return 0;
        }
        int downloadCost = Long.valueOf(videoInfo.getFileSize() / DOWNLOAD_SPEED).intValue();
        if (downloadCost < 0) {
            downloadCost = 10 * 60;
        }
        return downloadCost;
    }

    private void logAnalysisResult(JobAnalysisParam param, Map<String, Long> quotaSet, VideoInfo videoInfo, long downloadCost, long processCost) {
        logTranscodeAnalysisResult(param, quotaSet, videoInfo, downloadCost, processCost, null, null);
    }


    private void logTranscodeAnalysisResult(JobAnalysisParam param, Map<String, Long> quotaSet, VideoInfo videoInfo,
                                            long downloadCost, long processCost,
                                            TranscodeSpecification specification,
                                            TranscodeResourceCostConfig costConfig) {
        SLSLogInfo slsLogInfo = new SLSLogInfo();
        slsLogInfo.setEngineModel(param.getEngineModel());
        slsLogInfo.setJobId(param.getJobId());
        slsLogInfo.setUserId(param.getUserId());
        slsLogInfo.setEvent("mps_analysis_result");
        slsLogInfo.setFunction("analysisByParseParam");
        slsLogInfo.setTrace(param.getTrace());

        slsLogInfo.getParams().put("videoInfo", JSONObject.toJSONString(videoInfo));
        slsLogInfo.getParams().put("downloadCost", downloadCost);
        slsLogInfo.getParams().put("processCost", processCost);
        slsLogInfo.getParams().put("quotaSet", quotaSet);
        if (specification != null) {
            if (specification.getResolution() != null) {
                slsLogInfo.getParams().put("specReso", specification.getResolution());
            }

            if (specification.getTranscodeCodec() != null) {
                slsLogInfo.getParams().put("specCodec", specification.getTranscodeCodec());
            }

            if (specification.getTranscodeType() != null) {
                slsLogInfo.getParams().put("specType", specification.getTranscodeType());
            }
        }

        if (costConfig != null) {
            slsLogInfo.getParams().put("costConfig", JSONObject.toJSONString(costConfig));
        }

        SLSLogUtil.info(slsLogInfo);
    }


}
