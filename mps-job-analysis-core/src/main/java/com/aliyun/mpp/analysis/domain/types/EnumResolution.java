package com.aliyun.mpp.analysis.domain.types;


/**
 * Created by lihe.lh on 2020/2/11.
 */
public enum EnumResolution {
    _4K(3840, 2160),
    _2K(2560, 1440),
    FHD(1920, 1080),
    HD(1280, 720),
    SD(848, 480),
    LD(640, 360),
    AUDIO(0, 0);


    public int width;
    public int height;
    public int area;

    private EnumResolution(Integer width, Integer height) {
        this.width = width;
        this.height = height;
        this.area = width * height;
    }
    
    public static EnumResolution parseReso(Integer width, Integer height) {
        if (width == null || height == null || width.intValue() == 0 || height.intValue() == 0) {
            return AUDIO;
        }
        int maxEdge = Math.max(width, height);
        int area = width * height;
        if (maxEdge <= LD.width || area <= LD.area) {
            return LD;
        }
        if (maxEdge <= SD.width || area <= SD.area) {
            return SD;
        }
        if (maxEdge <= HD.width || area <= HD.area) {
            return HD;
        }
        if (maxEdge <= FHD.width || area <= FHD.area) {
            return FHD;
        }
        if (maxEdge <= _2K.width || area <= _2K.area) {
            return _2K;
        }
        return _4K;
    }

    public static EnumResolution parseResoByLongShortEgde(Integer width, Integer height) {
        if (width == null || height == null || width.intValue() == 0 || height.intValue() == 0) {
            return AUDIO;
        }
        int longEdge = Math.max(width, height);
        int shortEdge = Math.min(width, height);
        if (longEdge <= LD.width && shortEdge <= LD.height) {
            return LD;
        }
        if (longEdge <= SD.width && shortEdge <= SD.height) {
            return SD;
        }
        if(longEdge <= HD.width && shortEdge <= HD.height) {
            return HD;
        }
        if(longEdge <= FHD.width && shortEdge <= FHD.height) {
            return FHD;
        }
        if(longEdge <= _2K.width && shortEdge <= _2K.height) {
            return _2K;
        }
        return _4K;
    }

    public static void main(String[] args) {
        parseLowNearestResoByArea(1280, 1280);
    }

    public static EnumResolution parseResoByArea(Integer width, Integer height) {
        if (width == null || height == null || width.equals(0) || height.equals(0)) {
            return AUDIO;
        }
        int area = width * height;
        if (area <= LD.area || 1.0f * area / LD.area < 1.0f * SD.area / area) {
            return LD;
        }
        if (area <= SD.area || 1.0f * area / SD.area < 1.0f * HD.area / area) {
            return SD;
        }
        if (area <= HD.area || 1.0f * area / HD.area < 1.0f * FHD.area / area) {
            return HD;
        }
        if (area <= FHD.area || 1.0f * area / FHD.area < 1.0f * _2K.area / area) {
            return FHD;
        }
        if (area <= _2K.area || 1.0f * area / _2K.area < 1.0f * _4K.area / area) {
            return _2K;
        }
        return _4K;
    }

    public static EnumResolution parseLowNearestResoByArea(Integer width, Integer height) {
        return parseNearestReso(width, height, true);
    }

    public static EnumResolution parseHighNearestResoByArea(Integer width, Integer height) {
        return parseNearestReso(width, height, false);
    }

    private static EnumResolution parseNearestReso(Integer width, Integer height, boolean isLower){
        if (width == null || height == null || width.equals(0) || height.equals(0)) {
            return AUDIO;
        }
        int area = width * height;
        if(area == LD.area){
            return LD;
        }
        if(area == SD.area){
            return SD;
        }

        if(area == HD.area){
            return HD;
        }

        if(area == FHD.area){
            return FHD;
        }

        if(area == _2K.area){
            return _2K;
        }

        if(area == _4K.area){
            return _4K;
        }

        if (area < LD.area) {
            return isLower ? AUDIO : LD;
        }else if (area < SD.area) {
            return isLower ? LD : SD;
        } else if (area < HD.area) {
            return isLower ? SD : HD;
        } else if (area < FHD.area) {
            return isLower ? HD : FHD;
        } else if (area < _2K.area) {
            return isLower ? FHD : _2K;
        } else if(area < _4K.area){
            return isLower ? _2K : _4K;
        } else {
            return _4K;
        }
    }
}
