package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

/**
 * Created by lihe.lh on 2021/1/4.
 */
@Data
public class DagJobEdge {
    private String name;
    private String from;
    private String to;
    private FromStatus fromStatus = FromStatus.COMPLETED;
    private EdgeStatus status = EdgeStatus.RUNNING;
    private Mode mode = Mode.PUSH;

    public enum Mode {
        PUSH, PULL
    }

    public enum FromStatus {
        RUNNING, COMPLETED, NOT_QUEUING
    }

    public enum EdgeStatus {
        RUNNING, PAUSE
    }
}
