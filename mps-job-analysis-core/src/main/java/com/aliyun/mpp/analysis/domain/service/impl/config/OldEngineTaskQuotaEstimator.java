package com.aliyun.mpp.analysis.domain.service.impl.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.types.Config;
import com.aliyun.mpp.analysis.domain.types.CustomParam;
import com.aliyun.mpp.analysis.domain.types.EstimateResult;
import com.aliyun.mpp.analysis.domain.types.ScheduleParam;
import com.aliyun.mpp.analysis.util.http.HttpUtil;
import com.aliyun.mpp.analysis.util.log.GlobalVars;
import com.aliyun.mpp.analysis.util.log.SLSLogInfo;
import com.aliyun.mpp.analysis.util.log.SLSLogUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by lihe.lh on 2020/12/7.
 */
@Slf4j
@Component
public class OldEngineTaskQuotaEstimator {
    public static final String CPU = "cpu";
    public static final String GPU = "gpu";
    public static final String GENCODE = "gencode";
    public static final String GMEM = "gmem";
    public static final String RESULT = "result";
    public static final String META = "meta";
    public static final String ANALYSIS = "analysis";
    public static final String EDITING = "editing";
    public static final String SNAPSHOT = "snapshot";
    public static final String PACKAGE = "package";
    public static final String CONVERT_SUBTITLE = "convertSubtitle";
    public static final String TRANSCODE = "transcode";
    public static final String S_265 = "s265";
    public static final String X_265 = "x265";
    public static final String X_264 = "x264";
    public static final String AOM_AV_1 = "aom_av1";
    public static final String COPY = "copy";
    public static final String H_264_NVENC = "h264_nvenc";
    public static final String NH_VERSION = "nhVersion";
    public static final String VIDEO_CODEC = "videoCodec";
    public static final String UHD = "uhd";
    public static final String FRC = "frc";
    public static final String HDR = "hdr";
    public static final String MODE = "mode";
    public static final String DECODE = "decode";
    public static final String WAVESPIC = "wavespic";

    private MetaJobEstimater metaJobEstimater;
    private SnapshotJobEstimater snapshotJobEstimater;
    private PackageJobEstimater packageJobEstimater;
    private CovertSubtitleJobEstimater convertSubtitleJobEstimater;
    private AnalysisJobEstimater analysisJobEstimater;
    private EditingJobEstimater editingJobEstimater;
    private DecodeJobEstimater decodeJobEstimater;
    private WavespicJobEstimater wavespicJobEstimater = new WavespicJobEstimater();

    private TranscodeJobEstimater transcodeNhV1p5JobEstimater;
    private TranscodeJobEstimater transcodeNhV1p6X264JobEstimater;
    private TranscodeJobEstimater transcodeNhV2p0JobEstimater;
    private TranscodeJobEstimater transcodeNhRCANP4JobEstimater;
    private TranscodeJobEstimater transcodeNhUHDV1JobEstimater;

    private TranscodeJobEstimater transcodeUHDV100JobEstimater;
    private TranscodeJobEstimater transcodeUHDV101JobEstimater;
    private TranscodeJobEstimater transcodeFRCV100JobEstimater;
    private TranscodeJobEstimater transcodeHDRV100JobEstimater;

    private TranscodeJobEstimater transcodeUfhUHDV1O1JobEstimater;
    private TranscodeJobEstimater transcodeUfhHDRV100JobEstimater;
    private TranscodeJobEstimater transcodeUfhFRCV100JobEstimater;

    private TranscodeJobEstimater transcodeNhV1p6S265JobEstimater;
    private TranscodeJobEstimater transcodeNormalX265JobEstimater;
    private TranscodeJobEstimater transcodeNormalX264JobEstimater;
    private TranscodeJobEstimater transcodeNormalAV1JobEstimater;
    private TranscodeJobEstimater transcodeNormalH264NvencJobEstimater;
    private TranscodeJobEstimater transcodeNormalAudioJobEstimater;
    private TranscodeJobEstimater transcodeNormalCopyJobEstimater;


    @Resource
    private HttpUtil httpUtil;

    @Resource
    private GlobalVars globalVars;

    private boolean useOldEstimate = true;

    public void init() {
        String url = globalVars.getEngineServiceServerUrl() + "/quotaset/query";
        Map<String, Object> params = new HashMap<>();
        params.put("EngineModel", "mps-transcode");
        params.put("Version", "v3.1");

        /**
         *
         curl http://mpp-engine-service.mpp-pre-controller.svc.cluster.local:8080/quotaset/query -X POST -d '{"EngineModel":"mps-transcode","Version":"v2}'
         */
        for (int i = 0; i < 100; i++) {
            try {
                String data = httpUtil.sendPostRequest(url, params);
                log.info("<EMAIL>, data:" + data);
                JSONObject jsonObject = JSONObject.parseObject(data);
                if ("Success".equals(jsonObject.getString("code"))) {
                    initQuotaConfig(jsonObject.getString("data"));
                    return;
                } else {
                    throw new RuntimeException("fail@EngineQuotaEstimateConfig, invalid response:" + data);
                }
            } catch (Exception e) {
                log.info("fail@EngineQuotaEstimateConfig", e);
                try {
                    TimeUnit.SECONDS.sleep(10);
                } catch (InterruptedException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    public void initQuotaConfig(String data) {
        JSONArray jsonArray = JSON.parseArray(data);
        List<SnapshotEstimateConfig> snapshotEstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeUHDV100EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeUHDV101EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeHDRV100EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeFRCV100EstimateConfigs = new ArrayList<>();

        List<TranscodeEstimateConfig> transcodeUfhUHDV101EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeUfhHDRV100EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeUfhFRCV100EstimateConfigs = new ArrayList<>();


        List<TranscodeEstimateConfig> transcodeNhV1p5EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeNhV1p6X264EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeNhV1p6S265EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeNhV2p0EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeNhRCANP4EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeNormalX265EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeNormalX264EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeNormalAV1EstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeNormalH264NvencEstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeNormalAudioEstimateConfigs = new ArrayList<>();
        List<TranscodeEstimateConfig> transcodeNormalCopyEstimateConfigs = new ArrayList<>();


        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            JSONObject param = jsonObject.getJSONObject("param");
            JSONObject config = param.getJSONObject("config");

            String jobType = config.getString("jobType");
            JSONArray result = jsonObject.getJSONArray(RESULT);
            if (jobType.equals(META)) {
                metaJobEstimater = new MetaJobEstimater(parseQuotaResult(result));
            } else if (jobType.equals(ANALYSIS)) {
                analysisJobEstimater = new AnalysisJobEstimater(parseQuotaResult(result));
            } else if (jobType.equals(PACKAGE)) {
                packageJobEstimater = new PackageJobEstimater(parseQuotaResult(result));
            } else if (jobType.equals(CONVERT_SUBTITLE)) {
                convertSubtitleJobEstimater = new CovertSubtitleJobEstimater(parseQuotaResult(result));
            } else if (jobType.equals(SNAPSHOT)) {
                snapshotEstimateConfigs.add(parseSnapshotQuotaResult(config, result));
            } else if (jobType.equals(EDITING)) {
                editingJobEstimater = new EditingJobEstimater(parseQuotaResult(result));
            } else if (jobType.equals(DECODE)) {
                decodeJobEstimater = new DecodeJobEstimater(parseQuotaResult(result));
            } else if (jobType.equals(TRANSCODE)) {
                String nhVersion = config.getString(NH_VERSION);
                if (StringUtils.isEmpty(nhVersion)) {
                    String videoCodec = config.getString(VIDEO_CODEC);
                    String uhd = config.getString(UHD);
                    String hdr = config.getString(HDR);
                    String frc = config.getString(FRC);
                    String mode = config.getString(MODE);
                    boolean isUfhMode = "uhd-frc-hdr".equals(mode);
                    if(!StringUtils.isEmpty(uhd)){
                        if(isUfhMode){
                            transcodeUfhUHDV101EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                        }else if("1.0.0".equals(uhd)){
                            transcodeUHDV100EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                        }else {
                            transcodeUHDV101EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                        }
                    }else if (!StringUtils.isEmpty(hdr)) {
                        if (isUfhMode) {
                            transcodeUfhHDRV100EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                        } else {
                            transcodeHDRV100EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                        }
                    } else if (!StringUtils.isEmpty(frc)) {
                        if (isUfhMode) {
                            transcodeUfhFRCV100EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                        } else {
                            transcodeFRCV100EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                        }
                    } else if (StringUtils.isEmpty(videoCodec)) {
                        transcodeNormalAudioEstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                    } else if (X_265.equals(videoCodec)) {
                        transcodeNormalX265EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                    } else if (X_264.equals(videoCodec)) {
                        transcodeNormalX264EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                    } else if (AOM_AV_1.equals(videoCodec)) {
                        transcodeNormalAV1EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                    } else if (COPY.equals(videoCodec)) {
                        transcodeNormalCopyEstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                    } else if (H_264_NVENC.equals(videoCodec)) {
                        transcodeNormalH264NvencEstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                    }
                } else if ("1.5".equals(nhVersion)) {
                    transcodeNhV1p5EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                } else if ("1.6".equals(nhVersion)) {
                    String videoCodec = config.getString(VIDEO_CODEC);
                    if (S_265.equals(videoCodec)) {
                        transcodeNhV1p6S265EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                    } else {
                        transcodeNhV1p6X264EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                    }
                } else if ("2.0".equals(nhVersion)) {
                    transcodeNhV2p0EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                } else if ("RCANP4".equals(nhVersion)) {
                    transcodeNhRCANP4EstimateConfigs.add(parseTranscodeQuotaResult(config, result));
                }
            }
        }

        snapshotJobEstimater = new SnapshotJobEstimater(snapshotEstimateConfigs);

        transcodeUHDV101JobEstimater = new TranscodeJobEstimater(transcodeUHDV101EstimateConfigs);
        transcodeUHDV100JobEstimater = new TranscodeJobEstimater(transcodeUHDV100EstimateConfigs);
        transcodeUfhUHDV1O1JobEstimater = new TranscodeJobEstimater(transcodeUfhUHDV101EstimateConfigs);

        transcodeFRCV100JobEstimater = new TranscodeJobEstimater(transcodeFRCV100EstimateConfigs);
        transcodeUfhFRCV100JobEstimater = new TranscodeJobEstimater(transcodeUfhFRCV100EstimateConfigs);

        transcodeHDRV100JobEstimater = new TranscodeJobEstimater(transcodeHDRV100EstimateConfigs);
        transcodeUfhHDRV100JobEstimater = new TranscodeJobEstimater(transcodeUfhHDRV100EstimateConfigs);

        transcodeNhV1p5JobEstimater = new TranscodeJobEstimater(transcodeNhV1p5EstimateConfigs);
        transcodeNhV1p6X264JobEstimater = new TranscodeJobEstimater(transcodeNhV1p6X264EstimateConfigs);
        transcodeNhV1p6S265JobEstimater = new TranscodeJobEstimater(transcodeNhV1p6S265EstimateConfigs);
        transcodeNhV2p0JobEstimater = new TranscodeJobEstimater(transcodeNhV2p0EstimateConfigs);

        transcodeNhRCANP4JobEstimater = new TranscodeJobEstimater(transcodeNhRCANP4EstimateConfigs);
        transcodeNhUHDV1JobEstimater = new TranscodeJobEstimater(transcodeNhRCANP4EstimateConfigs);

        transcodeNormalX265JobEstimater = new TranscodeJobEstimater(transcodeNormalX265EstimateConfigs);
        transcodeNormalX264JobEstimater = new TranscodeJobEstimater(transcodeNormalX264EstimateConfigs);
        transcodeNormalAV1JobEstimater = new TranscodeJobEstimater(transcodeNormalAV1EstimateConfigs);
        transcodeNormalH264NvencJobEstimater = new TranscodeJobEstimater(transcodeNormalH264NvencEstimateConfigs);
        transcodeNormalAudioJobEstimater = new TranscodeJobEstimater(transcodeNormalAudioEstimateConfigs);
        transcodeNormalCopyJobEstimater = new TranscodeJobEstimater(transcodeNormalCopyEstimateConfigs);

    }

    public EstimateResult estimateJob(ScheduleParam scheduleParam) {
        String jobType = scheduleParam.getConfig().getJobType();
        JobEstimater estimater = null;
        if (jobType.equals(META)) {
            estimater = metaJobEstimater;
        } else if (jobType.equals(ANALYSIS)) {
            estimater = analysisJobEstimater;
        } else if (jobType.equals(SNAPSHOT)) {
            estimater = snapshotJobEstimater;
        } else if (jobType.equals(CONVERT_SUBTITLE)) {
            estimater = convertSubtitleJobEstimater;
        } else if (jobType.equals(PACKAGE)) {
            estimater = packageJobEstimater;
        } else if (jobType.equals(EDITING)) {
            estimater = editingJobEstimater;
        } else if (jobType.equals(DECODE)) {
            estimater = decodeJobEstimater;
        } else if (jobType.equals(TRANSCODE)) {
            String nhVersion = scheduleParam.getConfig().getNhVersion();
            if (StringUtils.isEmpty(nhVersion)) {
                String videoCodec = scheduleParam.getConfig().getVideoCodec();
                String uhd = scheduleParam.getConfig().getUhd();
                String frc = scheduleParam.getConfig().getFrc();
                String hdr = scheduleParam.getConfig().getHdr();
                String mode = scheduleParam.getConfig().getMode();
                boolean isUfhMode = "uhd-frc-hdr".equals(mode);
                if (!StringUtils.isEmpty(uhd)) {
                    if(isUfhMode){
                        estimater = transcodeUfhUHDV1O1JobEstimater;
                    }else if("1.0.0".equals(uhd)){
                        estimater = transcodeUHDV100JobEstimater;
                    }else {
                        estimater = transcodeUHDV101JobEstimater;
                    }
                } else if (!StringUtils.isEmpty(hdr)) {
                    if(isUfhMode){
                        estimater = transcodeUfhHDRV100JobEstimater;
                    }else {
                        estimater = transcodeHDRV100JobEstimater;
                    }
                } else if (!StringUtils.isEmpty(frc)) {
                    if(isUfhMode){
                        estimater = transcodeUfhFRCV100JobEstimater;
                    }else {
                        estimater = transcodeFRCV100JobEstimater;
                    }
                } else if (StringUtils.isEmpty(videoCodec)) {
                    estimater = transcodeNormalAudioJobEstimater;
                } else if (X_265.equals(videoCodec)) {
                    estimater = transcodeNormalX265JobEstimater;
                } else if (S_265.equals(videoCodec)) {
                    estimater = transcodeNhV1p6S265JobEstimater;
                } else if (X_264.equals(videoCodec)) {
                    estimater = transcodeNormalX264JobEstimater;
                } else if (COPY.equals(videoCodec)) {
                    estimater = transcodeNormalCopyJobEstimater;
                } else if (AOM_AV_1.equals(videoCodec)) {
                    estimater = transcodeNormalAV1JobEstimater;
                } else if (H_264_NVENC.equals(videoCodec)) {
                    estimater = transcodeNormalH264NvencJobEstimater;
                }
            } else if (nhVersion.equals("1.5")) {
                estimater = transcodeNhV1p5JobEstimater;
            } else if (nhVersion.equals("2.0")) {
                estimater = transcodeNhV2p0JobEstimater;
            } else if (nhVersion.equals("1.6")) {
                estimater = transcodeNhV1p6X264JobEstimater;
            } else if (nhVersion.equals("RCANP4")) {
                estimater = transcodeNhRCANP4JobEstimater;
            } else if (nhVersion.equals("UHDV1")) {
                estimater = transcodeNhUHDV1JobEstimater;
            }
        }else if(jobType.equals(WAVESPIC)){
            estimater = wavespicJobEstimater;
        }

        return estimater.estimate(scheduleParam);
    }


    private SnapshotEstimateConfig parseSnapshotQuotaResult(JSONObject config, JSONArray result) {
        SnapshotEstimateConfig snapshotEstimateConfig = new SnapshotEstimateConfig();

        snapshotEstimateConfig.setSnapshotType(config.getString("snapshotType"));
        snapshotEstimateConfig.setQuotaResult(parseQuotaResult(result));

        return snapshotEstimateConfig;
    }

    private TranscodeEstimateConfig parseTranscodeQuotaResult(JSONObject config, JSONArray result) {
        TranscodeEstimateConfig transcodeEstimateConfig = new TranscodeEstimateConfig();
        transcodeEstimateConfig.setHeight(config.getInteger("height"));
        transcodeEstimateConfig.setWidth(config.getInteger("width"));

        transcodeEstimateConfig.setQuotaResult(parseQuotaResult(result));

        return transcodeEstimateConfig;

    }

    private QuotaResult parseQuotaResult(JSONArray result) {
        QuotaResult quotaResult = new QuotaResult();
        for (int i = 0; i < result.size(); i++) {
            JSONObject jsonObject = result.getJSONObject(i);
            QuotaEstimateResult estimateResult = new QuotaEstimateResult();
            estimateResult.setSpeed(jsonObject.getDouble("speed"));
            estimateResult.setCost(jsonObject.getLong("cost"));

            JSONObject quotaSet = jsonObject.getObject("quotaSet", JSONObject.class);
            estimateResult.setCpu(quotaSet.getLong("cpu"));
            estimateResult.setGpu(quotaSet.getLong("gpu"));
            estimateResult.setGmem(quotaSet.getLong("gmem"));
            estimateResult.setGencode(quotaSet.getLong("gencode"));

            estimateResult.setParallel(jsonObject.getInteger("parallel"));

            quotaResult.add(estimateResult);
        }

        return quotaResult;
    }


}

interface JobEstimater {
    EstimateResult estimate(ScheduleParam scheduleParam);
}

@Data
class MetaJobEstimater implements JobEstimater {
    private QuotaResult quotaResult;

    public MetaJobEstimater(QuotaResult metaQuotaResult) {
        this.quotaResult = metaQuotaResult;
    }

    @Override
    public EstimateResult estimate(ScheduleParam scheduleParam) {
        return quotaResult.getEstimateResults().get(0).estimate(scheduleParam);
    }
}


@Data
class PackageJobEstimater implements JobEstimater {
    private QuotaResult quotaResult;

    public PackageJobEstimater(QuotaResult metaQuotaResult) {
        this.quotaResult = metaQuotaResult;
    }

    @Override
    public EstimateResult estimate(ScheduleParam scheduleParam) {
        return quotaResult.getEstimateResults().get(0).estimate(scheduleParam);
    }
}


@Data
class EditingJobEstimater implements JobEstimater {
    private QuotaResult quotaResult;

    public EditingJobEstimater(QuotaResult metaQuotaResult) {
        this.quotaResult = metaQuotaResult;
    }

    @Override
    public EstimateResult estimate(ScheduleParam scheduleParam) {
        return quotaResult.getEstimateResults().get(0).estimate(scheduleParam);
    }
}

@Data
class DecodeJobEstimater implements JobEstimater {
    private QuotaResult quotaResult;


    public DecodeJobEstimater(QuotaResult metaQuotaResult) {
        this.quotaResult = metaQuotaResult;
    }

    @Override
    public EstimateResult estimate(ScheduleParam scheduleParam) {
        return quotaResult.getEstimateResults().get(0).estimate(scheduleParam);
    }
}

@Data
class WavespicJobEstimater implements JobEstimater {

    @Override
    public EstimateResult estimate(ScheduleParam scheduleParam) {
        EstimateResult estimateResult = new EstimateResult();
        Map<String, Long> quotaSet = new HashMap<>();
        quotaSet.put("cpu", 1000L);
        quotaSet.put("disk", scheduleParam.getInput().getSize() >> 20);
        estimateResult.setQuotaSet(quotaSet);
        estimateResult.setExpectCostTime(Double.valueOf(scheduleParam.getInput().getDuration() / 50).longValue());

        return estimateResult;
    }
}

@Data
class AnalysisJobEstimater implements JobEstimater {
    private QuotaResult quotaResult;

    public AnalysisJobEstimater(QuotaResult metaQuotaResult) {
        this.quotaResult = metaQuotaResult;
    }

    @Override
    public EstimateResult estimate(ScheduleParam scheduleParam) {
        return quotaResult.getEstimateResults().get(0).estimate(scheduleParam);
    }
}

@Data
class CovertSubtitleJobEstimater implements JobEstimater {
    private QuotaResult quotaResult;


    public CovertSubtitleJobEstimater(QuotaResult metaQuotaResult) {
        this.quotaResult = metaQuotaResult;
    }

    @Override
    public EstimateResult estimate(ScheduleParam scheduleParam) {
        return quotaResult.getEstimateResults().get(0).estimate(scheduleParam);
    }
}


@Data
class SnapshotJobEstimater implements JobEstimater {
    private Map<String, SnapshotEstimateConfig> snapshotEstimateConfigs = new HashMap<>();

    public SnapshotJobEstimater(List<SnapshotEstimateConfig> snapshotEstimateConfigs) {
        for (SnapshotEstimateConfig snapshotEstimateConfig : snapshotEstimateConfigs) {
            this.snapshotEstimateConfigs.put(snapshotEstimateConfig.getSnapshotType(), snapshotEstimateConfig);
        }
    }


    @Override
    public EstimateResult estimate(ScheduleParam scheduleParam) {
        String snapshotType = scheduleParam.getConfig().getSnapshotType();
        SnapshotEstimateConfig config = snapshotEstimateConfigs.get(snapshotType);
        return config.estimate(scheduleParam);
    }
}

@Data
class TranscodeJobEstimater implements JobEstimater {
    private List<TranscodeEstimateConfig> estimateConfigs;

    public TranscodeJobEstimater(List<TranscodeEstimateConfig> estimateConfigs) {
        this.estimateConfigs = estimateConfigs;
    }

    @Override
    public EstimateResult estimate(ScheduleParam scheduleParam) {
        if (estimateConfigs.size() == 1) {
            return estimateConfigs.get(0).getQuotaResult().getEstimateResults().get(0).estimate(scheduleParam);
        } else {
            return estimateByReso(scheduleParam);
        }
    }

    private EstimateResult estimateByReso(ScheduleParam scheduleParam) {
        if (estimateConfigs.size() == 1) {
            return estimateConfigs.get(0).getQuotaResult().getEstimateResults().get(0).estimate(scheduleParam);
        } else if (scheduleParam.getConfig().isAudioOnly() && estimateConfigs.size() > 1) {
            //多于1个音频转码配置,先选择第一个
            return estimateConfigs.get(0).getQuotaResult().getEstimateResults().get(0).estimate(scheduleParam);
        } else {
            //查找分辨率完全匹配的
            for (TranscodeEstimateConfig estimateConfig : estimateConfigs) {
                if (estimateConfig.matchReso(scheduleParam.getConfig())) {
                    return estimateConfig.getQuotaResult().getEstimateResults().get(0).estimate(scheduleParam);
                }
            }


            //分辨率没有完全配置
            Integer reso = scheduleParam.getConfig().getHeight() * scheduleParam.getConfig().getWidth();
            TranscodeEstimateConfig curMinUpperResoConfig = null;
            TranscodeEstimateConfig curMaxLowerResoConfig = null;
            Integer curMaxLowerReso = null;
            Integer curMinUpperReso = null;
            for (TranscodeEstimateConfig estimateConfig : estimateConfigs) {
                Integer configReso = estimateConfig.getHeight() * estimateConfig.getWidth();
                if (configReso > reso) {
                    //分辨率大于配置
                    if (curMinUpperReso == null || curMinUpperReso > configReso) {
                        curMinUpperResoConfig = estimateConfig;
                        curMinUpperReso = configReso;
                    }
                } else {
                    //分辨率小于配置
                    if (curMaxLowerReso == null || curMaxLowerReso < configReso) {
                        curMaxLowerResoConfig = estimateConfig;
                        curMaxLowerReso = configReso;
                    }
                }
            }

            if (curMaxLowerResoConfig == null) {
                //低于最小分辨率,没有找到低于scheduleParam要求的分辨率,以音频转码和最低分辨率的视频转码为基准
                EstimateResult result = curMinUpperResoConfig.getQuotaResult().getEstimateResults().get(0).estimate(scheduleParam);
                long upperCpuQuota = result.getQuotaSet().get(OldEngineTaskQuotaEstimator.CPU);
                long audioCpuQuota = 1000L;
                long refineCpuQuota = Double.valueOf(audioCpuQuota + (upperCpuQuota - audioCpuQuota) * Math.sqrt(reso) / Math.sqrt(curMinUpperResoConfig.getHeight() * curMinUpperResoConfig.getWidth())).longValue();
                result.getQuotaSet().put(OldEngineTaskQuotaEstimator.CPU, refineCpuQuota);
                return result;
            } else if (curMinUpperResoConfig == null) {
                //大于最大分辨率，采用最大分辨率的进行预估
                return curMaxLowerResoConfig.getQuotaResult().getEstimateResults().get(0).estimate(scheduleParam);
            } else {

                EstimateResult minUpperResult = curMinUpperResoConfig.getQuotaResult().getEstimateResults().get(0).estimate(scheduleParam);
                EstimateResult maxLowerResult = curMaxLowerResoConfig.getQuotaResult().getEstimateResults().get(0).estimate(scheduleParam);
                long maxLowerCpuQuota = maxLowerResult.getQuotaSet().get(OldEngineTaskQuotaEstimator.CPU);
                long minUpperCpuQuota = minUpperResult.getQuotaSet().get(OldEngineTaskQuotaEstimator.CPU);
                if (minUpperCpuQuota == maxLowerCpuQuota) {
                    return minUpperResult;
                } else {
                    long refineCpuQuota = Double.valueOf(maxLowerCpuQuota + (minUpperCpuQuota - maxLowerCpuQuota) *
                            (Math.sqrt(reso) - Math.sqrt(maxLowerCpuQuota))
                            / (Math.sqrt(curMinUpperReso) - Math.sqrt(maxLowerCpuQuota))).longValue();
                    minUpperResult.getQuotaSet().put(OldEngineTaskQuotaEstimator.CPU, refineCpuQuota);
                    return minUpperResult;

                }
            }
        }
    }
}

@Data
class SnapshotEstimateConfig {
    private String snapshotType;
    private QuotaResult quotaResult;

    public EstimateResult estimate(ScheduleParam scheduleParam) {
        return this.quotaResult.getEstimateResults().get(0).estimate(scheduleParam);
    }
}

@Data
class TranscodeEstimateConfig {
    private Integer width;
    private Integer height;
    private Map<String, Object> extend;

    private QuotaResult quotaResult;

    public boolean matchReso(Config config) {
        if (config.isAudioOnly()) {
            return this.width == null || this.height == null;
        } else if (this.height == null || this.width == null) {
            return false;
        } else {
            return (this.width.intValue() == config.getWidth().intValue()
                    && this.height.intValue() == config.getHeight().intValue())
                    ||
                    (this.width.intValue() == config.getHeight().intValue()
                            && this.height.intValue() == config.getWidth().intValue());
        }
    }
}

@Data
class QuotaResult {
    private List<QuotaEstimateResult> estimateResults = new ArrayList<>();

    public void add(QuotaEstimateResult result) {
        this.estimateResults.add(result);
    }
}


@Data
class QuotaEstimateResult {
    private Integer parallel;
    private Long cpu;
    private Long gpu;
    private Long gencode;
    private Long cost;
    private Double speed;
    private Long gmem;

    private Map<String, Long> quotaSet;

    public EstimateResult estimate(ScheduleParam scheduleParam) {
        EstimateResult result = new EstimateResult();
        if (cost != null) {
            result.setExpectCostTime(Math.max(1, cost / 1000));
        } else {
            result.setExpectCostTime(Double.valueOf(scheduleParam.getInput().getDuration() / speed + 0.5f).longValue());
            if (result.getExpectCostTime().longValue() <= 0L) {
                result.setExpectCostTime(1L);
            }
        }

        if (quotaSet == null) {
            synchronized (this) {
                if (quotaSet == null) {
                    quotaSet = new HashMap<>();
                    quotaSet.put(OldEngineTaskQuotaEstimator.CPU, cpu);
                    quotaSet.put(OldEngineTaskQuotaEstimator.GPU, gpu);
                    quotaSet.put(OldEngineTaskQuotaEstimator.GENCODE, gencode);
                    quotaSet.put(OldEngineTaskQuotaEstimator.GMEM, gmem);
                }
            }
        }
        result.setQuotaSet(new HashMap<>(quotaSet));
        return result;
    }
}




