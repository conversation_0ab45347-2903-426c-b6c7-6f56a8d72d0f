package com.aliyun.mpp.analysis.domain.service.impl.config;

import com.aliyun.mpp.analysis.domain.types.EnumResolution;
import com.aliyun.mpp.analysis.domain.types.EnumTranscodeCodec;
import com.aliyun.mpp.analysis.domain.types.EnumTranscodeType;
import com.aliyun.mpp.analysis.domain.types.TranscodeResourceCostConfig;

/**
 * Created by lihe.lh on 2020/2/11.
 */
public interface TranscodeResourceCostConfigRepository {
    TranscodeResourceCostConfig find(EnumResolution resolution, EnumTranscodeCodec transcodeCodec, EnumTranscodeType transcodeType);


}
