package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

import java.util.Map;


@Data
public class WorkerBrainAnalysisResult {
    private String code;
    private String message;
    private EnumExecuteMode executeMode;
    private boolean useMachineTypeOnly = false;
    private Map<String, TaskResourceRequest> resourceRequests;
    private Map<String,DagJobGraph> graphMap;

    public enum EnumExecuteMode{
        SINGLE, DAG
    }
}
