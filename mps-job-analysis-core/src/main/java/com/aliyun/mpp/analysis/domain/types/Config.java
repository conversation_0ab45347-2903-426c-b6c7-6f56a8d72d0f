package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by lihe.lh on 2020/9/22.
 */
@Data
public class Config {
    private String jobType;
    private String id;
    private Integer width;
    private Integer height;
    private Map<String, Object> extend = new HashMap<>();
    private String mediaType;
    private String videoCodec;
    private String audioCodec;
    private String format;
    private Double duration;
    private Double fps;
    private Double videoBitrate;
    private Double audioBitrate;
    private Integer interval;
    private String resolution;
    private String snapshotType;
    private String nhVersion;
    private Integer parallelNum;
    private String uhd;
    private String hdr;
    private String frc;
    private String mode;
    private String subJobType;
    private Boolean withMultiFiles;
    private Boolean byWorkerBrain;
    private String arch;
    private String templateId;
    private String gpuRestore;

    public void addExtend(String key, Object value){
        extend.put(key, value);
    }

    public boolean isAudioOnly(){
        return this.width == null && this.height == null;
    }
}
