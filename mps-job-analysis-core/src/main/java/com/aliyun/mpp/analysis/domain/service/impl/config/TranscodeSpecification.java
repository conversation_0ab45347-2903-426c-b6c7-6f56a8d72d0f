package com.aliyun.mpp.analysis.domain.service.impl.config;

import com.aliyun.mpp.analysis.domain.types.EnumResolution;
import com.aliyun.mpp.analysis.domain.types.EnumTranscodeCodec;
import com.aliyun.mpp.analysis.domain.types.EnumTranscodeType;
import lombok.Data;

/**
 * Created by lihe.lh on 2020/2/12.
 */
@Data
public class TranscodeSpecification {
    private EnumTranscodeCodec transcodeCodec;
    private EnumTranscodeType transcodeType;
    private EnumResolution resolution;

    public TranscodeSpecification(EnumTranscodeCodec transcodeCodec, EnumTranscodeType transcodeType, EnumResolution resolution) {
        this.transcodeCodec = transcodeCodec;
        this.transcodeType = transcodeType;
        this.resolution = resolution;
    }
}
