package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by lihe.lh on 2020/2/9.
 */
@Data
public class JobAnalysisResult {
    private boolean isSuccess = true;
    private String code = "Success";
    private String message;

    private EnumExecuteMode executeMode;
    private ScheduleParams scheduleParams;
    //executeMode为SINGLE时有效
    private Long expectCostTime;
    private Map<String, Long> quotaSet;
    private Map<String, Long> originQuotaSet;
    private Long slaFinishDelay;
    private Long slaQueuingDelay;
    private Integer maxMigrateRetry;
    private Map<String, Long> migrateDiscardQuotaThreshold;
    private Map<String, TaskResourceRequest> resourceRequests;

    //executeMode为DAG时有效
    private DagJobGraph graph;
    /**
     * 倍速转码时使用 key为speedx
     */
    private Map<String,DagJobGraph> graphMap;

    /**
     * key 为"speedX"时，value 为实际预估能跑的倍速
     */
    private Map<String,Message> speedXMessage;

    public enum EnumExecuteMode{
        PROBE_FIRST, SINGLE, DAG
    }

    public static JobAnalysisResult generateSingleModeResult(Map<String, Long> quotaSet, Long expectCostTime){
        return generateSingleModeResult(quotaSet, expectCostTime, quotaSet);
    }

    public static JobAnalysisResult generateSingleModeResult(Map<String, Long> quotaSet, Long expectCostTime, Map<String, Long> originQuotaSet){
        JobAnalysisResult result = new JobAnalysisResult();
        result.setExecuteMode(EnumExecuteMode.SINGLE);
        result.setQuotaSet(quotaSet);
        result.setExpectCostTime(expectCostTime);
        result.setOriginQuotaSet(new HashMap<>(originQuotaSet));

        return result;
    }
/*
    public static JobAnalysisResult generateDAGModeResult(Map<String, Task> tasks, Map<String, List<String>> dependencies){
        JobAnalysisResult result = new JobAnalysisResult();
        result.setExecuteMode(EnumExecuteMode.DAG);
        result.setTasks(tasks);
        result.setDependencies(dependencies);
        return result;
    }*/



}
