package com.aliyun.mpp.analysis.domain.types;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.net.URI;
import java.net.URLDecoder;

/**
 * Created by lihe.lh on 2020/2/11.
 */
@Data
public class OSSFile implements File{
    private String url;
    private Long userId;
    private String roleArn;
    private String bucket;
    private String object;
    private String location;

    public OSSFile(String url, Long userId, String roleArn){
        this.url = url;
        this.userId = userId;
        this.roleArn = roleArn;

        this.parseHttpAddress(url);
    }

    private void parseHttpAddress(String url) {
        try {
            URI uri = new URI(url);
            String host = uri.getHost();
            String[] components = host.split("\\.");
            this.setBucket(components[0]);
            this.setLocation(components[1]);
            String objectName = uri.getRawPath();
            if(objectName.startsWith("/")){
                objectName = objectName.substring(1);
            }
            this.setObject(URLDecoder.decode(objectName, "UTF-8"));
            this.url = "http://" + host + "/" + this.object;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getType() {
        return "OSS";
    }

    @Override
    public String detail() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url", url);
        jsonObject.put("userId", userId);
        jsonObject.put("roleArn", roleArn);
        jsonObject.put("type", getType());
        return jsonObject.toJSONString();
    }

    @Override
    public boolean isM3u8() {
        return url.endsWith("m3u8");
    }
}
