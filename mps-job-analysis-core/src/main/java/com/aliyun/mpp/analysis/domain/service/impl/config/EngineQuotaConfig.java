package com.aliyun.mpp.analysis.domain.service.impl.config;

import com.alibaba.fastjson.annotation.JSONField;
import com.aliyun.mpp.analysis.domain.types.CustomParam;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

@Data
public class EngineQuotaConfig {
    @JSONField(ordinal = 1)
    private String name;
    @JSONField(ordinal = 2)
    private Map<String, Object> configs;
    @JSONField(ordinal = 3)
    private Map<String, Long> quotaSet;
    @JSONField(ordinal = 4)
    private Long cost;
    @JSONField(ordinal = 5)
    private Double speed;
    @JSONField(ordinal = 6)
    private Double diskRatio = 2.0;
    @JSONField(ordinal = 7)
    private Long diskQuota;
    @JSONField(ordinal = 8)
    private CustomParam customParam;
    @JSONField(ordinal = 9)
    private Integer maxMigrateRetry;
    @JSONField(ordinal = 10)
    private Map<String, Long> migrateDiscardQuotaThreshold;
    @JSONField(ordinal = 11)
    private List<EngineQuotaRefineWithInputConfig> inputConfigs;


    @JSONField(serialize = false)
    public boolean isCustomParamConfig(){
        return customParam != null &&
                (!StringUtils.isEmpty(customParam.getTag())
                        && !StringUtils.isEmpty(customParam.getUserId())
                        && !StringUtils.isEmpty(customParam.getPipelineId()));
    }

}
