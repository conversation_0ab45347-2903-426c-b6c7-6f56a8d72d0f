package com.aliyun.mpp.analysis.util.log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.mpp.analysis.domain.types.Config;
import com.aliyun.mpp.analysis.domain.types.Input;
import com.aliyun.mpp.analysis.util.DateUtil;
import lombok.Data;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.*;

/**
 * Created by lihe.lh on 2020/8/4.
 */
@Data
public class JobResultCollectInfo {
    private String region;
    private String product = "mps";
    private String jobId;
    private String engineModel;
    private String userId;
    private String pipelineId;
    private String tag;
    private String startTime;
    private String finishTime;
    private Float totalTime;
    private List<Output> outputs = new ArrayList<>();
    private JSONArray inputs;
    private JSONArray configs;
    private Map<String, Long> allocQuotaSet;
    private Map<String, Long> avgQuotaSet;
    private Map<String, Long> maxQuotaSet;
    private Long expectCostTime;
    private Long realCostTime;
    private Date createTime;
    private Date submitTime;
    private String event;
    private String currentTime;
    private String env = "center";
    private String station;

    public void addInput(Input input){
        this.inputs.add(input);
    }

    public void addOutput(Output output){
        this.outputs.add(output);
    }

    public void addConfig(Config config){
        this.configs.add(config);
    }

    @Data
    public static class Output {
        private String id;
        private Integer width;
        private Integer height;
        private String videoCodec;
        private String audioCodec;
        private String format;
        private Float duration;
        private Long size;
        private Float fps;
        private Float videoBitrate;
        private Float audioBitrate;
        private String mediaType;
        private String status;
        private String resolution;

        private Float processSpeed;
        private Float processTime;
        private Float totalSpeed;
        private Float totalTime;
        private Float downloadTime;
        private Float uploadTime;

        public JSONObject toLogString(){
            return (JSONObject) JSONObject.toJSON(this);
        }
    }


    public String toLogString(){
        JSONObject jsonObject = new JSONObject();
        if(inputs != null && !inputs.isEmpty()){
            jsonObject.put("input0", inputs.get(0));
            jsonObject.put("inputs", inputs);
        }

        if(outputs != null && !outputs.isEmpty()){
            jsonObject.put("output0", outputs.get(0).toLogString());
            jsonObject.put("outputs", outputs);
        }

        if(configs != null && !configs.isEmpty()){
            jsonObject.put("config0", configs.get(0));
            jsonObject.put("configs", configs);
        }

        jsonObject.put("region", region);
        jsonObject.put("product", product);
        jsonObject.put("jobId", jobId);
        jsonObject.put("engineModel", engineModel);
        jsonObject.put("tag", tag);
        jsonObject.put("userId", userId);
        jsonObject.put("pipelineId", pipelineId);
        jsonObject.put("event", event);
        jsonObject.put("env", env);
        jsonObject.put("station", station);

        if(allocQuotaSet != null){
            for (Map.Entry<String, Long> entry : allocQuotaSet.entrySet()) {
                jsonObject.put("alloc_" + entry.getKey() + "_quota", entry.getValue());
            }
            jsonObject.put("allocQuotaSet", allocQuotaSet);
        }


        if(avgQuotaSet != null){
            for (Map.Entry<String, Long> entry : avgQuotaSet.entrySet()) {
                jsonObject.put("avg_" + entry.getKey() + "_quota", entry.getValue());
            }
            jsonObject.put("avgQuotaSet", avgQuotaSet);
        }


        if(maxQuotaSet != null){
            for (Map.Entry<String, Long> entry : maxQuotaSet.entrySet()) {
                jsonObject.put("max_" + entry.getKey() + "_quota", entry.getValue());
            }
            jsonObject.put("maxQuotaSet", maxQuotaSet);
        }
        jsonObject.put("startTime", startTime);
        jsonObject.put("finishTime", finishTime);
        jsonObject.put("totalTime", totalTime);
        jsonObject.put("expectCostTime", expectCostTime);
        jsonObject.put("realCostTime", realCostTime);
        jsonObject.put("createTime", DateUtil.format(createTime));
        jsonObject.put("submitTime", DateUtil.format(submitTime));
        jsonObject.put("currentTime", DateUtil.format(new Date()));

        return JSON.toJSONString(jsonObject, SerializerFeature.DisableCircularReferenceDetect);
    }
}
