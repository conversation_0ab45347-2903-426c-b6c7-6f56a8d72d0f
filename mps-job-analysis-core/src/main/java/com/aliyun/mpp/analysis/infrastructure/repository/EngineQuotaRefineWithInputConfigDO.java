package com.aliyun.mpp.analysis.infrastructure.repository;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.service.impl.config.EngineQuotaRefineWithInputConfig;
import lombok.Data;

import java.util.Map;

@Data
public class EngineQuotaRefineWithInputConfigDO {
    private String configName;
    private String inputName;
    private String inputs;
    private String quotaSet;
    private Long cost;
    private Double speed;

    public static EngineQuotaRefineWithInputConfigDO toEngineQuotaRefineWithInputDO(EngineQuotaRefineWithInputConfig config){
        if(config == null){
            return null;
        }

        EngineQuotaRefineWithInputConfigDO configDO = new EngineQuotaRefineWithInputConfigDO();
        configDO.setConfigName(config.getConfigName());
        configDO.setInputName(config.getInputName());
        if(config.getInputs() != null) {
            configDO.setInputs(JSONObject.toJSONString(config.getInputs()));
        }
        if(config.getQuotaSet() != null) {
            configDO.setQuotaSet(JSONObject.toJSONString(config.getQuotaSet()));
        }

        configDO.setCost(config.getCost());
        configDO.setSpeed(config.getSpeed());
        return configDO;
    }

    public static EngineQuotaRefineWithInputConfig toEngineQuotaRefineWithInputConfig(EngineQuotaRefineWithInputConfigDO configDO){
        if(configDO == null){
            return null;
        }

        EngineQuotaRefineWithInputConfig config = new EngineQuotaRefineWithInputConfig();
        config.setConfigName(configDO.getConfigName());
        config.setInputName(configDO.getInputName());
        if(configDO.getInputs() != null){
            config.setInputs(JSONObject.parseObject(configDO.getInputs(), Map.class));
        }
        if(configDO.getQuotaSet() != null){
            config.setQuotaSet(EngineQuotaConfigDO.convertToLongValueMap(configDO.getQuotaSet()));
        }
        config.setCost(configDO.getCost());
        config.setSpeed(configDO.getSpeed());

        return config;
    }
}
