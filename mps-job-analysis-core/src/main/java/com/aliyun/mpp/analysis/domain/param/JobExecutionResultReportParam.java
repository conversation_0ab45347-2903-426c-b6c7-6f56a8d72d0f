package com.aliyun.mpp.analysis.domain.param;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * Created by lihe.lh on 2020/7/7.
 */
@Data
public class JobExecutionResultReportParam {
    private String product;
    private String engineModel;
    private String tag;
    private String userId;
    private String jobId;
    private JSONObject trace;
    private String pipelineId;
    private Map<String, Long> allocQuotaSet;
    private Map<String, Long> maxQuotaSet;
    private Map<String, Long> avgQuotaSet;

    private Long expectCostTime;
    private Long realCostTime;
    private Object resultData;
    private String requestId;
    private String engineParams;
    private Date createTime;
    private Date submitTime;
    private String env;
    private String station;
}
