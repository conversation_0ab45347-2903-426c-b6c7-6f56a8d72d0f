package com.aliyun.mpp.analysis.util.log;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * Created by lihe.lh on 2019/12/9.
 */
@Component
@Data
public class GlobalVars {
    @Value("${app.region}")
    private String region;

    @Value("${app.engine.serviceUrl}")
    private String engineServiceServerUrl;

    @Value("${app.dmes.serviceUrl}")
    private String dmesServiceUrl;

    @Value("${app.sla.serviceUrl}")
    private String slaServiceUrl;

    @Value("${app.media.serviceBucketList}")
    private List<String> serviceBucketList;


    @Value("${app.media.defaultDiskTimesForMultiTranscode}")
    private Long defaultDiskTimesForMultiTranscode;

    @Value("${app.media.maxDiskForMultiTranscode}")
    private Long maxDiskForMultiTranscode;
    
    @Value("${app.media.minDiskForMultiTranscode}")
    private Long minDiskForMultiTranscode;

    @Value("${app.worker_brain.mpsTranscodeNewUrl}")
    private String workerBrainMpsTranscodeNewUrl;

    @Value("${app.worker_brain.mpsEditingUrl}")
    private String workerBrainMpsEditingUrl;

    public boolean isServiceBucket(String bucket){
        return serviceBucketList != null && serviceBucketList.contains(bucket);
    }

}
