package com.aliyun.mpp.analysis.infrastructure.repository;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.service.impl.config.EngineQuotaConfig;
import com.aliyun.mpp.analysis.domain.types.CustomParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@Data
public class EngineQuotaConfigDO {
    private String name;
    private String configs;
    private String quotaSet;
    private Long cost;
    private Double speed;
    private Double diskRatio;
    private Long diskQuota;
    private String customParam;
    private Integer maxMigrateRetry;
    private String migrateDiscardQuotaThreshold;

    public static EngineQuotaConfigDO toEngineQuotaConfigDO(EngineQuotaConfig config){
        if(config == null){
            return null;
        }
        EngineQuotaConfigDO configDO = new EngineQuotaConfigDO();
        configDO.setName(config.getName());
        configDO.setCost(config.getCost());
        configDO.setSpeed(config.getSpeed());
        configDO.setQuotaSet(JSONObject.toJSONString(config.getQuotaSet()));
        configDO.setConfigs(JSONObject.toJSONString(config.getConfigs()));
        configDO.setDiskQuota(config.getDiskQuota());
        configDO.setDiskRatio(config.getDiskRatio());
        if(config.getCustomParam() != null) {
            configDO.setCustomParam(JSONObject.toJSONString(config.getCustomParam()));
        }
        configDO.setMaxMigrateRetry(config.getMaxMigrateRetry());
        if(config.getMigrateDiscardQuotaThreshold() != null) {
            configDO.setMigrateDiscardQuotaThreshold(JSONObject.toJSONString(config.getMigrateDiscardQuotaThreshold()));
        }

        return configDO;
    }

    public static EngineQuotaConfig toEngineQuotaConfig(EngineQuotaConfigDO configDO){
        if(configDO == null){
            return null;
        }
        try {
            EngineQuotaConfig config = new EngineQuotaConfig();
            config.setName(configDO.getName());
            config.setConfigs(JSONObject.parseObject(configDO.getConfigs(), Map.class));
            config.setQuotaSet(EngineQuotaConfigDO.convertToLongValueMap(configDO.getQuotaSet()));
            config.setCost(configDO.getCost());
            config.setSpeed(configDO.getSpeed());
            config.setDiskRatio(configDO.getDiskRatio());
            config.setDiskQuota(configDO.getDiskQuota());
            if (!StringUtils.isEmpty(configDO.getCustomParam())) {
                config.setCustomParam(JSONObject.parseObject(configDO.getCustomParam(), CustomParam.class));
            }
            config.setMaxMigrateRetry(configDO.getMaxMigrateRetry());
            if (!StringUtils.isEmpty(configDO.getMigrateDiscardQuotaThreshold())) {
                config.setMigrateDiscardQuotaThreshold(EngineQuotaConfigDO.convertToLongValueMap(configDO.getMigrateDiscardQuotaThreshold()));
            }

            return config;
        }catch (Exception e){
            log.error("fail@toEngineQuotaConfig, configDO:{}", JSONObject.toJSONString(configDO));
        }
        return null;
    }


    private static Map<String, Long> convertToLongValueMap(Map param){
        if(param == null){
            return null;
        }
        Map<String, Long> result = new HashMap<>();
        for (Object o : param.keySet()) {
            String key = String.valueOf(o);
            Object value = param.get(o);
            if(value instanceof Long){
                result.put(key, (Long)value);
            }else if(value instanceof Integer){
                result.put(key, ((Integer) value).longValue());
            }
        }
        return result;
    }


    public static Map<String, Long> convertToLongValueMap(String param){
        Map<String, Object> objectMap = JSONObject.parseObject(param, Map.class);
        return convertToLongValueMap(objectMap);
    }



}
