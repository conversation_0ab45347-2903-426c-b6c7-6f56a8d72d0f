package com.aliyun.mpp.analysis.infrastructure.repository;

import com.aliyun.mpp.analysis.domain.service.impl.config.EngineQuotaConfig;
import com.aliyun.mpp.analysis.domain.service.impl.config.EngineQuotaConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository
@Slf4j
public class IbatisEngineQuotaConfigRepository implements EngineQuotaConfigRepository {
    @Resource
    private EngineQuotaConfigMapper engineQuotaConfigMapper;

    @Override
    public List<EngineQuotaConfig> loadAllConfigs(){
        List<EngineQuotaConfigDO> configDOS = engineQuotaConfigMapper.loadAllConfigs();
        List<EngineQuotaConfig> result = new ArrayList<>();
        for (EngineQuotaConfigDO configDO : configDOS) {
            result.add(EngineQuotaConfigDO.toEngineQuotaConfig(configDO));
        }
        return result;
    }

    @Override
    public EngineQuotaConfig queryByName(String name) {
        return EngineQuotaConfigDO.toEngineQuotaConfig(engineQuotaConfigMapper.queryByName(name));
    }

    @Override
    public boolean create(EngineQuotaConfig config) {
        try{
            engineQuotaConfigMapper.create(EngineQuotaConfigDO.toEngineQuotaConfigDO(config));
            return true;
        }catch (DuplicateKeyException e){
            return false;
        }
    }

    @Override
    public void update(EngineQuotaConfig config) {
        engineQuotaConfigMapper.update(EngineQuotaConfigDO.toEngineQuotaConfigDO(config));
    }

}
