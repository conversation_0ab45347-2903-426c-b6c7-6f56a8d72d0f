package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

import java.util.Map;

/**
 * Created by lihe.lh on 2021/1/4.
 */
@Data
public class DagJobVertex {
    private String taskId;
    private String name;
    private String engineModel;
    private String engineParams;
    private ScheduleParams scheduleParams;
    private TaskResourceRequest resourceRequest;
    private Integer maxMigrateRetry;
    private Map<String, Long> migrateDiscardQuotaThreshold;
}
