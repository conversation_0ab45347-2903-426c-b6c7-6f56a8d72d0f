package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

import java.util.Map;

/**
 * Created by lihe.lh on 2019/11/26.
 */
@Data
public class TaskResourceRequest {
    private Map<String, Long> quotaSet;

    private Long expectCostTime;
    private String engineParams;

    public TaskResourceRequest(){

    }

    public TaskResourceRequest(Map<String, Long> quotaSet, Long expectCostTime){
        this.quotaSet = quotaSet;
        this.expectCostTime = expectCostTime;
    }

}
