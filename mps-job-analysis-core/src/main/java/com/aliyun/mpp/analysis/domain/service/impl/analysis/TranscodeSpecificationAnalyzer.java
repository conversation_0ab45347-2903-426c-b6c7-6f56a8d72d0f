package com.aliyun.mpp.analysis.domain.service.impl.analysis;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.service.impl.config.MemTranscodeResourceCostConfigRepository;
import com.aliyun.mpp.analysis.domain.service.impl.config.TranscodeResourceCostConfigRepository;
import com.aliyun.mpp.analysis.domain.service.impl.config.TranscodeSpecification;
import com.aliyun.mpp.analysis.domain.types.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;


/**
 * Created by lihe.lh on 2020/2/12.
 */
@Slf4j
@Component
public class TranscodeSpecificationAnalyzer {
    public static TranscodeSpecification analysis(JobParam jobParam, VideoInfo videoInfo) {
        Pair<Integer, Integer> reso = parseTranscodeReso(jobParam, videoInfo);
        EnumTranscodeType transcodeType = parseTranscodeType(jobParam, videoInfo);
        EnumTranscodeCodec transcodeCodec = parseTranscodeCodec(jobParam, videoInfo);
        return new TranscodeSpecification(transcodeCodec, transcodeType, EnumResolution.parseReso(reso.getLeft(), reso.getRight()));
    }


    public static Long refineTranscodeCpuQuota(TranscodeSpecification specification, JobParam jobParam, VideoInfo videoInfo, TranscodeResourceCostConfigRepository costConfigRepository) {
        if (specification.getTranscodeType() == EnumTranscodeType.NH_V2) {
            return null;
        }
        if (specification.getTranscodeCodec() == EnumTranscodeCodec.COPY || specification.getTranscodeCodec() == EnumTranscodeCodec.NVENC) {
            return null;
        }

        Pair<Integer, Integer> reso = parseTranscodeReso(jobParam, videoInfo);
        EnumResolution refinedResolution = EnumResolution.parseResoByArea(reso.getLeft(), reso.getRight());
        if (refinedResolution == EnumResolution.AUDIO) {
            return null;
        } else {
            TranscodeResourceCostConfig costConfig = costConfigRepository.find(refinedResolution, specification.getTranscodeCodec(), specification.getTranscodeType());
            Long refinedQuota = Double.valueOf(1.0 * costConfig.getCpu() * reso.getLeft() * reso.getRight() / (refinedResolution.height * refinedResolution.width)).longValue();
            return Math.min(refinedQuota, 16000);
        }
    }

//    public static Integer refineTranscodeCpuQuota(TranscodeSpecification specification, JobParam jobParam, VideoInfo videoInfo, TranscodeResourceCostConfigRepository costConfigRepository) {
//        if (specification.getTranscodeType() == EnumTranscodeType.NH_V2) {
//            return null;
//        }
//        if (specification.getTranscodeCodec() == EnumTranscodeCodec.COPY || specification.getTranscodeCodec() == EnumTranscodeCodec.NVENC) {
//            return null;
//        }
//
//        //quota优化，基于输出分辨率面积调整quota
//        //(1)先计算转码输出对应分辨率对应标准分辨率（audio，ld，sd，hd，2k，4k)中最邻近的两个(不大于中最大的，不小于中最小的）；
//        //(2)如果 lowNearestReso 和 highNearestReso 相同，则表示完全匹配，不需要调整；
//        //(3)如果不同，则线性调优，具体如下：
//        // a. 设定函数 f(x) = y, 其中x为分辨率面积，y为对应的cpu quota；
//        // b. 即 f(lowNearestReso.area) = lowNearestReso.cpuQuota, f(highNearestReso.area) = highNearestReso.cpuQuota
//        // c. 线性调整,(f(reso.area) - f(lowNearestReso.area))/(reso.area - lowNearestReso.area)
//        // = (f(highNearestReso.area) - f(lowNearestReso.area))/(highNearestReso.area - lowNearestReso.area)，
//        // d. 继而, f(reso.area) = f(lowNearestReso.area) + (reso.area - lowNearestReso.area)
//        // * (f(highNearestReso.area) - f(lowNearestReso.area))/(highNearestReso.area - lowNearestReso.area)
//        Pair<Integer, Integer> reso = parseTranscodeReso(jobParam, videoInfo);
//        EnumResolution lowNearestReso = EnumResolution.parseLowNearestResoByArea(reso.getLeft(), reso.getRight());
//        EnumResolution highNearestReso = EnumResolution.parseHighNearestResoByArea(reso.getLeft(), reso.getRight());
//        if (lowNearestReso == highNearestReso) {
//            return null;
//        } else {
//            TranscodeResourceCostConfig lowResoCostConfig = costConfigRepository.find(lowNearestReso, specification.getTranscodeCodec(), specification.getTranscodeType());
//            TranscodeResourceCostConfig highResoCostConfig = costConfigRepository.find(highNearestReso, specification.getTranscodeCodec(), specification.getTranscodeType());
//            Integer lowCpuCost = lowResoCostConfig.getCpu();
//            Integer highCpuCost = highResoCostConfig.getCpu();
//            Integer lowResoArea = lowNearestReso.area;
//            Integer highResoArea = highNearestReso.area;
//            Integer curResoArea = reso.getLeft() * reso.getRight();
//
//            if(highCpuCost.equals(lowCpuCost)){
//                return null;
//            }
//
//            Double refinedQuota = 1.0 * (curResoArea - lowResoArea) * (highCpuCost - lowCpuCost) / (highResoArea - lowResoArea)
//                    + lowCpuCost;
//
//            return Math.min(refinedQuota.intValue(), 16000);
//        }
//    }

    public static void main(String[] args) {
        MemTranscodeResourceCostConfigRepository costConfigRepository = new MemTranscodeResourceCostConfigRepository();
        costConfigRepository.initConfigs();
        Pair<Integer, Integer> reso = new ImmutablePair<>(1282, 720);
        EnumResolution lowNearestReso = EnumResolution.parseLowNearestResoByArea(reso.getLeft(), reso.getRight());
        EnumResolution highNearestReso = EnumResolution.parseHighNearestResoByArea(reso.getLeft(), reso.getRight());
        log.info("info@refineTranscodeCpuQuota, outputReso:" + reso + ", lowNearestReso:" + lowNearestReso + ", highNearestReso:" + highNearestReso);
        if (lowNearestReso == highNearestReso) {
            return;
        } else {
            TranscodeResourceCostConfig lowResoCostConfig = costConfigRepository.find(lowNearestReso, EnumTranscodeCodec.H_264, EnumTranscodeType.NORMAL);
            TranscodeResourceCostConfig highResoCostConfig = costConfigRepository.find(highNearestReso, EnumTranscodeCodec.H_264, EnumTranscodeType.NORMAL);
            Long lowCpuCost = lowResoCostConfig.getCpu();
            Long highCpuCost = highResoCostConfig.getCpu();
            Integer lowResoArea = lowNearestReso.area;
            Integer highResoArea = highNearestReso.area;
            Integer curResoArea = reso.getLeft() * reso.getRight();
            if(highCpuCost.equals(lowCpuCost)){
                return;
            }

            double refinedQuota = 1.0 * (curResoArea - lowResoArea) * (highCpuCost - lowCpuCost) / (highResoArea - lowResoArea)
                        + lowCpuCost;
            System.out.println(refinedQuota);
        }
    }

    public static EnumTranscodeCodec parseTranscodeCodec(JobParam jobParam, VideoInfo videoInfo) {
        if ("nvenc".equals(jobParam.getTranscodeTaskType())) {
            return EnumTranscodeCodec.NVENC;
        }
        if (jobParam.isSystemDynamicTranscode()) {
            return EnumTranscodeCodec.H_264;
        }
        JSONObject transcodeVideoConfig = jobParam.getTranscodeVideoConfig();
        if (transcodeVideoConfig != null && transcodeVideoConfig.getBooleanValue("copy")) {
            return EnumTranscodeCodec.COPY;
        }
        if (transcodeVideoConfig == null || videoInfo.isAudioOnly()) {
            return EnumTranscodeCodec.H_264;
        }

        String videoCodec = transcodeVideoConfig.getString("codec");
        if (!StringUtils.isEmpty(videoCodec)) {
            if ("H.265".equals(videoCodec)) {
                return EnumTranscodeCodec.H_265;
            } else if ("AV1".equals(videoCodec)) {
                return EnumTranscodeCodec.AV1;
            } else {
                return EnumTranscodeCodec.H_264;
            }
        } else {
            return EnumTranscodeCodec.H_264;
        }
    }

    private static List<String> DnCNN_KEYS = Arrays.asList("DnCNNP4", "DnCNN", "RCANP4", "UHDV1");

    private static boolean isDnCNN(JSONObject transcodeVideoConfig) {
        if (transcodeVideoConfig == null) {
            return false;
        }
        for (String dnCNN_key : DnCNN_KEYS) {
            if (transcodeVideoConfig.containsKey(dnCNN_key)) {
                return true;
            }
        }
        return false;
    }

    public static EnumTranscodeType parseTranscodeType(JobParam jobParam, VideoInfo videoInfo) {
        if (jobParam.isSystemDynamicTranscode()) {
            return EnumTranscodeType.NORMAL;
        }
        JSONObject transcodeVideoConfig = jobParam.getTranscodeVideoConfig();
        if (transcodeVideoConfig == null || videoInfo.isAudioOnly()) {
            return EnumTranscodeType.NORMAL;
        }
        if (transcodeVideoConfig != null && isDnCNN(transcodeVideoConfig)) {
            return EnumTranscodeType.NH_V2;
        }

        String videoParams = transcodeVideoConfig.getString("params");

        if (videoParams != null && videoParams.contains("sharp")) {
            return EnumTranscodeType.NH_V1;
        } else {
            return EnumTranscodeType.NORMAL;
        }
    }

    public static Pair<Integer, Integer> parseTranscodeReso(JobParam jobParam, VideoInfo videoInfo) {
        if (jobParam.isSystemDynamicTranscode()) {
            return parseSystemDynamicTranscodeReso(jobParam);
        }
        JSONObject transcodeVideoConfig = jobParam.getTranscodeVideoConfig();
        if (transcodeVideoConfig == null || videoInfo.isAudioOnly()) {
            return new ImmutablePair<>(EnumResolution.AUDIO.width, EnumResolution.AUDIO.height);
        }

        JSONObject transcodeTransConfig = jobParam.getTranscodeTransConfig();
        boolean isCheckVideoReso = (transcodeTransConfig != null && transcodeTransConfig.getBooleanValue("isCheckReso"));
        boolean isSwap = (transcodeTransConfig != null
                && "longShort".equals(transcodeTransConfig.getString("resoMode")))
                && (videoInfo.getWidth() < videoInfo.getHeight());
        Integer videoOutputHeight = transcodeVideoConfig.getInteger("height");
        Integer videoOutputWidth = transcodeVideoConfig.getInteger("width");
        if (videoOutputHeight == null && videoOutputWidth == null) {
            videoOutputHeight = videoInfo.getHeight();
            videoOutputWidth = videoInfo.getWidth();
        } else if (videoOutputHeight == null && videoOutputWidth != null) {
            if (isSwap) {
                videoOutputHeight = videoOutputWidth;
                videoOutputWidth = videoOutputHeight * videoInfo.getWidth() / videoInfo.getHeight();
            } else {
                videoOutputHeight = videoOutputWidth * videoInfo.getHeight() / videoInfo.getWidth();
            }
        } else if (videoOutputHeight != null && videoOutputWidth == null) {
            if (isSwap) {
                videoOutputWidth = videoOutputHeight;
                videoOutputHeight = videoOutputWidth * videoInfo.getHeight() / videoInfo.getWidth();
            } else {
                videoOutputWidth = videoOutputHeight * videoInfo.getWidth() / videoInfo.getHeight();
            }
        }

        if (isCheckVideoReso && (videoInfo.getWidth() < videoOutputWidth)) {
            videoOutputHeight = videoInfo.getHeight();
            videoOutputWidth = videoInfo.getWidth();
        }
        videoOutputHeight = videoOutputHeight >> 1 << 1;
        videoOutputWidth = videoOutputWidth >> 1 << 1;
        return new ImmutablePair<>(videoOutputWidth, videoOutputHeight);
    }

    private static Pair<Integer, Integer> parseSystemDynamicTranscodeReso(JobParam jobParam) {
        String processLogic = jobParam.getTranscodeProcessLogic();
        if (processLogic.contains("xld")) {
            return new ImmutablePair(480, 270);
        } else if (processLogic.contains("ld")) {
            return new ImmutablePair<>(EnumResolution.LD.width, EnumResolution.LD.height);
        } else if (processLogic.contains("sd")) {
            return new ImmutablePair<>(EnumResolution.SD.width, EnumResolution.SD.height);
        } else if (processLogic.contains("hd")) {
            return new ImmutablePair<>(EnumResolution.HD.width, EnumResolution.HD.height);
        } else {
            return new ImmutablePair<>(EnumResolution.FHD.width, EnumResolution.FHD.height);
        }
    }

}
