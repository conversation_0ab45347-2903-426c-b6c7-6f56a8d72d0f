package com.aliyun.mpp.analysis.domain.service.impl.config;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

@Data
public class EngineQuotaRefineWithInputConfig {
    @JSONField(ordinal = 1)
    private String configName;
    @JSONField(ordinal = 2)
    private String inputName;
    @JSONField(ordinal = 3)
    private Map<String, Object> inputs;
    @JSONField(ordinal = 4)
    private Map<String, Long> quotaSet;
    @JSONField(ordinal = 4)
    private Long cost;
    @JSONField(ordinal = 5)
    private Double speed;


}
