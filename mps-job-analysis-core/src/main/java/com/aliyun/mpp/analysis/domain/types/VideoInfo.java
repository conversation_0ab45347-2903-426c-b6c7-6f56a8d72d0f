package com.aliyun.mpp.analysis.domain.types;

import lombok.Data;

/**
 * Created by lihe.lh on 2020/2/11.
 */
@Data
public class VideoInfo {
    private Integer width;
    private Integer height;
    //单位秒
    private Double duration;
    private String formatName;
    private Double bitrate;
    //单位B
    private Long fileSize;
    //单位kbps
    private Double videoBitrate;
    //单位kbps
    private Double audioBitrate;
    private String videoCodecName;
    private String audioCodecName;
    private Double fps;
    private File file;
    private boolean isConstructByProbe = true;

    public boolean isAudioOnly(){
        return this.width == null || this.height == null || this.width.intValue() == 0 || this.height.intValue() == 0;
    }

    public boolean isVideoOnly(){
        return audioCodecName == null || audioBitrate == null;
    }

    public static VideoInfo generateDefaultVideoInfo(){
        VideoInfo videoInfo = new VideoInfo();
        videoInfo.setWidth(DEFAULT_WIDTH);
        videoInfo.setHeight(DEFAULT_HEIGHT);
        videoInfo.setFps(30.0);
        videoInfo.setFileSize(DEFAULT_FILE_SIZE);
        videoInfo.setDuration(DEFAULT_DURATION);
        return videoInfo;
    }

    //默认分辨率
    public static int DEFAULT_WIDTH = 1280;

    public static int DEFAULT_HEIGHT = 720;

    //默认码率1Mbps(1024kbps),单位 kbps
    public static double DEFAULT_BIT_RATE = 1024;
    //默认文件时长，1小时
    public static double DEFAULT_DURATION = 3600;
    //默认文件大小，时长*帧率
    public static long DEFAULT_FILE_SIZE = Double.valueOf(DEFAULT_BIT_RATE * DEFAULT_DURATION / 8).longValue();
}
