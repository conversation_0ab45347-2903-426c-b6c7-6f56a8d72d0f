/******************************************/
/*   DatabaseName = mpp_tc_job_mps   */
/*   TableName = mps_engine_quota_config   */
/******************************************/
CREATE TABLE `mps_engine_quota_config` (
                                           `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `gmt_create` datetime NOT NULL COMMENT '创建时间',
                                           `gmt_modified` datetime NOT NULL COMMENT '修改时间',
                                           `name` varchar(64) NOT NULL COMMENT '名称',
                                           `configs` varchar(1024) NOT NULL COMMENT '配置',
                                           `quota_set` varchar(128) NOT NULL COMMENT '任务多维度quota',
                                           `cost` bigint unsigned DEFAULT NULL COMMENT '耗时',
                                           `speed` decimal(8,4) DEFAULT NULL COMMENT '处理速度',
                                           `disk_ratio` decimal(8,4) DEFAULT NULL COMMENT '磁盘quota与片源时长的比例',
                                           `disk_quota` bigint unsigned DEFAULT NULL COMMENT '磁盘quota',
                                           `custom_param` varchar(1024) DEFAULT NULL COMMENT '业务配置，任务tag、用户、管道',
                                           `max_migrate_retry` int unsigned DEFAULT '5' COMMENT '调度迁移最大重试次数',
                                           `migrate_discard_quota_threshold` varchar(128) DEFAULT NULL COMMENT '迁移丢弃quota阈值，当运行时quota超过当前值时，不再迁移任务',
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=135 DEFAULT CHARSET=utf8mb3 COMMENT='mps引擎quota配置表'
;
