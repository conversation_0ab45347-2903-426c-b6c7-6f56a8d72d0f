#!/bin/bash

APP_NAME=mps-job-analysis
APP_HOME=/home/<USER>/${APP_NAME}
APP_TGZ_FILE=${APP_NAME}.tgz
HEALTH_CHECK_FILE=${APP_HOME}/status.ok

app_pid=0
is_stop=0

extract_tgz() {
    local tgz_path="$1"
    local dir_path="$2"
    echo "tgz_path ${tgz_path}"
    echo "dir_path ${dir_path}"
    cd "${APP_HOME}/target" || exit1
    rm -rf "${dir_path}" || exit1
    tar xzf "${tgz_path}" || exit1
    # in order to support fat.jar, unzip it.
    test -f "${dir_path}.jar" && unzip -q "${dir_path}.jar" -d "${dir_path}"
    test -d "${dir_path}" || die "no directory: ${dir_path}"
    touch --reference "${tgz_path}" "${tgz_path}.timestamp" || exit1
}

update_target() {
    local tgz_name="$1"
    local dir_name="$2"

    local tgz_path="${APP_HOME}/target/${tgz_name}"
    local dir_path="${APP_HOME}/target/${dir_name}"

    local error=0
    # dir exists
    if [ -d "${dir_path}" ]; then
          local need_tar=0
          if [ ! -e "${tgz_path}.timestamp" ]; then
              need_tar=1
          else
              local tgz_time=$(stat -L -c "%Y" "${tgz_path}")
              local last_time=$(stat -L -c "%Y" "${tgz_path}.timestamp")
              if [ $tgz_time -gt $last_time ]; then
                  need_tar=1
              fi
          fi
          # tgz is new - extract_tgz
          if [ "${need_tar}" -eq 1 ]; then
              extract_tgz "${tgz_path}" "${dir_path}"
          fi
    else
        extract_tgz "${tgz_path}" "${dir_path}"
    fi

    return $error
}

start() {
  mkdir -p "${APP_HOME}/target" || exit1
  mkdir -p "${APP_HOME}/logs" || exit1

  update_target "${APP_TGZ_FILE}" "${APP_NAME}"

  java `echo $JAVA_OPTS` -jar /${APP_HOME}/target/mps-job-analysis.jar &
  app_pid="$!"
  sleep 30
  touch ${HEALTH_CHECK_FILE}
}

function stop_client()
{
    is_stop=1
    kill "$app_pid"
}

function destroy()
{
    if [ $app_pid -ne 0 ]; then
        rm -rf ${HEALTH_CHECK_FILE}
        sleep 30
        echo 'start stop client'
        stop_client
        wait "$app_pid"
    fi
    exit 0
}

function handle_INT()
{
    echo '### received signal : SIGINT. destroying'
    destroy
}

function handle_TERM()
{
    echo '### received signal : SIGTERM. destroying'
    destroy
}

function check_process()
{
    PROC_NAME=${APP_NAME}
    ProcNumber=`ps -ef |grep -w $PROC_NAME|grep -v log| grep -v grep|wc -l`
    if [ $ProcNumber -le 0 ];then
       echo "process is not run. wait 1 second and start."
       sleep 1
       start_client
     fi
}


trap 'handle_INT' SIGINT
trap 'handle_TERM' SIGTERM

start

while :
do
    if [ $is_stop -eq 0 ];then
        check_process
    fi

    sleep 5
done