#!/bin/bash

SHHOME=$(cd `dirname $0`; pwd)
BASEHOME=$(cd $SHHOME/..; pwd)

if [ -z "$1" ]; then
    APPNAME="*"
else
    APPNAME="$1"
fi

# 开启coredump
# ulimit -c unlimited

# JAVA 默认路径
JAVA=/opt/taobao/java/bin/java
DEFAULT_LIBRARY_PATH=$BASEHOME/lib

TARFILE=`ls $BASEHOME/target/*.tgz`
if [ ! -z "$TARFILE" ] && [ -f "$TARFILE" ]; then
    cd $BASEHOME/target
    tar -zxvf *.tgz
    cd -
fi

# SpringBoot Jar默认路径
JAR=`find $BASEHOME/target -name "$APPNAME.jar"`
if [ -z "$JAR" ] || [ ! -f "$JAR" ]; then
    echo "cannot found jar in $BASEHOME/target"
    exit 1
fi

cd $BASEHOME

test -f $JAVA || JAVA=`which java`
if [ -z "$JAR" ] || [ ! -f $JAVA ]; then
    echo "please install jdk1.8."
    exit 1
fi

parse_options() {
  if [ -f "$1" ]; then
    echo "`grep "^-" "$1" | tr '\n' ' '`"
  fi
}

if [ ! -z $DEFAULT_LIBRARY_PATH ]; then
    export LD_LIBRARY_PATH=${DEFAULT_LIBRARY_PATH}
fi


#if [ -f "/home/<USER>/xxx/disk/application.properties" ]; then
#    cp -f /home/<USER>/xxx/disk/application.properties $BASEHOME/conf/application.properties
#fi

JVM_OPTIONS=${SHHOME}/jvm.options

JAVA_OPTS="`parse_options "$JVM_OPTIONS"` $JAVA_OPTS"
SPRINGBOOT_OPTS="-jar $JAR"
#SPRINGBOOT_OPTS="${SPRINGBOOT_OPTS} --spring.config.location=$BASEHOME/conf/application.properties"
#不再使用，等级太高，无法被配置文件配置覆盖
#SPRINGBOOT_OPTS="${SPRINGBOOT_OPTS} --server.port=7001 --management.port=7002"
SPRINGBOOT_OPTS="${SPRINGBOOT_OPTS} --management.info.build.mode=full --startup.at=$(($(date +%s%N)/1000000))"
SPRINGBOOT_OPTS="${SPRINGBOOT_OPTS} --logging.path=${BASEHOME}/logs --logging.file=${BASEHOME}/logs/application.log"
SPRINGBOOT_OPTS="${SPRINGBOOT_OPTS} --spring.profiles.active=cloud"


if [ ! -z "${JVM_HEAP_SIZE}" ] ;then
    JAVA_OPTS="${JAVA_OPTS} -Xms${JVM_HEAP_SIZE} -Xmx${JVM_HEAP_SIZE}"
else
    # use memory based on the available resources in the machine
    let memTotal=`cat /proc/meminfo | grep MemTotal | awk '{printf "%d", $2/1024*0.75 }'`
    if [ ${memTotal} -gt 5500 ]; then
        JAVA_OPTS="${JAVA_OPTS} -Xms4g -Xmx4g"
    elif [ ${memTotal} -gt 3500 ]; then
        JAVA_OPTS="${JAVA_OPTS} -Xmx2g"
    else
        JAVA_OPTS="${JAVA_OPTS} -Xmx512m"
    fi
fi

if [ ! -z "${JVM_MIN_HEAP_SIZE}" ];then
    JAVA_OPTS="${JAVA_OPTS} -Xms${JVM_MIN_HEAP_SIZE}"
fi

if [ ! -z "${JVM_MAX_HEAP_SIZE}" ];then
    JAVA_OPTS="${JAVA_OPTS} -Xmx${JVM_MAX_HEAP_SIZE}"
fi

# add nacos support ref: https://github.com/alibaba/spring-cloud-alibaba/wiki/Nacos-config
if [ -n "${APP_NACOS_ADDR}" ];then
    JAVA_OPTS="${JAVA_OPTS} -Dspring.cloud.nacos.config.server-addr=${APP_NACOS_ADDR}"
    JAVA_OPTS="${JAVA_OPTS} -Dspring.cloud.nacos.discovery.server-addr=${APP_NACOS_ADDR}"
fi

# add jasypt support ref: https://github.com/ulisesbocchio/jasypt-spring-boot
if [ -n "${APP_SM_ID}" ];then
    JAVA_OPTS="${JAVA_OPTS} -Djasypt.encryptor.password=${APP_SM_ID}"
fi

STARTUP_SLEEP_TIME=10
# manual parsing to find out, if process should be detached
if ! echo $* | grep -E '(^-d |-d$| -d |--daemonize$|--daemonize )' > /dev/null; then
  exec \
    "$JAVA" \
    $JAVA_OPTS \
    $SPRINGBOOT_OPTS \
    "$@"
else
  nohup \
    "$JAVA" \
    $JAVA_OPTS \
    $SPRINGBOOT_OPTS \
    "$@"
    > /dev/null 2>&1 &
  retval=$?
  pid=$!
  [ $retval -eq 0 ] || exit $retval
  if [ ! -z "$STARTUP_SLEEP_TIME" ]; then
    sleep $STARTUP_SLEEP_TIME
  fi
  if ! ps -p $pid > /dev/null ; then
    echo "start failed..."
    exit 1
  fi
  exit 0
fi

exit $?

