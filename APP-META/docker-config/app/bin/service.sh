#!/bin/bash

SHHOME=$(cd `dirname $0`; pwd)
BASEHOME=$(cd $SHHOME/..; pwd)

if [ -z "$1" ]; then
    APPNAME="*"
else
    APPNAME="$1"
fi

# 开启coredump
# ulimit -c unlimited

# JAVA 默认路径
JAVA=/opt/taobao/java/bin/java
DEFAULT_LIBRARY_PATH=$BASEHOME/lib

TARFILE=`ls $BASEHOME/target/*.tgz`
if [ ! -z "$TARFILE" ] && [ -f "$TARFILE" ]; then
    cd $BASEHOME/target
    tar -zxvf *.tgz
    cd -
fi

# SpringBoot Jar默认路径
JAR=`find $BASEHOME/target -name "$APPNAME.jar"`
if [ -z "$JAR" ] || [ ! -f "$JAR" ]; then
    echo "cannot found jar in $BASEHOME/target"
    exit 1
fi

cd $BASEHOME

test -f $JAVA || JAVA=`which java`
if [ -z "$JAR" ] || [ ! -f $JAVA ]; then
    echo "please install jdk1.8."
    exit 1
fi

if [ ! -z $DEFAULT_LIBRARY_PATH ]; then
    export LD_LIBRARY_PATH=${DEFAULT_LIBRARY_PATH}
fi

JAVA_OPTS="${JAVA_OPTS} -server -Xms4g -Xmx4g -XX:PermSize=256m -XX:MaxPermSize=256m -Xmn2g -XX:MaxDirectMemorySize=1g -XX:SurvivorRatio=10 -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSMaxAbortablePrecleanTime=5000 -XX:+CMSClassUnloadingEnabled -XX:CMSInitiatingOccupancyFraction=80 -XX:+UseCMSInitiatingOccupancyOnly -XX:+ExplicitGCInvokesConcurrent -Dsun.rmi.dgc.server.gcInterval=2592000000 -Dsun.rmi.dgc.client.gcInterval=2592000000 -XX:ParallelGCThreads=4 -Xloggc:/home/<USER>/logs/gc.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/home/<USER>/logs/java.hprof -Djava.awt.headless=true -Dsun.net.client.defaultConnectTimeout=10000 -Dsun.net.client.defaultReadTimeout=30000 -DJM.LOG.PATH=/home/<USER>/logs -DJM.SNAPSHOT.PATH=/home/<USER>/snapshots"

# add nacos support ref: https://github.com/alibaba/spring-cloud-alibaba/wiki/Nacos-config
if [ -n "${APP_NACOS_ADDR}" ];then
    JAVA_OPTS="${JAVA_OPTS} -Dspring.cloud.nacos.config.server-addr=${APP_NACOS_ADDR}"
    JAVA_OPTS="${JAVA_OPTS} -Dspring.cloud.nacos.discovery.server-addr=${APP_NACOS_ADDR}"
fi

# add jasypt support ref: https://github.com/ulisesbocchio/jasypt-spring-boot
if [ -n "${APP_SM_ID}" ];then
    JAVA_OPTS="${JAVA_OPTS} -Djasypt.encryptor.password=${APP_SM_ID}"
fi


if [ -n "${APP_TRACER_ENDPOINT}" ] && [ "${APP_TRACER_ENDPOINT}" != "nil" ] ;then
    JAVA_OPTS="${JAVA_OPTS} -javaagent:${SHHOME}/opentelemetry-javaagent.jar"
    JAVA_OPTS="${JAVA_OPTS} -Dotel.resource.attributes=service.name=${APP_NAME}"
    JAVA_OPTS="${JAVA_OPTS} -Dotel.exporter.otlp.endpoint=${APP_TRACER_ENDPOINT}"
    JAVA_OPTS="${JAVA_OPTS} -Dotel.exporter.otlp.headers=Authentication=${APP_TRACER_TOKEN}"
    JAVA_OPTS="${JAVA_OPTS} -Dotel.metrics.exporter=none"
fi

SPRINGBOOT_OPTS="-jar $JAR"
#exec java `echo $JAVA_OPTS` -jar "$JAR"

STARTUP_SLEEP_TIME=10
# manual parsing to find out, if process should be detached
if ! echo $* | grep -E '(^-d |-d$| -d |--daemonize$|--daemonize )' > /dev/null; then
  exec \
    "$JAVA" \
    $JAVA_OPTS \
    $SPRINGBOOT_OPTS \
    "$@"
else
  nohup \
    "$JAVA" \
    $JAVA_OPTS \
    $SPRINGBOOT_OPTS \
    "$@"
    > /dev/null 2>&1 &
  retval=$?
  pid=$!
  [ $retval -eq 0 ] || exit $retval
  if [ ! -z "$STARTUP_SLEEP_TIME" ]; then
    sleep $STARTUP_SLEEP_TIME
  fi
  if ! ps -p $pid > /dev/null ; then
    echo "start failed..."
    exit 1
  fi
  exit 0
fi

exit $?