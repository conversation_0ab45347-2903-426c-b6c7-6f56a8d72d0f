#!/bin/bash

SHHOME=$(cd `dirname $0`; pwd)
SCNAME=$(basename $0)

STAGE="$1"

used=$(free | awk '/^Mem:/{printf "%.0f", $3*100/$2}')
if [ "$used" -ge 80 ]; then
  echo "low memory: used  ${used}% "
else
  echo "memory : used ${used}% "
fi

URL="http://127.0.0.1:8080/health"

HEALTH_CHECK_CODE=$(curl -s --connect-timeout 1 --max-time 2 "${URL}" -o /dev/null -w %{http_code})

if [ X"$HEALTH_CHECK_CODE" == X"200" ]; then
    echo "Health check ok, status:[$HEALTH_CHECK_CODE]..."
    exit 0
elif [ X"$HEALTH_CHECK_CODE" == X"404" ]; then
    echo "Health api is unimplemented, status:[$HEALTH_CHECK_CODE]..."
    exit 0
else
    echo "Health check failed, status:[$HEALTH_CHECK_CODE]"
    exit 1
fi
