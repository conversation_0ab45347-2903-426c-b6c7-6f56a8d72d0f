language: java
desc:
name: $APP_NAME

service:
  registry:
    enable: true

dependencies:
  supervisord:
    enable: true
    config: /home/<USER>/$name/supervisor.ini
  nginx:
    enable: false
    config: /home/<USER>/$name/nginx-proxy.conf

command:
  start:
    pre: /home/<USER>/$name/bin/pre_start.sh
    post: /home/<USER>/$name/bin/post_start.sh
    health: /home/<USER>/$name/bin/health.sh
  stop:
    pre: /home/<USER>/$name/bin/pre_stop.sh
    post: echo "post-stop"

