// 完整的配置及字段说明请参考 https://yuque.antfin-inc.com/docs/share/583ad8c2-01e9-4d6a-9fec-15a2d1038448?# 《serverless IaC模型解释》
// 命令行工具使用参考 https://yuque.antfin-inc.com/docs/share/f145b1de-5e82-46f8-af56-5dbc4629b68c?# 《gitops 命令行工具》
// CUE 常见问题 https://yuque.antfin-inc.com/docs/share/16718ca5-ed76-4dc9-b4f5-aaf833c000d9?# 《cue一百问》
//
package main

import (
    "encoding/json"
	"gitlab.alibaba-inc.com/aone-oss/serverless-iac/serverless"
	ctr "gitlab.alibaba-inc.com/aone-oss/serverless-iac/container"
	r "gitlab.alibaba-inc.com/aone-oss/serverless-iac/resource" // 定义别名为r, 避免包名与字段名一样造成循环引用
	"gitlab.alibaba-inc.com/aone-oss/serverless-iac/workloadtype"
  //wls "gitlab.alibaba-inc.com/ali-mpp/serverless-iac/workloadSettings"
)
//rms业务预发不能配置污点，不采用eci部署
ASI_BY_ECI?: _

//单独声明ilogtail sidecar配置
#LogtailSidecar: ctr.#Sidecar & {
  name: "logtail"
  //image: "registry-vpc."+APPSTACK_REGION+".aliyuncs.com/log-service/logtail:latest"
  if APPSTACK_REGION == "me-east-1" {
      image: "registry-me-central-1.ack.aliyuncs.com/acs/logtail:v1.8.7.0-aliyun"
    }
  if APPSTACK_REGION != "me-east-1" {
      image: "registry-"+APPSTACK_REGION+"-vpc.ack.aliyuncs.com/acs/logtail:v1.8.7.0-aliyun"
    }
  env: [
    {name: "IS_SIDECAR", value: "true"},
    {name: "SIGMA_IGNORE_RESOURCE", value: "true"},
    {name: "ALIYUN_LOGTAIL_CONFIG",value:"/etc/ilogtail/conf/"+APPSTACK_REGION+"-internet/ilogtail_config.json"},
    //仅需要修改下面两个字段配置, 其他全部保持不变COPY至cue即可
    {name: "ALIYUN_LOGTAIL_USER_ID", value: "1426105496228119"},
    {name: "APP_NAME", value: APPSTACK_APP_NAME},
    {name: "APPSTACK_STAGE", value: APPSTACK_STAGE},
    {name: "ALIYUN_LOGTAIL_USER_DEFINED_ID", value: ALIYUN_LOGTAIL_USER_DEFINED_ID},
	{name: "ALIYUN_LOG_ENV_TAGS", value: "CSE_INSTANCE_ID|CSE_POD_IP|CSE_POD_NAMESPACE|CSE_NODE_IP|CSE_NODE_NAME|APP_NAME"},
    ]
  // 资源
  resource: r.#Schema & {
    cpu:    "1"
    memory: "1G"
  }
}

//serverless.#Service你可以理解为java里面的一个class定义，myApp是这个class的具体object
myApp: serverless.#Service & {
    sidecar: [#LogtailSidecar]
	// 主进程容器
	mainContainer: {
	    //cmd: ["sleep","3600"]
		cmd: ["/home/<USER>/start.sh"] // 主进程启动命令
		args: ["standalone"] // 主进程启动参数

		env: [
				{name: "JAVA_OPTS", value: JAVA_OPTS},
				{name: "APP_REGION", value: APP_REGION},
				{name: "DATA_SOURCE_URL", value: DATA_SOURCE_URL},
				{name: "DMES_SERVICE_URL", value: DMES_SERVICE_URL},
				{name: "ENGINE_SERVICE_URL", value: ENGINE_SERVICE_URL},
				{name: "SERVICE_BUCKET_LIST", value: SERVICE_BUCKET_LIST},
				{name: "SLA_SERVICE_URL", value: SLA_SERVICE_URL},
				{name: "WORKER_BRAIN_MPS_EDITING_URL", value: WORKER_BRAIN_MPS_EDITING_URL},
				{name: "WORKER_BRAIN_MPS_TRANSCODE_NEW_URL", value: WORKER_BRAIN_MPS_TRANSCODE_NEW_URL},
		]

		// 配置存活、就绪和启动探测器，请根据实际情况修改下面的配置，文档参考
		// https://kubernetes.io/zh/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
		// 启动探针, 启动探针探测成功之后存活探测才开始工作
		startupProbe: {
			initialDelaySeconds: 30 // 容器启动后要等待多少秒后存活和就绪探测器才被初始化
			periodSeconds:       10 // 执行探测的时间间隔（单位是秒）。默认是 10 秒。最小值是 1。
			timeoutSeconds:      1  // 探测的超时后等待多少秒。默认值是 1 秒。最小值是 1。
			successThreshold:    1  // 探测器在失败后，被视为成功的最小连续成功数。默认值是 1。存活探测的这个值必须是 1。最小值是 1。
			failureThreshold:    300000  // 探测失败阀值，存活探测超过阀值意味着重新启动容器。就绪探测情况下认为未就绪
            exec: [ "/bin/sh", "-c", "/home/<USER>/health.sh"]
		}

		// 存活探针
		livenessProbe: {
			initialDelaySeconds: 30 // 容器启动后要等待多少秒后存活和就绪探测器才被初始化
			periodSeconds:       5  // 执行探测的时间间隔（单位是秒）。默认是 10 秒。最小值是 1。
			timeoutSeconds:      1  // 探测的超时后等待多少秒。默认值是 1 秒。最小值是 1。
			successThreshold:    1  // 探测器在失败后，被视为成功的最小连续成功数。默认值是 1。存活探测的这个值必须是 1。最小值是 1。
			failureThreshold:    3  // 探测失败阀值，存活探测超过阀值意味着重新启动容器。就绪探测情况下认为未就绪
			exec: [ "/bin/sh", "-c", "/home/<USER>/health.sh"]
		}

		// 就绪探针
		readinessProbe: {
			initialDelaySeconds: 30 // 容器启动后要等待多少秒后存活和就绪探测器才被初始化
			periodSeconds:       10 // 执行探测的时间间隔（单位是秒）。默认是 10 秒。最小值是 1。
			timeoutSeconds:      1  // 探测的超时后等待多少秒。默认值是 1 秒。最小值是 1。
			successThreshold:    1  // 探测器在失败后，被视为成功的最小连续成功数。默认值是 1。存活探测的这个值必须是 1。最小值是 1。
			failureThreshold:    3  // 探测失败阀值，存活探测超过阀值意味着重新启动容器。就绪探测情况下认为未就绪
      exec: [ "/bin/sh", "-c", "/home/<USER>/health.sh"]
		}
	}

	// 实例数和实例规格
	// 不同环境等级使用不用配置
	// APPSTACK_ENV_LEVEL 为环境内置标签名, 参考 https://yuque.antfin-inc.com/docs/share/9a1d662d-0498-48da-8da1-548034d69246#558a5489
	// 规格请参考: https://yuque.antfin-inc.com/cnp/gitops/gp6v01#aj8Zt
	if APPSTACK_ENV_LEVEL == "staging-ncloud" {
		replica:  2
		resource: r.#Large // 使用 resource 包下预定义的规格
	}

	if APPSTACK_ENV_LEVEL == "staging1-ncloud" {
    		replica:  2
    		resource: r.#Large // 使用 resource 包下预定义的规格
    	}

	if APPSTACK_ENV_LEVEL == "staging2-ncloud" {
		replica:  2
		resource: r.#Large // 使用 resource 包下预定义的规格
	}

	if APPSTACK_ENV_LEVEL == "production-ncloud" {
		replica:  2
		resource: r.#LargeX // 使用 resource 包下预定义的规格
	}

	// 其他环境等级用以下配置
  // 注意不要漏写 if 对应的 else 部分，否则推导时没匹配上的环境会报配置缺失错误，另外 cue  不支持 else
  if APPSTACK_ENV_LEVEL != "production-ncloud" &&  APPSTACK_ENV_LEVEL != "staging-ncloud" &&  APPSTACK_ENV_LEVEL!= "staging1-ncloud" &&  APPSTACK_ENV_LEVEL!= "staging2-ncloud" {
  	replica:  2
    resource: r.#Large
  }

  if MPP_TC_ANALYSIS_SLB != _|_ {
			slb: {
				slbInstanceId: MPP_TC_ANALYSIS_SLB
//				forceOverrideListeners: "true"
				annotations: {
          "service.beta.kubernetes.io/alibaba-cloud-loadbalancer-health-check-type": "http" // 将SLB的健康检查类型设置为http类型
          "service.beta.kubernetes.io/alibaba-cloud-loadbalancer-health-check-method": "get"
          "service.beta.kubernetes.io/alibaba-cloud-loadbalancer-health-check-uri": "/service/liveness"
        }
				ports: [
					{
						name:       "mpp-tc-analysis-slb"
						protocol:   "TCP"
						port:       8080
						targetPort: 8080
					},
				]
			}
  }

   if ASI_BY_ECI != _|_ {
             //污点配置
             exclusiveResource: { multipleExclusiveResource: "mpp.aliyun.com/product=mps,sigma.ali/resource-pool=mcp,virtual-kubelet.io/provider=alibabacloud" }


             //acrInstanceId: string
             podAnnotations: {

                 "k8s.aliyun.com/eci-instance-type": "ecs.c6.xlarge"
                 "k8s.aliyun.com/eci-custom-tags": "product:mps,biz:mpp,engine:mps-job-analysis"
                 "alibabacloud.com/user-elastic-cluster": "true"
                  // Set memory request （只有加了这句话才能让 Memory request 生效）
//                 "k8s.aliyun.com/eci-container-resource-view": "true"

                 // 注入 pod annotation  https://yuque.antfin-inc.com/docs/share/583ad8c2-01e9-4d6a-9fec-15a2d1038448?#Av2nC
                 //	"k8s.aliyun.com/eci-use-specs": "ecs.g7se.6xlarge,ecs.g7.6xlarge,ecs.g6.6xlarge,ecs.c5.xlarge,24-96Gi"
                 "k8s.aliyun.com/eci-extra-ephemeral-storage": "200Gi"
             }
         }

	// 重建升级：按需取消注释，谨慎使用，直播某些业务不支持重建升级。
	//迪拜发布 更新发布策略为InPlaceIfPossible
	workloadType: workloadtype.#CloneSet
    	workloadSettings: {
    		spec: json.Marshal({
    			// CloneSet类型支持 ReCreate（强制重建发布）、InPlaceIfPossible（尽量原地升级发布）、InPlaceOnly（只原地升级）
    			updateStrategy: "InPlaceIfPossible"
    			maxSurge: "100%"
    			maxUnavailable: "50%"
    			cloneSetMode: "community"
    		})
    	}

	// 发布策略
	releaseStrategy: {
		betaReplicas: 1            // beta实例数. 优先发布beta实例, 然后再按照stepWeight实例数继续发布
		stepWeight:   50           // 流量百分比, 每批最小实例是1个。例如设置20，会按5批发，但是总共只有3个实例，最终会按三批发(每批最小实例是1个)
		pausePolicy:  "firstPause" // 暂停策略，manual：每批暂停 | firstPause：只首批暂停 | auto：每批都不暂停
		interval:     "10s"        // auto时每批发布间隔时间. 30s, 10m, 1h等.  默认20s.
	}

	// 应用部署拓扑, 至少2个可用区  https://aliyuque.antfin.com/cnp/gitops/gp6v01#cUcQr
    	// 排除迪拜
    	if  APPSTACK_ENV_TAG != "mpp-vod-me-prd" {
    	    appZoneTopology: {
                instanceSpreadType: "default"
                whenUnsatisfiable:  "doNotSchedule"
                maxSkew:            1
                minTopologyValues:  2
            }
    	}
	volumesConflict: true // 挂载路径冲突时以volumes为准, 默认 false
	// volume 挂载
	// 可选，请根据实际情况配置
	volumes: [
		{
			mounts: [
				{
					container: "main" // 所有容器
					path:      "/home/<USER>/mps-job-analysis/logs"
					readOnly:  false
				},
				{
					container: "logtail" // 所有容器
					path:      "/home/<USER>/mpp-traffic/logs"
					readOnly:  false
				},
			]
			emptyDir: {
			}
		},
		{
			mounts: [
				{
					container: "*" // 所有容器
					path:      "/home/<USER>/logs"
					readOnly:  false
				},
			]
			emptyDir: {
			}
		}]

	timezone: "Asia/Shanghai" // 时区
}
