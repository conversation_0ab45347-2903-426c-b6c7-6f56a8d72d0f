package com.aliyun.mpp.analysis.test.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.service.impl.analysis.SliceSnapshotSupportAnalyzer;
import com.aliyun.mpp.analysis.domain.types.JobAnalysisResult;
import com.aliyun.mpp.analysis.domain.types.JobParam;
import com.aliyun.mpp.analysis.domain.types.VideoInfo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Created by lihe.lh on 2020/2/13.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SliceSnapshotSupportAnalyzerTest extends BaseTest {
    @Resource
    private SliceSnapshotSupportAnalyzer sliceSnapshotSupportAnalyzer;

    @Test
    public void testSupportSliceSnapshot() {
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, SNAPSHOT);
        JSONObject customParams = new JSONObject();
        addSnapshoByInterval(customParams);

        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setHeight(480);
        videoInfo.setHeight(640);
        videoInfo.setDuration(60 * 60.0);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        Assert.assertTrue(sliceSnapshotSupportAnalyzer.supportSliceSnapshot(jobParam, videoInfo));
    }

    @Test
    public void testNotSupportSliceSnapshotForShortVideo() {
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, SNAPSHOT);
        JSONObject customParams = new JSONObject();
        addSnapshoByInterval(customParams);

        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setHeight(480);
        videoInfo.setHeight(640);
        videoInfo.setDuration(60.0);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        Assert.assertFalse(sliceSnapshotSupportAnalyzer.supportSliceSnapshot(jobParam, videoInfo));
    }

   /* @Test
    public void testGenerateSliceTranscodeDAG() {
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, SNAPSHOT);
        JSONObject customParams = new JSONObject();
        addSnapshoByInterval(customParams);

        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setHeight(480);
        videoInfo.setHeight(640);
        videoInfo.setDuration(60 * 60.0);
        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());

        JobAnalysisResult result = sliceSnapshotSupportAnalyzer.generateSliceSnapshotDAG(jobParam, videoInfo);
        Assert.assertTrue(result.isSuccess());
    }
*/

}
