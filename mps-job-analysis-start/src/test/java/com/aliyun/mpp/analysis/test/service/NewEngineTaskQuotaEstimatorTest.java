package com.aliyun.mpp.analysis.test.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.service.impl.JobAnalysisServiceImpl;
import com.aliyun.mpp.analysis.domain.service.impl.config.EngineQuotaConfig;
import com.aliyun.mpp.analysis.domain.service.impl.config.EngineQuotaConfigRepository;
import com.aliyun.mpp.analysis.domain.service.impl.config.EngineQuotaRefineWithInputRepository;
import com.aliyun.mpp.analysis.domain.service.impl.config.NewEngineTaskQuotaEstimator;
import com.aliyun.mpp.analysis.domain.types.CustomParam;
import com.aliyun.mpp.analysis.domain.types.EstimateResult;
import com.mysql.jdbc.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class NewEngineTaskQuotaEstimatorTest {
    @InjectMocks
    private NewEngineTaskQuotaEstimator newEngineTaskQuotaEstimator;

    @Mock
    private EngineQuotaConfigRepository engineQuotaConfigRepository;
    @Mock
    private EngineQuotaRefineWithInputRepository engineQuotaRefineWithInputRepository;

    @Before
    public void before(){
        Mockito.when(engineQuotaConfigRepository.loadAllConfigs()).thenReturn(constructLoadAllConfigs());
        newEngineTaskQuotaEstimator.init();
    }
    @Test
    public void testS265Transcode(){
        JSONObject jsonObject = JSONObject.parseObject("{\"configs\":[{\"arch\":\"cpu\",\"audioBitrate\":64.0,\"audioCodec\":\"fdk_aac\",\"audioOnly\":false,\"duration\":14.49,\"extend\":{},\"format\":\"mp4\",\"fps\":30.0,\"height\":1920,\"id\":\"ab7bf419dae443e49184a900c0b2af0f\",\"jobType\":\"transcode\",\"videoCodec\":\"s265\",\"width\":1920}],\"inputs\":[{\"audioBitrate\":196.401,\"audioCodec\":\"aac\",\"avgFPS\":30.0,\"duration\":14.49,\"format\":\"QuickTime / MOV\",\"fps\":30.0,\"height\":1080,\"size\":20964141,\"videoBitrate\":11389.071,\"videoCodec\":\"h264\",\"width\":1080}]}");
        EstimateResult result = newEngineTaskQuotaEstimator.analysis(jsonObject, null);
        Assert.assertTrue(result.getName().equals("transcode_720p_s265"));
    }

    @Test
    public void testVpxTranscode(){
        JSONObject jsonObject = JSONObject.parseObject("{\"configs\":[{\"audioBitrate\":128.0,\"audioCodec\":\"opus\",\"audioOnly\":true,\"duration\":81.732,\"extend\":{},\"format\":\"webm\",\"fps\":25.0,\"id\":\"d9635965bf0f4486bc9e63bda6b32405\",\"jobType\":\"transcode\",\"videoBitrate\":5000.0,\"videoCodec\":\"vpx\"}],\"inputs\":[{\"audioBitrate\":64.085,\"audioCodec\":\"aac\",\"avgFPS\":25.0,\"duration\":81.732,\"format\":\"QuickTime / MOV\",\"fps\":25.0,\"height\":480,\"size\":5419054,\"videoBitrate\":459.788,\"videoCodec\":\"h264\",\"width\":854}]}");
        EstimateResult result = newEngineTaskQuotaEstimator.analysis(jsonObject, null);
        Assert.assertTrue(result.getName().equals("transcode_vpx"));
    }

    @Test
    public void testWebpTranscode(){
        JSONObject jsonObject = JSONObject.parseObject("{\"configs\":[{\"audioOnly\":false,\"duration\":38.674,\"extend\":{},\"format\":\"webp\",\"fps\":27.681742,\"height\":960,\"id\":\"4367f87c0976432b80f43620ddf8ada5\",\"jobType\":\"transcode\",\"videoCodec\":\"webp\",\"width\":540}],\"inputs\":[{\"audioBitrate\":48.016,\"audioCodec\":\"aac\",\"avgFPS\":27.681742,\"duration\":38.674,\"format\":\"QuickTime / MOV\",\"fps\":90000.0,\"height\":1280,\"size\":8110392,\"videoBitrate\":1621.978,\"videoCodec\":\"h264\",\"width\":720}]}");
        EstimateResult result = newEngineTaskQuotaEstimator.analysis(jsonObject, null);
        Assert.assertTrue(result.getName().equals("transcode_webp"));
    }

    @Test
    public void testNullInputSizeForTranscode(){
        JSONObject jsonObject = JSONObject.parseObject("{\"configs\":[{\"audioCodec\":\"fdk_aac\",\"audioOnly\":false,\"extend\":{},\"format\":\"m3u8\",\"fps\":30.0,\"height\":1138,\"jobType\":\"transcode\",\"videoCodec\":\"copy\",\"width\":640}],\"inputs\":[{\"duration\":44840.795995,\"fps\":30.0,\"height\":640,\"videoCodec\":\"yuv\",\"width\":360}]}");
        EstimateResult result = newEngineTaskQuotaEstimator.analysis(jsonObject, null);
        Assert.assertTrue(result.getName().equals("transcode_copy"));
    }

    private List<EngineQuotaConfig> constructLoadAllConfigs() {
        List<EngineQuotaConfig> result = new ArrayList<>();
        try {
            BufferedReader reader = new BufferedReader(new InputStreamReader(getClass().getResourceAsStream("/mps_engine_quota_config.csv")));
            String line;
            int count = 0;
            while ((line = reader.readLine()) != null) {
                String[] data = line.split("\t");
                if (count == 0){
                    count ++;
                    continue;
                } else {
                    count ++;
                    try {
                        EngineQuotaConfig config = new EngineQuotaConfig();
                        config.setName(data[3]);
                        config.setInputConfigs(null);
                        config.setConfigs(JSON.parseObject(data[4], Map.class));
                        config.setQuotaSet(JSON.parseObject(data[5], Map.class));
                        if(!StringUtils.isNullOrEmpty(data[6])){
                            config.setCost(Long.valueOf(data[6]));
                        }
                        if(!StringUtils.isNullOrEmpty(data[7])){
                            config.setSpeed(Double.valueOf(data[7]));
                        }
                        if(!StringUtils.isNullOrEmpty(data[8])){
                            config.setDiskRatio(Double.valueOf(data[8]));
                        }
                        if(!StringUtils.isNullOrEmpty(data[9])){
                            config.setDiskQuota(Long.valueOf(data[9]));
                        }
                        if(!StringUtils.isNullOrEmpty(data[10])){
                            config.setCustomParam(JSON.parseObject(data[10], CustomParam.class));
                        }
                        if(!StringUtils.isNullOrEmpty(data[11])){
                            config.setMaxMigrateRetry(Integer.valueOf(data[11]));
                        }

                        result.add(config);
                    }catch (Exception e){
                        log.error("fail@constructLoadAllConfigs", e);
                    }

                }
            }
        } catch (Exception e){
            log.error("fail@constructLoadAllConfigs", e);
            Assert.fail(e.getMessage());
        }
        return result;

    }

}
