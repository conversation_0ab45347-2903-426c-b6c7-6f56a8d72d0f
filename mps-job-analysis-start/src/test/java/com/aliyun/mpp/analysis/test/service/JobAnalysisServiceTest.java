package com.aliyun.mpp.analysis.test.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.service.JobAnalysisService;
import com.aliyun.mpp.analysis.domain.types.Config;
import com.aliyun.mpp.analysis.domain.types.Input;
import com.aliyun.mpp.analysis.domain.types.JobAnalysisResult;
import com.aliyun.mpp.analysis.domain.types.ScheduleParams;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.sql.SQLOutput;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by lihe.lh on 2020/2/12.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class JobAnalysisServiceTest extends BaseTest{
    @Resource
    private JobAnalysisService jobAnalysisService;


    @Test
    public void testTranscodeMetaEstimate(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, "meta");
        String jobParam = constructTranscodeJobParam(params);
        JobAnalysisParam param = constructParam(jobParam);
        JobAnalysisResult result = jobAnalysisService.analysisByParseParam(param);
        Assert.assertTrue(result.getExpectCostTime().equals(1L));
        Assert.assertTrue(result.getQuotaSet().equals(Collections.singletonMap(CPU, 500L)));
        System.out.println();
    }


    @Test
    public void testTranscodeSingleSnapshotEstimate(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, "snapshot");
        JSONObject customParams = new JSONObject();
        addSnapshotOneFrame(customParams);

        addSnapshotCustomParam(params, customParams);
        String jobParam = constructTranscodeJobParam(params);
        JobAnalysisParam param = constructParam(jobParam);

        JobAnalysisResult result = jobAnalysisService.analysisByParseParam(param);
        Assert.assertTrue(result.getQuotaSet().get(CPU).equals(500L));
    }

    @Test
    public void testTranscodeNotSingleSnapshotEstimate(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, "snapshot");
        JSONObject customParams = new JSONObject();
        addSnapshotMultiFrame(customParams);

        addSnapshotCustomParam(params, customParams);
        String jobParam = constructTranscodeJobParam(params);
        JobAnalysisParam param = constructParam(jobParam);

        JobAnalysisResult result = jobAnalysisService.analysisByParseParam(param);
        Assert.assertTrue(result.getQuotaSet().get(CPU).equals(2000L));
    }

    @Test
    public void testSliceSnapshot(){
        JobAnalysisParam estimateParam = new JobAnalysisParam();
        estimateParam.setEngineParams("{\"jobId\":\"dc75cc4dc14546ae8480da64bd80cf97\",\"params\":\"{\\\"arguments\\\":\\\"{\\\\\\\"output_oss_host\\\\\\\":\\\\\\\"oss-cn-shanghai.aliyuncs.com\\\\\\\",\\\\\\\"output_oss_object\\\\\\\":\\\\\\\"lihe/temp/mps/snapshot/4d83ce98-33d1-45d8-994b-a0d7f10edf3d/{SequenceTime}.jpg\\\\\\\",\\\\\\\"custom_param\\\\\\\":{\\\\\\\"container\\\\\\\":{},\\\\\\\"muxConfig\\\\\\\":{\\\\\\\"snapshot\\\\\\\":{\\\\\\\"interval\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"start\\\\\\\":0.0,\\\\\\\"isAutoSwapReso\\\\\\\":true,\\\\\\\"method\\\\\\\":\\\\\\\"parallel\\\\\\\",\\\\\\\"type\\\\\\\":\\\\\\\"normal\\\\\\\"}}},\\\\\\\"output_oss_bucket\\\\\\\":\\\\\\\"mts-sh-out\\\\\\\",\\\\\\\"parallelEnv\\\\\\\":{\\\\\\\"taskType\\\\\\\":\\\\\\\"sliceSnapshot\\\\\\\",\\\\\\\"sliceVer\\\\\\\":\\\\\\\"slice_v2_mp4+6.025002\\\\\\\",\\\\\\\"instanceId\\\\\\\":2,\\\\\\\"sliceCount\\\\\\\":2,\\\\\\\"ossTempPrefix\\\\\\\":\\\\\\\"parallel/output/758a201d16ef4d73985c4f0523fd6314\\\\\\\"},\\\\\\\"aliyunUid\\\\\\\":\\\\\\\"1253406881704637\\\\\\\"}\\\",\\\"expire\\\":36000,\\\"input_param\\\":\\\"{\\\\\\\"param\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"error_code\\\\\\\\\\\\\\\":0}\\\\\\\",\\\\\\\"data\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"type\\\\\\\\\\\\\\\":1,\\\\\\\\\\\\\\\"url\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"http://mts-sh-out.oss-cn-shanghai.aliyuncs.com/lihe/input/yunxi_my.mp4\\\\\\\\\\\\\\\"}\\\\\\\"}\\\",\\\"mixEncodingSwitch\\\":0,\\\"mixTag\\\":0,\\\"nvencTag\\\":0,\\\"oHeight\\\":0,\\\"oWidth\\\":0,\\\"process_logic\\\":\\\"sliceSnapshot\\\",\\\"userData\\\":{\\\"mtsJobId\\\":\\\"dc75cc4dc14546ae8480da64bd80cf97\\\",\\\"requestId\\\":\\\"18F992B8-5A2B-4160-A1CD-FA60FDCE1FD2\\\",\\\"sliceActivityId\\\":\\\"3f0219933fe54164b1947964f3a801b8\\\",\\\"activityType\\\":\\\"snapshot\\\",\\\"type\\\":\\\"sliceSnapshot\\\"}}\",\"priority\":10,\"type\":\"Transcode\",\"userId\":1253406881704637}");
        JobAnalysisResult result = jobAnalysisService.analysisByParseParam(estimateParam);

        Assert.assertTrue(result.isSuccess());
    }


    @Test
    public void testSliceTranscode(){
        JobAnalysisParam estimateParam = new JobAnalysisParam();
        estimateParam.setEngineParams("{\"jobId\":\"3b4dcc24c9954e6faac8c624c7264079\",\"params\":\"{\\\"arguments\\\":\\\"{\\\\\\\"output_oss_host\\\\\\\":\\\\\\\"oss-cn-shanghai.aliyuncs.com\\\\\\\",\\\\\\\"output_oss_object\\\\\\\":\\\\\\\"lihe/temp/mps/d5a1520c-52e9-4e9c-8f76-d4002748a829\\\\\\\",\\\\\\\"output_oss_bucket\\\\\\\":\\\\\\\"mts-sh-out\\\\\\\",\\\\\\\"custom_param\\\\\\\":{\\\\\\\"custom_params\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"audio\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"samplerate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"44100\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"channels\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"bitrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"64\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"codec\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"aac\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"container\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"format\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"mp4\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"transFeatures\\\\\\\\\\\\\\\":{},\\\\\\\\\\\\\\\"outputfile\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"isProcess\\\\\\\\\\\\\\\":false},\\\\\\\\\\\\\\\"transConfig\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"transMode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"onepass\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"video\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"crf\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"27\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"width\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"640\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"bitrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"400\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"codec\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"H.264\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"preset\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"medium\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"pixFmt\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"yuv420p\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"maxrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"900\\\\\\\\\\\\\\\"}}\\\\\\\"},\\\\\\\"parallelEnv\\\\\\\":{\\\\\\\"hasAudio\\\\\\\":true,\\\\\\\"taskType\\\\\\\":\\\\\\\"sliceTranscode\\\\\\\",\\\\\\\"sliceVer\\\\\\\":\\\\\\\"slice_v2_mp4\\\\\\\",\\\\\\\"instanceId\\\\\\\":11,\\\\\\\"sliceCount\\\\\\\":13,\\\\\\\"ossTempPrefix\\\\\\\":\\\\\\\"parallel/output/7fdf541d4922414f9970f5d2998c68bc\\\\\\\"},\\\\\\\"aliyunUid\\\\\\\":1253406881704637}\\\",\\\"debugInfo\\\":\\\"w*h=230400.0\\\",\\\"expire\\\":86400,\\\"input_param\\\":\\\"{\\\\\\\"param\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"error_code\\\\\\\\\\\\\\\":0}\\\\\\\",\\\\\\\"data\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"type\\\\\\\\\\\\\\\":1,\\\\\\\\\\\\\\\"url\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"http://mts-sh-out.oss-cn-shanghai.aliyuncs.com/lihe/input/yunxi_ls.mp4\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"videoInfo\\\\\\\":{\\\\\\\"fileSize\\\\\\\":619015932,\\\\\\\"duration\\\\\\\":1577,\\\\\\\"height\\\\\\\":1080,\\\\\\\"width\\\\\\\":1920,\\\\\\\"bitRate\\\\\\\":3139.65}}\\\",\\\"mixEncodingSwitch\\\":0,\\\"mixTag\\\":0,\\\"nvencTag\\\":1,\\\"oHeight\\\":0,\\\"oWidth\\\":640,\\\"process_logic\\\":\\\"sliceTranscode\\\",\\\"userData\\\":{\\\"mtsJobId\\\":\\\"3b4dcc24c9954e6faac8c624c7264079\\\",\\\"requestId\\\":\\\"FE25EC9B-0489-4BC9-9E6B-4CB903C40FB1\\\",\\\"sliceActivityId\\\":\\\"1c76e0b7701b45689e66027b9d1550b3\\\",\\\"format\\\":\\\"mp4\\\",\\\"activityType\\\":\\\"transcode\\\",\\\"type\\\":\\\"sliceTranscode\\\"}}\",\"priority\":10,\"type\":\"Transcode\",\"userId\":1253406881704637}");

        JobAnalysisResult result = jobAnalysisService.analysisByParseParam(estimateParam);

        Assert.assertTrue(result.isSuccess());
    }
    //todo 需要做mock
    @Test
    public void testEstimateJobWithScheduleParams(){
//        JobAnalysisParam estimateParam = new JobAnalysisParam();
//
//        estimateParam.setJobId("ec394816819e463d8c4f033515a366a0");
//        estimateParam.setEngineModel("mps-transcode-new");
//        estimateParam.setEngineParams("{\"action\":{\"uid\":1833220977560785,\"ops\":{\"download-0\":{\"op\":{\"args\":{\"output\":\"output\",\"input\":\"var:var_1\"},\"name\":\"download\"},\"kind\":\"builtin\"},\"download-3\":{\"op\":{\"args\":{\"output\":\"output\",\"input\":\"var:var_4\"},\"name\":\"download\"},\"kind\":\"builtin\"},\"vodTranscode-4\":{\"op\":{\"args\":{\"preProcessResults\":[{\"videoInfo\":{\"rotate\":\"\",\"avgFPS\":\"25.000000\",\"audioStreamCnt\":1,\"colorRange\":\"tv\",\"audioCodecTag\":\"0x6134706d\",\"format_long_name\":\"QuickTime / MOV\",\"bitrate\":\"2317389\",\"avg_fps\":25,\"formatLongName\":\"QuickTime / MOV\",\"audio_codec_name\":\"aac\",\"dar\":\"16:9\",\"audio_codec_tag_string\":\"mp4a\",\"audioDuration\":\"180.016000\",\"video_bitrate\":2248047,\"height\":1080,\"videoStreamCnt\":1,\"samplerate\":\"48000\",\"video_codec_long_name\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"level\":40,\"streams\":[{\"color_range\":\"tv\",\"pix_fmt\":\"yuv420p\",\"r_frame_rate\":\"25/1\",\"start_pts\":0,\"extradata_size\":45,\"duration_ts\":16203600,\"duration\":\"180.040000\",\"bit_rate\":\"2248047\",\"sample_aspect_ratio\":\"1:1\",\"field_order\":\"progressive\",\"film_grain\":0,\"avg_frame_rate\":\"25/1\",\"codec_tag_string\":\"avc1\",\"closed_captions\":0,\"id\":\"0x1\",\"color_space\":\"bt709\",\"nb_frames\":\"4551\",\"codec_long_name\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"height\":1080,\"color_primaries\":\"bt709\",\"chroma_location\":\"left\",\"time_base\":\"1/90000\",\"level\":40,\"color_transfer\":\"bt709\",\"coded_height\":1080,\"profile\":\"High\",\"bits_per_raw_sample\":\"8\",\"index\":0,\"nb_read_frames\":\"4501\",\"tags\":{\"handler_name\":\"VideoHandler\",\"vendor_id\":\"[0][0][0][0]\",\"language\":\"und\",\"mov_stsd_entries\":\"1\"},\"codec_name\":\"h264\",\"start_time\":\"0.000000\",\"disposition\":{\"metadata\":0,\"original\":0,\"visual_impaired\":0,\"attached_pic\":0,\"forced\":0,\"still_image\":0,\"descriptions\":0,\"captions\":0,\"dub\":0,\"karaoke\":0,\"default\":1,\"timed_thumbnails\":0,\"hearing_impaired\":0,\"comment\":0,\"dependent\":0,\"lyrics\":0,\"clean_effects\":0},\"codec_tag\":\"0x31637661\",\"has_b_frames\":1,\"refs\":1,\"codec_time_base\":\"1/50\",\"width\":1920,\"display_aspect_ratio\":\"16:9\",\"coded_width\":1920,\"codec_type\":\"video\"},{\"r_frame_rate\":\"0/0\",\"start_pts\":0,\"extradata_size\":2,\"channel_layout\":\"stereo\",\"duration_ts\":8640768,\"duration\":\"180.016000\",\"bit_rate\":\"64135\",\"avg_frame_rate\":\"0/0\",\"codec_tag_string\":\"mp4a\",\"id\":\"0x2\",\"nb_frames\":\"8534\",\"codec_long_name\":\"AAC (Advanced Audio Coding)\",\"time_base\":\"1/48000\",\"profile\":\"LC\",\"index\":1,\"tags\":{\"handler_name\":\"SoundHandler\",\"vendor_id\":\"[0][0][0][0]\",\"language\":\"eng\",\"mov_stsd_entries\":\"1\"},\"codec_name\":\"aac\",\"start_time\":\"0.000000\",\"disposition\":{\"metadata\":0,\"original\":0,\"visual_impaired\":0,\"attached_pic\":0,\"forced\":0,\"still_image\":0,\"descriptions\":0,\"captions\":0,\"dub\":0,\"karaoke\":0,\"default\":1,\"timed_thumbnails\":0,\"hearing_impaired\":0,\"comment\":0,\"dependent\":0,\"lyrics\":0,\"clean_effects\":0},\"codec_tag\":\"0x6134706d\",\"sample_rate\":\"48000\",\"channels\":2,\"sample_fmt\":\"fltp\",\"codec_time_base\":\"1/48000\",\"bits_per_sample\":0,\"codec_type\":\"audio\"}],\"workAgent\":true,\"profile\":\"High\",\"format\":{\"duration\":\"182.059000\",\"start_time\":\"0.000000\",\"bit_rate\":\"2317389\",\"size\":\"52737695\",\"probe_score\":100,\"format_long_name\":\"QuickTime / MOV\",\"nb_programs\":0,\"nb_streams\":2,\"format_name\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"tags\":{\"major_brand\":\"isom\",\"encoder\":\"Lavf57.83.100\",\"minor_version\":\"512\",\"compatible_brands\":\"isomiso2avc1mp41\"}},\"audio_codec_long_name\":\"AAC (Advanced Audio Coding)\",\"sampleRate\":\"48000\",\"ar\":\"48000\",\"channels\":2,\"aac_profile\":\"LC\",\"format_name\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"video_codec_tag_string\":\"avc1\",\"bitrateInKbps\":2317.39,\"videoCodecLongName\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"streamBasicInfo\":[{\"streamType\":\"video\",\"notificationMessage\":\"\",\"index\":0,\"codecName\":\"h264\",\"notificationCode\":0},{\"streamType\":\"audio\",\"notificationMessage\":\"\",\"index\":1,\"codecName\":\"aac\",\"notificationCode\":0}],\"videoBitrate\":2248047,\"duration\":182.059,\"colorSpace\":\"bt709\",\"videoBitrateInKbps\":2248.05,\"startTime\":\"0.000000\",\"audioCodecTagString\":\"mp4a\",\"subtitleStreamCnt\":0,\"color_space\":\"bt709\",\"colorTransfer\":\"bt709\",\"videoCodecTagString\":\"avc1\",\"videoCodecTag\":\"0x31637661\",\"videoCodecName\":\"h264\",\"audioBitrateInKbps\":64.14,\"videoStartTime\":\"0.000000\",\"audioStartTime\":\"0.000000\",\"sar\":\"1:1\",\"audioStrmKey\":1,\"formatName\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"fps\":\"25.000000\",\"audio_bitrate\":\"64135\",\"audioCodecName\":\"aac\",\"audioBitrate\":\"64135\",\"pixFmt\":\"yuv420p\",\"audioCodecLongName\":\"AAC (Advanced Audio Coding)\",\"videoDuration\":\"180.040000\",\"video_codec_name\":\"h264\",\"width\":1920,\"colorPrimaries\":\"bt709\",\"videoStrmKey\":0},\"errorCode\":0}],\"params\":\"-i {{ op:download-0.output }}  -i {{ op:download-1.output }}  -i {{ op:download-2.output }}  -i {{ op:download-3.output }}  -c:v libx264 -s 1920x1080 -pix_fmt yuv420p -r 25 -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params roi=2.0:hierarchical-b=1:psy=0:aq-mode=2:aq-strength=1.0 -keyint_min 3000 -g 3000 -crf 26 -preset veryslow -vsync cfr -filter_complex \\\"[0:v]scale=-2:1080:flags=lanczos[sclv];[sclv]scale=1920:1080,setsar=1[prstv0];[prstv0][2:v][3:v]concat=n=3:v=1[cnctvout];[1:v]scale=200:200[slogo1];[cnctvout][slogo1]overlay=main_w-100.0-overlay_w:50.0[logo1];[logo1]ans=sharp_strength=0.6:box=3:sharp_delta_max=15:nr_strength=0,restore=model_dir=/home/<USER>/tools/data/models_hrt/366x_gan_y_v1/:light=1:gpus=0\\\" -c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 -filter_complex \\\"[0:a][2:a][3:a]concat=n=3:v=0:a=1,aresample=async=1000\\\" -enable_dolby -loop {} -movflags +faststart -map_metadata -1 -loglevel warning {{ output,suffix=mp4 }} -y\",\"platform\":\"nv_p4\"},\"name\":\"vodTranscode\"},\"kind\":\"builtin\"},\"upload\":{\"op\":{\"args\":{\"output\":\"var:var_5\",\"input\":\"op:vodTranscode-4.output\",\"isProcess\":\"false\"},\"name\":\"upload\"},\"kind\":\"builtin\"},\"download-1\":{\"op\":{\"args\":{\"output\":\"output\",\"input\":\"var:var_2\"},\"name\":\"download\"},\"kind\":\"builtin\"},\"download-2\":{\"op\":{\"args\":{\"output\":\"output\",\"input\":\"var:var_3\"},\"name\":\"download\"},\"kind\":\"builtin\"},\"probe\":{\"op\":{\"args\":{\"input\":\"op:vodTranscode-4.output\"},\"name\":\"probe\"},\"kind\":\"builtin\"}},\"callback\":\"\",\"unfinished\":false},\"vars\":{\"var_4\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1833220977560785\",\"roleArn\":\"acs:ram::1833220977560785:role/AliyunMTSDefaultRole\",\"url\":\"https://algo-eval-bucket.oss-cn-shanghai.aliyuncs.com/qiaoruosong%2Fmedia_common%2Fdefault%2F9a05b4568a4d4216d5476f48c26be816.mp4?OSSAccessKeyId=LTAI5tSD9ywZ6vikkMJTrY3F&Expires=1714896867&Signature=2L5Bip7mdMQIUGtUe%2BrHi2WEHJs%3D\"}},\"var_5\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1833220977560785\",\"roleArn\":\"acs:ram::1833220977560785:role/AliyunMTSDefaultRole\",\"url\":\"http://mps-sh-out.oss-cn-shanghai.aliyuncs.com/0onedryrun/1683618627276-fece0d79d99648bc90848abc5aeeffa2-04bbbc72aa20418e954c39a479155917.mp4\"}},\"userData\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1833220977560785\",\"roleArn\":\"\",\"url\":\"69279b7d7be64ea580193baf643db743\"}},\"var_1\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1833220977560785\",\"roleArn\":\"acs:ram::1833220977560785:role/AliyunMTSDefaultRole\",\"url\":\"http://algo-eval-bucket.oss-cn-shanghai.aliyuncs.com/qiaoruosong/media_common/default/9a05b4568a4d4216d5476f48c26be816.mp4\"}},\"var_2\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1833220977560785\",\"roleArn\":\"acs:ram::1833220977560785:role/AliyunMTSDefaultRole\",\"url\":\"http://mps-sh-in.oss-cn-shanghai.aliyuncs.com/water/wate_test-3.png\"}},\"var_3\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1833220977560785\",\"roleArn\":\"acs:ram::1833220977560785:role/AliyunMTSDefaultRole\",\"url\":\"https://algo-eval-bucket.oss-cn-shanghai.aliyuncs.com/qiaoruosong%2Fmedia_common%2Fdefault%2F9a05b4568a4d4216d5476f48c26be816.mp4?OSSAccessKeyId=LTAI5tSD9ywZ6vikkMJTrY3F&Expires=1714896867&Signature=2L5Bip7mdMQIUGtUe%2BrHi2WEHJs%3D\"}}}}");
//        estimateParam.setUserId("1833220977560785");
//        estimateParam.setSpeedXRange(null);
//        estimateParam.setAutoSpeedX(false);
//
//        estimateParam.setSliceProcess(false);
//
//        estimateParam.setInvokeWorkerBrain(true);
//        estimateParam.setUseWorkerBrainResult(true);
//
//        estimateParam.setTag("v100");
//
//        ScheduleParams scheduleParams = new ScheduleParams();
//        scheduleParams.setPipelineId("04bbbc72aa20418e954c39a479155917");
//        scheduleParams.setPriority(6);
//        scheduleParams.setExpectCostTime(null);
//
//        List<Input> inputs = new ArrayList<>();
//        Input input = new Input();
//        input.setWidth(1280);
//        input.setHeight(720);
//        input.setVideoCodec("h264");
//        input.setAudioCodec("aac");
//        input.setFormat("QuickTime / MOV");
//        input.setDuration(182.059);
//        input.setSize(52737695L);
//        input.setFps(25.0);
//        input.setAvgFPS(25.0);
//        input.setVideoBitrate(2248.047);
//        input.setAudioBitrate(128.786);
//
//        inputs.add(input);
//        scheduleParams.setInputs(inputs);
//
//        List<Config> configs = new ArrayList<>();
//        Config config = new Config();
//        config.setJobType("transcode");
//        config.setNhVersion("2.0");
//        config.setId("69279b7d7be64ea580193baf643db743");
//        config.setTemplateId("fece0d79d99648bc90848abc5aeeffa2");
//        config.setVideoCodec("x264");
//        config.setAudioCodec("fdk_aac");
//        config.setFormat("mp4");
//        config.setDuration(182.059);
//        config.setFps(14.824062);
//        config.setAudioBitrate(64.135);
//
//        configs.add(config);
//        scheduleParams.setConfigs(configs);
//        estimateParam.setScheduleParams(scheduleParams);
//        JSONObject trace = new JSONObject();
//        trace.put("jobId","69279b7d7be64ea580193baf643db743");
//        trace.put("requestId","C4B8E194-A3D8-1D98-B8BE-C0AFCE91E075");
//        trace.put("aliyunUid","1833220977560785");
//
//        estimateParam.setTrace(trace);
//
//        System.out.println(JSONObject.toJSONString(estimateParam));
//        JobAnalysisResult result = jobAnalysisService.estimateJobWithScheduleParams(estimateParam);
//
//        Assert.assertTrue(result.isSuccess());
    }


}
