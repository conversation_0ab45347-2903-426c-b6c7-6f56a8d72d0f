package com.aliyun.mpp.analysis.test.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.types.VideoInfo;

import java.util.UUID;

/**
 * Created by lihe.lh on 2020/2/12.
 */
public class BaseTest {
    public static final String CUSTOMTRANSCODE = "customtranscode";
    public static final String SNAPSHOT = "snapshot";
    public static final String VIDEO = "video";
    public static final String AUDIO = "audio";
    protected String baseParams = "{\"arguments\":\"{\\\"output_oss_object\\\":\\\"a\\\",\\\"output_oss_bucket\\\":\\\"mts-log-param-cn-beijing\\\",\\\"aliyunUid\\\":\\\"1026520343778131\\\"}\",\"expire\":1800,\"input_param\":\"{\\\"param\\\":\\\"{\\\\\\\"error_code\\\\\\\":0}\\\",\\\"data\\\":\\\"{\\\\\\\"type\\\\\\\":3,\\\\\\\"url\\\\\\\":\\\\\\\"http://conan-online.oss-cn-beijing.aliyuncs.com/conan-english%2F221960430%2F2572-0--1856101256.aac\\\\\\\"}\\\",\\\"isSliceTrans\\\":false}\"}";
    protected final String PROCESS_LOGIC = "process_logic";
    protected final String CPU = "cpu";

    private final double BASE_DURATION = 7200f;
    private final double BASE_FPS = 30f;
    private final int BASE_WIDTH = 1280;
    private final int BASE_HEIGHT = 720;
    private final long BASE_FILE_SIZE = 1024 * 1024 * 1024;
    private final String VIDEO_CODEC = "h264";

    protected void addVideoCopy(JSONObject customeParams){
        JSONObject videoConfig = getConfig(customeParams, VIDEO);
        videoConfig.put("copy", true);
        customeParams.put("video", videoConfig);
    }

    protected void addBasicAudioConfig(JSONObject customParams){
        JSONObject audioConfig = getConfig(customParams, AUDIO);
        customParams.put("audio", audioConfig);
    }

    protected void addBasicVideoConfig(JSONObject customParams){
        JSONObject audioConfig = getConfig(customParams, VIDEO);
        customParams.put("video", audioConfig);
    }


    protected void addH265Codec(JSONObject customParams){
        JSONObject videoConfig = getConfig(customParams, VIDEO);
        videoConfig.put("codec", "H.265");
        customParams.put("video", videoConfig);

    }

    protected void addNarrowBandV1(JSONObject customParams){
        JSONObject videoConfig = getConfig(customParams, VIDEO);

        videoConfig.put("params", "ref=4:sharp=1-1");
        customParams.put("video", videoConfig);
    }

    protected void addOutputReso(JSONObject customParams, Integer width, Integer height){
        JSONObject videoConfig = getConfig(customParams, VIDEO);
        if(width != null) {
            videoConfig.put("width", width);
        }
        if(height != null){
            videoConfig.put("height", height);
        }
        customParams.put("video", videoConfig);
    }


    protected void addNarrowBandV2(JSONObject customParams){
        JSONObject videoConfig = getConfig(customParams, VIDEO);
        videoConfig.put("DnCNN", "{}");
        customParams.put("video", videoConfig);
    }

    protected void addSnapshotOneFrame(JSONObject customParams){
        JSONObject muxConfig = getConfig(customParams, "muxConfig");
        JSONObject snapshotConfig = getConfig(muxConfig, "snapshot");
        snapshotConfig.put("number", 1);
        muxConfig.put("snapshot", snapshotConfig);
        customParams.put("muxConfig", muxConfig);
    }

    protected void addSnapshotMultiFrame(JSONObject customParams){
        JSONObject muxConfig = getConfig(customParams, "muxConfig");
        JSONObject snapshotConfig = getConfig(muxConfig, "snapshot");
        snapshotConfig.put("number", 100);
        muxConfig.put("snapshot", snapshotConfig);
        customParams.put("muxConfig", muxConfig);
    }

    protected void addSnapshoByInterval(JSONObject customParams){
        JSONObject muxConfig = getConfig(customParams, "muxConfig");
        JSONObject snapshotConfig = getConfig(muxConfig, "snapshot");
        snapshotConfig.put("interval", 1000);
        muxConfig.put("snapshot", snapshotConfig);
        customParams.put("muxConfig", muxConfig);
    }

    protected JSONObject getConfig(JSONObject customParams, String key) {
        JSONObject value = customParams.getJSONObject(key);
        if (value == null) {
            value = new JSONObject();
        }
        return value;
    }


    protected String constructTranscodeJobParam(JSONObject params){
        JSONObject jobParam = new JSONObject();
        jobParam.put("type", "Transcode");
        jobParam.put("userId", 111);
        jobParam.put("jobId", UUID.randomUUID().toString());
        jobParam.put("params", params.toJSONString());

        JSONObject userData = new JSONObject();
        userData.put("requestId", UUID.randomUUID().toString());

        jobParam.put("userData", userData);
        return jobParam.toJSONString();
    }

    protected void addSnapshotCustomParam(JSONObject params, JSONObject customParams){
        JSONObject argument = params.getJSONObject("arguments");
        argument.put("custom_param", customParams);
        params.put("arguments", argument.toJSONString());
    }

    protected JobAnalysisParam generateTranscodeJobParam(JSONObject params, JSONObject customParams){
        JSONObject argument = params.getJSONObject("arguments");

        JSONObject outerCustomParams = new JSONObject();
        outerCustomParams.put("custom_params", customParams.toJSONString());
        argument.put("custom_param", outerCustomParams);

        params.put("arguments", argument.toJSONString());

        String jobParam = constructTranscodeJobParam(params);
        JobAnalysisParam param = constructParam(jobParam);
        return param;
    }

    protected JobAnalysisParam constructParam(String engineParams){
        JobAnalysisParam jobAnalysisParam = new JobAnalysisParam();
        jobAnalysisParam.setJobId(UUID.randomUUID().toString());
        jobAnalysisParam.setEngineModel("mps-transcode");
        jobAnalysisParam.setSliceProcess(false);
        jobAnalysisParam.setTrace(JSON.parseObject("{\"a\":\"b\"}"));
        jobAnalysisParam.setEngineParams(engineParams);
        jobAnalysisParam.setUserId("11");
        return jobAnalysisParam;
    }


    protected VideoInfo generateVideoInfo(){
        VideoInfo videoInfo = new VideoInfo();
        videoInfo.setDuration(BASE_DURATION);
        videoInfo.setFps(BASE_FPS);
        videoInfo.setVideoCodecName(VIDEO_CODEC);
        videoInfo.setHeight(BASE_HEIGHT);
        videoInfo.setWidth(BASE_WIDTH);
        videoInfo.setFileSize(BASE_FILE_SIZE);
        videoInfo.setFormatName("mp4");
        videoInfo.setBitrate(videoInfo.getFileSize() * 8 / videoInfo.getDuration());
        videoInfo.setAudioBitrate(128 * 1000.0);
        videoInfo.setVideoBitrate(videoInfo.getBitrate() - videoInfo.getAudioBitrate());

        return videoInfo;
    }

}
