package com.aliyun.mpp.analysis.test.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.service.impl.JobAnalysisServiceImpl;
import com.aliyun.mpp.analysis.domain.service.impl.JobAnalysisUtil;
import com.aliyun.mpp.analysis.domain.service.impl.config.NewEngineTaskQuotaEstimator;
import com.aliyun.mpp.analysis.domain.service.impl.config.OldEngineTaskQuotaEstimator;
import com.aliyun.mpp.analysis.domain.types.EstimateResult;
import com.aliyun.mpp.analysis.domain.types.JobAnalysisResult;
import com.aliyun.mpp.analysis.domain.types.ScheduleParams;
import com.aliyun.mpp.analysis.util.http.HttpUtil;
import com.aliyun.mpp.analysis.util.log.GlobalVars;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RunWith(MockitoJUnitRunner.class)
public class JobAnalysisServiceV2Test {
    @InjectMocks
    private JobAnalysisServiceImpl jobAnalysisService;

    @Mock
    private JobAnalysisUtil jobAnalysisUtil;

    @Mock
    private OldEngineTaskQuotaEstimator oldEngineTaskQuotaEstimator;

    @Mock
    private NewEngineTaskQuotaEstimator newEngineTaskQuotaEstimator;

    @Mock
    private GlobalVars globalVars;

    @Mock
    private HttpUtil httpUtil;


    @Before
    public void setup() {
    }

    //普通dag任务测试
    @Test
    public void testEstimateJobWithScheduleParamsForDAGJob(){
        JobAnalysisParam estimateParam = new JobAnalysisParam();
        estimateParam.setEngineModel("mps-transcode-new");
        estimateParam.setAnalysisMode("byScheduleParams");
        estimateParam.setEngineParams("{\"action\":{\"uid\":1532103438415171,\"ops\":{\"segment-3\":{\"op\":{\"args\":{\"params\":\"-analyzeduration 60000000 -probesize 60000000 -i {{ op:vodTranscode-1.output }} -c copy -f hls -hls_time 9.9 -hls_list_size 0 -hls_allow_cache 1 {{ output,suffix=m3u8 }} -y\"},\"name\":\"segment\"},\"kind\":\"builtin\"},\"download-0\":{\"op\":{\"args\":{\"output\":\"output\",\"input\":\"var:var_1\"},\"name\":\"download\"},\"kind\":\"builtin\"},\"upload\":{\"op\":{\"args\":{\"output\":\"var:var_2\",\"input\":\"op:segment-3.output\",\"m3u8Config\":{\"tsPattern\":\"${prefix}-${index|05d}.ts\"},\"isProcess\":\"false\"},\"name\":\"upload\"},\"kind\":\"builtin\"},\"vodTranscode-1\":{\"op\":{\"args\":{\"preProcessResults\":[{\"videoInfo\":{\"rotate\":\"\",\"avgFPS\":\"25.000000\",\"audioStreamCnt\":1,\"colorRange\":\"\",\"audioCodecTag\":\"0x6134706d\",\"format_long_name\":\"QuickTime / MOV\",\"bitrate\":\"1627985\",\"avg_fps\":25,\"formatLongName\":\"QuickTime / MOV\",\"audio_codec_name\":\"aac\",\"dar\":\"16:9\",\"audio_codec_tag_string\":\"mp4a\",\"sliceMode\":\"normal\",\"audioDuration\":\"344597.206009\",\"video_bitrate\":1491037,\"height\":1080,\"videoStreamCnt\":1,\"samplerate\":\"44100\",\"video_codec_long_name\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"level\":40,\"streams\":[{\"pix_fmt\":\"yuv420p\",\"r_frame_rate\":\"25/1\",\"start_pts\":0,\"extradata_size\":46,\"duration_ts\":4410844160,\"duration\":\"344597.200000\",\"bit_rate\":\"1491037\",\"sample_aspect_ratio\":\"1:1\",\"field_order\":\"progressive\",\"film_grain\":0,\"avg_frame_rate\":\"25/1\",\"codec_tag_string\":\"avc1\",\"closed_captions\":0,\"id\":\"0x1\",\"nb_frames\":\"8614930\",\"codec_long_name\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"height\":1080,\"chroma_location\":\"left\",\"time_base\":\"1/12800\",\"level\":40,\"coded_height\":1080,\"profile\":\"High\",\"bits_per_raw_sample\":\"8\",\"index\":0,\"nb_read_frames\":\"8614930\",\"tags\":{\"handler_name\":\"VideoHandler\",\"vendor_id\":\"[0][0][0][0]\",\"language\":\"und\",\"mov_stsd_entries\":\"1\"},\"codec_name\":\"h264\",\"start_time\":\"0.000000\",\"disposition\":{\"metadata\":0,\"original\":0,\"visual_impaired\":0,\"attached_pic\":0,\"forced\":0,\"still_image\":0,\"descriptions\":0,\"captions\":0,\"dub\":0,\"karaoke\":0,\"default\":1,\"timed_thumbnails\":0,\"hearing_impaired\":0,\"comment\":0,\"dependent\":0,\"lyrics\":0,\"clean_effects\":0},\"codec_tag\":\"0x31637661\",\"has_b_frames\":2,\"refs\":1,\"codec_time_base\":\"1/50\",\"width\":1920,\"display_aspect_ratio\":\"16:9\",\"coded_width\":1920,\"codec_type\":\"video\"},{\"r_frame_rate\":\"0/0\",\"start_pts\":0,\"extradata_size\":2,\"channel_layout\":\"stereo\",\"duration_ts\":15196736785,\"duration\":\"344597.206009\",\"bit_rate\":\"128000\",\"avg_frame_rate\":\"0/0\",\"codec_tag_string\":\"mp4a\",\"id\":\"0x2\",\"nb_frames\":\"14840607\",\"codec_long_name\":\"AAC (Advanced Audio Coding)\",\"time_base\":\"1/44100\",\"profile\":\"LC\",\"index\":1,\"tags\":{\"handler_name\":\"SoundHandler\",\"vendor_id\":\"[0][0][0][0]\",\"language\":\"und\",\"mov_stsd_entries\":\"1\"},\"codec_name\":\"aac\",\"start_time\":\"0.000000\",\"disposition\":{\"metadata\":0,\"original\":0,\"visual_impaired\":0,\"attached_pic\":0,\"forced\":0,\"still_image\":0,\"descriptions\":0,\"captions\":0,\"dub\":0,\"karaoke\":0,\"default\":1,\"timed_thumbnails\":0,\"hearing_impaired\":0,\"comment\":0,\"dependent\":0,\"lyrics\":0,\"clean_effects\":0},\"codec_tag\":\"0x6134706d\",\"sample_rate\":\"44100\",\"channels\":2,\"sample_fmt\":\"fltp\",\"codec_time_base\":\"1/44100\",\"bits_per_sample\":0,\"codec_type\":\"audio\"}],\"workAgent\":true,\"profile\":\"High\",\"format\":{\"duration\":\"344597.253000\",\"start_time\":\"0.000000\",\"bit_rate\":\"1627985\",\"size\":\"70124898816\",\"probe_score\":100,\"format_long_name\":\"QuickTime / MOV\",\"nb_programs\":0,\"nb_streams\":2,\"format_name\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"tags\":{\"major_brand\":\"isom\",\"encoder\":\"www.aliyun.com - Media Transcoding\",\"minor_version\":\"512\",\"compatible_brands\":\"isomiso2avc1mp41\"}},\"audio_codec_long_name\":\"AAC (Advanced Audio Coding)\",\"sampleRate\":\"44100\",\"ar\":\"44100\",\"channels\":2,\"aac_profile\":\"LC\",\"format_name\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"video_codec_tag_string\":\"avc1\",\"bitrateInKbps\":1627.98,\"videoCodecLongName\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"streamBasicInfo\":[{\"streamType\":\"video\",\"notificationMessage\":\"\",\"index\":0,\"codecName\":\"h264\",\"notificationCode\":0},{\"streamType\":\"audio\",\"notificationMessage\":\"\",\"index\":1,\"codecName\":\"aac\",\"notificationCode\":0}],\"videoBitrate\":1491037,\"duration\":344597.253,\"colorSpace\":\"\",\"videoBitrateInKbps\":1491.04,\"startTime\":\"0.000000\",\"audioCodecTagString\":\"mp4a\",\"subtitleStreamCnt\":0,\"color_space\":\"\",\"colorTransfer\":\"\",\"videoCodecTagString\":\"avc1\",\"videoCodecTag\":\"0x31637661\",\"videoCodecName\":\"h264\",\"audioBitrateInKbps\":128,\"videoStartTime\":\"0.000000\",\"audioStartTime\":\"0.000000\",\"sar\":\"1:1\",\"audioStrmKey\":1,\"formatName\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"fps\":\"25.000000\",\"audio_bitrate\":\"128000\",\"audioCodecName\":\"aac\",\"audioBitrate\":\"128000\",\"pixFmt\":\"yuv420p\",\"audioCodecLongName\":\"AAC (Advanced Audio Coding)\",\"videoDuration\":\"344597.200000\",\"video_codec_name\":\"h264\",\"width\":1920,\"colorPrimaries\":\"\",\"videoStrmKey\":0},\"errorCode\":0}],\"params\":\"-disable_checkts -disable_checkts -i {{ op:download-0.output }}  -c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower -filter_complex \\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\" -c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 -enable_dolby -map_metadata -1 -loglevel warning {{ output,suffix=ts }} -y\",\"platform\":\"cpu\"},\"name\":\"vodTranscode\"},\"kind\":\"builtin\"},\"probe\":{\"op\":{\"args\":{\"input\":\"op:vodTranscode-1.output\"},\"name\":\"probe\"},\"kind\":\"builtin\"}},\"callback\":\"\",\"unfinished\":false},\"vars\":{\"userData\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"\",\"url\":\"44ce7d077ec141e9957a20b4c27ec578\"}},\"var_1\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"acs:ram::1532103438415171:role/AliyunMTSDefaultRole\",\"url\":\"http://oss-sim-vms-01.oss-cn-beijing.aliyuncs.com/video/1015/o/113303746052704665780001015/cc09c6ad278a4a39871ff4201b2b8c35.mp4\"}},\"var_2\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"acs:ram::1532103438415171:role/AliyunMTSDefaultRole\",\"url\":\"http://oss-sim-vms-pub-01.oss-cn-beijing.aliyuncs.com/video/1015/p/113303746052704665780001015/113303746059415552480011015/199a29cd815f4c0eae08b6695aee26ef-4.m3u8\"}}}}");
        estimateParam.setJobId(UUID.randomUUID().toString());
        estimateParam.setScheduleParams(JSON.parseObject("{\"pipelineId\":\"0c05ebf74a04450c8c99796221cb0fb1\",\"priority\":6,\"scheduleLevel\":\"STANDARD\",\"inputs\":[{\"duration\":344597.253,\"videoBitrate\":1491.037,\"avgFps\":25.0,\"size\":70124898816,\"format\":\"QuickTime / MOV\",\"fps\":25.0,\"width\":1920,\"audioCodec\":\"aac\",\"audioBitrate\":128.0,\"height\":1080,\"videoCodec\":\"h264\"}],\"configs\":[{\"duration\":344597.253,\"uhd\":\"1.0\",\"format\":\"m3u8\",\"fps\":25.0,\"id\":\"44ce7d077ec141e9957a20b4c27ec578\",\"audioCodec\":\"fdk_aac\",\"jobType\":\"transcode\",\"templateId\":\"7008883e17a946ad96d5efe540908dd0\",\"audioBitrate\":128.0,\"videoCodec\":\"x264\"}]}", ScheduleParams.class));
        estimateParam.setUserId("22");
        estimateParam.setCreateTime(new Date());
        estimateParam.setSliceProcess(true);
        estimateParam.setTag("test");
        estimateParam.setInvokeWorkerBrain(false);

        String result = "{\"code\":\"Success\",\"message\":\"\",\"graph\":{\"jobVertexs\":[{\"name\":\"chunkSpliter\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-sim-vms-01.oss-cn-beijing.aliyuncs.com/video/1015/o/113303746052704665780001015/cc09c6ad278a4a39871ff4201b2b8c35.mp4\\\",\\\"aliyunUid\\\":\\\"1532103438415171\\\",\\\"roleArn\\\":\\\"acs:ram::1532103438415171:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:download-0.output\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":-1,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"databusMap\\\":{},\\\"outputMap\\\":{\\\"0\\\":\\\"chunkSpliter_output0,suffix=mov\\\",\\\"1\\\":\\\"chunkSpliter_output1,suffix=mov\\\",\\\"2\\\":\\\"chunkSpliter_output2,suffix=mov\\\",\\\"3\\\":\\\"chunkSpliter_output3,suffix=mov\\\",\\\"4\\\":\\\"chunkSpliter_output4,suffix=mov\\\",\\\"5\\\":\\\"chunkSpliter_output5,suffix=mov\\\",\\\"6\\\":\\\"chunkSpliter_output6,suffix=mov\\\",\\\"7\\\":\\\"chunkSpliter_output7,suffix=mov\\\",\\\"8\\\":\\\"chunkSpliter_output8,suffix=mov\\\",\\\"9\\\":\\\"chunkSpliter_output9,suffix=mov\\\"},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":-2,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v0\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output0\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v1\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output1\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":1,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v2\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output2\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":2,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v3\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output3\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":3,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v4\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output4\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":4,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v5\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output5\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":5,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v6\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output6\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":6,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v7\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output7\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":7,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v8\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output8\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":8,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v9\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output9\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":9,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"download-0\\\":{\\\"op\\\":{\\\"name\\\":\\\"download\\\",\\\"args\\\":{\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"output\\\"}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"audioCodec\":\"aac\",\"format\":\"QuickTime / MOV\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25,\"videoBitrate\":1491.037,\"audioBitrate\":128}],\"configs\":[{\"jobType\":\"decode\"}]}},{\"name\":\"encoder_0\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_0.output\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":0,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_0\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"videoFilter\\\":\\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_1\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":1,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_1.output\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":1,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_1\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":1,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"videoFilter\\\":\\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_2\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":2,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_2.output\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":2,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_2\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":2,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"videoFilter\\\":\\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_3\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":3,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_3.output\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_3\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":3,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"videoFilter\\\":\\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_4\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":4,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_4.output\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":4,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_4\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":4,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"videoFilter\\\":\\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_5\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":5,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_5.output\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":5,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_5\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":5,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"videoFilter\\\":\\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_6\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":6,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_6.output\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":6,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_6\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":6,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"videoFilter\\\":\\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_7\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":7,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_7.output\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":7,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_7\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":7,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"videoFilter\\\":\\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_8\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":8,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_8.output\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":8,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_8\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":8,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"videoFilter\\\":\\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_9\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":9,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_9.output\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":9,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_9\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":10,\\\"frames\\\":8614930,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":9,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\"  \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"videoFilter\\\":\\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"merger\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_2\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-sim-vms-pub-01.oss-cn-beijing.aliyuncs.com/video/1015/p/113303746052704665780001015/113303746059415552480011015/199a29cd815f4c0eae08b6695aee26ef-4.m3u8\\\",\\\"aliyunUid\\\":\\\"1532103438415171\\\",\\\"roleArn\\\":\\\"acs:ram::1532103438415171:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"chunkMerger\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkMerge\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=ts\\\",\\\"chunks\\\":10,\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerInopts\\\":\\\"-disable_checkts -disable_checkts \\\",\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"},\\\"audioIn\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"probe\\\":{\\\"op\\\":{\\\"name\\\":\\\"probe\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkMerger.000\\\",\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"segment-3\\\":{\\\"op\\\":{\\\"name\\\":\\\"segment\\\",\\\"args\\\":{\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"params\\\":\\\"-analyzeduration 60000000 -probesize 60000000 -i {{ op:chunkMerger.000 }} -c copy -f hls -hls_time 9.9 -hls_list_size 0 -hls_allow_cache 1 {{ output,suffix=m3u8 }} -y\\\",\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"upload\\\":{\\\"op\\\":{\\\"name\\\":\\\"upload\\\",\\\"args\\\":{\\\"input\\\":\\\"op:segment-3.output\\\",\\\"output\\\":\\\"var:var_2\\\",\\\"duration\\\":344597.2,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"Proberesults\\\":null,\\\"isProcess\\\":\\\"false\\\"}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":344597.253,\"size\":70124898816,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"copy\",\"width\":null}]}}],\"jobEdges\":[{\"name\":\"chunkSpliterAudio\",\"from\":\"chunkSpliter\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderIn_0\",\"from\":\"chunkSpliter\",\"to\":\"encoder_0\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_0\",\"from\":\"encoder_0\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_0\",\"from\":\"encoder_0\",\"to\":\"encoder_1\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_1\",\"from\":\"chunkSpliter\",\"to\":\"encoder_1\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_1\",\"from\":\"encoder_1\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_1\",\"from\":\"encoder_1\",\"to\":\"encoder_2\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_2\",\"from\":\"chunkSpliter\",\"to\":\"encoder_2\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_2\",\"from\":\"encoder_2\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_2\",\"from\":\"encoder_2\",\"to\":\"encoder_3\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_3\",\"from\":\"chunkSpliter\",\"to\":\"encoder_3\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_3\",\"from\":\"encoder_3\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_3\",\"from\":\"encoder_3\",\"to\":\"encoder_4\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_4\",\"from\":\"chunkSpliter\",\"to\":\"encoder_4\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_4\",\"from\":\"encoder_4\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_4\",\"from\":\"encoder_4\",\"to\":\"encoder_5\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_5\",\"from\":\"chunkSpliter\",\"to\":\"encoder_5\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_5\",\"from\":\"encoder_5\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_5\",\"from\":\"encoder_5\",\"to\":\"encoder_6\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_6\",\"from\":\"chunkSpliter\",\"to\":\"encoder_6\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_6\",\"from\":\"encoder_6\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_6\",\"from\":\"encoder_6\",\"to\":\"encoder_7\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_7\",\"from\":\"chunkSpliter\",\"to\":\"encoder_7\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_7\",\"from\":\"encoder_7\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_7\",\"from\":\"encoder_7\",\"to\":\"encoder_8\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_8\",\"from\":\"chunkSpliter\",\"to\":\"encoder_8\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_8\",\"from\":\"encoder_8\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_8\",\"from\":\"encoder_8\",\"to\":\"encoder_9\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_9\",\"from\":\"chunkSpliter\",\"to\":\"encoder_9\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_9\",\"from\":\"encoder_9\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"}],\"migratePolicy\":\"RESTART_ALL\"}}";
        Mockito.when(httpUtil.sendPostRequest(Mockito.anyString(), Mockito.any())).thenReturn(result);


        Mockito.when(newEngineTaskQuotaEstimator.analysis(Mockito.any(), Mockito.any())).thenReturn(mockEstimateResult());

        JobAnalysisResult analysisResult = jobAnalysisService.estimateJobWithScheduleParams(estimateParam);
        Assert.assertTrue(analysisResult.isSuccess());
    }


    //倍速任务测试
    @Test
    public void testEstimateJobWithScheduleParamsForBoostJob(){
        JobAnalysisParam estimateParam = new JobAnalysisParam();
        estimateParam.setEngineModel("mps-transcode-new");
        estimateParam.setAnalysisMode("byScheduleParams");
        estimateParam.setEngineParams("{\"action\":{\"uid\":1532103438415171,\"ops\":{\"segment-3\":{\"op\":{\"args\":{\"params\":\"-analyzeduration 60000000 -probesize 60000000 -i {{ op:vodTranscode-1.output }} -c copy -f hls -hls_time 9.9 -hls_list_size 0 -hls_allow_cache 1 {{ output,suffix=m3u8 }} -y\"},\"name\":\"segment\"},\"kind\":\"builtin\"},\"download-0\":{\"op\":{\"args\":{\"output\":\"output\",\"input\":\"var:var_1\"},\"name\":\"download\"},\"kind\":\"builtin\"},\"upload\":{\"op\":{\"args\":{\"output\":\"var:var_2\",\"input\":\"op:segment-3.output\",\"m3u8Config\":{\"tsPattern\":\"${prefix}-${index|05d}.ts\"},\"isProcess\":\"false\"},\"name\":\"upload\"},\"kind\":\"builtin\"},\"vodTranscode-1\":{\"op\":{\"args\":{\"preProcessResults\":[{\"videoInfo\":{\"rotate\":\"\",\"avgFPS\":\"25.000000\",\"audioStreamCnt\":1,\"colorRange\":\"\",\"audioCodecTag\":\"0x6134706d\",\"format_long_name\":\"QuickTime / MOV\",\"bitrate\":\"1627985\",\"avg_fps\":25,\"formatLongName\":\"QuickTime / MOV\",\"audio_codec_name\":\"aac\",\"dar\":\"16:9\",\"audio_codec_tag_string\":\"mp4a\",\"sliceMode\":\"normal\",\"audioDuration\":\"344597.206009\",\"video_bitrate\":1491037,\"height\":1080,\"videoStreamCnt\":1,\"samplerate\":\"44100\",\"video_codec_long_name\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"level\":40,\"streams\":[{\"pix_fmt\":\"yuv420p\",\"r_frame_rate\":\"25/1\",\"start_pts\":0,\"extradata_size\":46,\"duration_ts\":4410844160,\"duration\":\"344597.200000\",\"bit_rate\":\"1491037\",\"sample_aspect_ratio\":\"1:1\",\"field_order\":\"progressive\",\"film_grain\":0,\"avg_frame_rate\":\"25/1\",\"codec_tag_string\":\"avc1\",\"closed_captions\":0,\"id\":\"0x1\",\"nb_frames\":\"8614930\",\"codec_long_name\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"height\":1080,\"chroma_location\":\"left\",\"time_base\":\"1/12800\",\"level\":40,\"coded_height\":1080,\"profile\":\"High\",\"bits_per_raw_sample\":\"8\",\"index\":0,\"nb_read_frames\":\"8614930\",\"tags\":{\"handler_name\":\"VideoHandler\",\"vendor_id\":\"[0][0][0][0]\",\"language\":\"und\",\"mov_stsd_entries\":\"1\"},\"codec_name\":\"h264\",\"start_time\":\"0.000000\",\"disposition\":{\"metadata\":0,\"original\":0,\"visual_impaired\":0,\"attached_pic\":0,\"forced\":0,\"still_image\":0,\"descriptions\":0,\"captions\":0,\"dub\":0,\"karaoke\":0,\"default\":1,\"timed_thumbnails\":0,\"hearing_impaired\":0,\"comment\":0,\"dependent\":0,\"lyrics\":0,\"clean_effects\":0},\"codec_tag\":\"0x31637661\",\"has_b_frames\":2,\"refs\":1,\"codec_time_base\":\"1/50\",\"width\":1920,\"display_aspect_ratio\":\"16:9\",\"coded_width\":1920,\"codec_type\":\"video\"},{\"r_frame_rate\":\"0/0\",\"start_pts\":0,\"extradata_size\":2,\"channel_layout\":\"stereo\",\"duration_ts\":15196736785,\"duration\":\"344597.206009\",\"bit_rate\":\"128000\",\"avg_frame_rate\":\"0/0\",\"codec_tag_string\":\"mp4a\",\"id\":\"0x2\",\"nb_frames\":\"14840607\",\"codec_long_name\":\"AAC (Advanced Audio Coding)\",\"time_base\":\"1/44100\",\"profile\":\"LC\",\"index\":1,\"tags\":{\"handler_name\":\"SoundHandler\",\"vendor_id\":\"[0][0][0][0]\",\"language\":\"und\",\"mov_stsd_entries\":\"1\"},\"codec_name\":\"aac\",\"start_time\":\"0.000000\",\"disposition\":{\"metadata\":0,\"original\":0,\"visual_impaired\":0,\"attached_pic\":0,\"forced\":0,\"still_image\":0,\"descriptions\":0,\"captions\":0,\"dub\":0,\"karaoke\":0,\"default\":1,\"timed_thumbnails\":0,\"hearing_impaired\":0,\"comment\":0,\"dependent\":0,\"lyrics\":0,\"clean_effects\":0},\"codec_tag\":\"0x6134706d\",\"sample_rate\":\"44100\",\"channels\":2,\"sample_fmt\":\"fltp\",\"codec_time_base\":\"1/44100\",\"bits_per_sample\":0,\"codec_type\":\"audio\"}],\"workAgent\":true,\"profile\":\"High\",\"format\":{\"duration\":\"344597.253000\",\"start_time\":\"0.000000\",\"bit_rate\":\"1627985\",\"size\":\"70124898816\",\"probe_score\":100,\"format_long_name\":\"QuickTime / MOV\",\"nb_programs\":0,\"nb_streams\":2,\"format_name\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"tags\":{\"major_brand\":\"isom\",\"encoder\":\"www.aliyun.com - Media Transcoding\",\"minor_version\":\"512\",\"compatible_brands\":\"isomiso2avc1mp41\"}},\"audio_codec_long_name\":\"AAC (Advanced Audio Coding)\",\"sampleRate\":\"44100\",\"ar\":\"44100\",\"channels\":2,\"aac_profile\":\"LC\",\"format_name\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"video_codec_tag_string\":\"avc1\",\"bitrateInKbps\":1627.98,\"videoCodecLongName\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"streamBasicInfo\":[{\"streamType\":\"video\",\"notificationMessage\":\"\",\"index\":0,\"codecName\":\"h264\",\"notificationCode\":0},{\"streamType\":\"audio\",\"notificationMessage\":\"\",\"index\":1,\"codecName\":\"aac\",\"notificationCode\":0}],\"videoBitrate\":1491037,\"duration\":344597.253,\"colorSpace\":\"\",\"videoBitrateInKbps\":1491.04,\"startTime\":\"0.000000\",\"audioCodecTagString\":\"mp4a\",\"subtitleStreamCnt\":0,\"color_space\":\"\",\"colorTransfer\":\"\",\"videoCodecTagString\":\"avc1\",\"videoCodecTag\":\"0x31637661\",\"videoCodecName\":\"h264\",\"audioBitrateInKbps\":128,\"videoStartTime\":\"0.000000\",\"audioStartTime\":\"0.000000\",\"sar\":\"1:1\",\"audioStrmKey\":1,\"formatName\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"fps\":\"25.000000\",\"audio_bitrate\":\"128000\",\"audioCodecName\":\"aac\",\"audioBitrate\":\"128000\",\"pixFmt\":\"yuv420p\",\"audioCodecLongName\":\"AAC (Advanced Audio Coding)\",\"videoDuration\":\"344597.200000\",\"video_codec_name\":\"h264\",\"width\":1920,\"colorPrimaries\":\"\",\"videoStrmKey\":0},\"errorCode\":0}],\"params\":\"-disable_checkts -disable_checkts -i {{ op:download-0.output }}  -c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower -filter_complex \\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\" -c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 -enable_dolby -map_metadata -1 -loglevel warning {{ output,suffix=ts }} -y\",\"platform\":\"cpu\"},\"name\":\"vodTranscode\"},\"kind\":\"builtin\"},\"probe\":{\"op\":{\"args\":{\"input\":\"op:vodTranscode-1.output\"},\"name\":\"probe\"},\"kind\":\"builtin\"}},\"callback\":\"\",\"unfinished\":false},\"vars\":{\"userData\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"\",\"url\":\"44ce7d077ec141e9957a20b4c27ec578\"}},\"var_1\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"acs:ram::1532103438415171:role/AliyunMTSDefaultRole\",\"url\":\"http://oss-sim-vms-01.oss-cn-beijing.aliyuncs.com/video/1015/o/113303746052704665780001015/cc09c6ad278a4a39871ff4201b2b8c35.mp4\"}},\"var_2\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"acs:ram::1532103438415171:role/AliyunMTSDefaultRole\",\"url\":\"http://oss-sim-vms-pub-01.oss-cn-beijing.aliyuncs.com/video/1015/p/113303746052704665780001015/113303746059415552480011015/199a29cd815f4c0eae08b6695aee26ef-4.m3u8\"}}}}");
        estimateParam.setJobId(UUID.randomUUID().toString());
        estimateParam.setScheduleParams(JSON.parseObject("{\"pipelineId\":\"0c05ebf74a04450c8c99796221cb0fb1\",\"priority\":6,\"scheduleLevel\":\"STANDARD\",\"inputs\":[{\"duration\":344597.253,\"videoBitrate\":1491.037,\"avgFps\":25.0,\"size\":70124898816,\"format\":\"QuickTime / MOV\",\"fps\":25.0,\"width\":1920,\"audioCodec\":\"aac\",\"audioBitrate\":128.0,\"height\":1080,\"videoCodec\":\"h264\"}],\"configs\":[{\"duration\":344597.253,\"uhd\":\"1.0\",\"format\":\"m3u8\",\"fps\":25.0,\"id\":\"44ce7d077ec141e9957a20b4c27ec578\",\"audioCodec\":\"fdk_aac\",\"jobType\":\"transcode\",\"templateId\":\"7008883e17a946ad96d5efe540908dd0\",\"audioBitrate\":128.0,\"videoCodec\":\"x264\"}]}", ScheduleParams.class));
        estimateParam.setUserId("22");
        estimateParam.setCreateTime(new Date());
        estimateParam.setSliceProcess(true);
        estimateParam.setTag("test");
        estimateParam.setInvokeWorkerBrain(false);

        String result = "{\"code\":\"Success\",\"message\":\"\",\"executeMode\":\"DAG\",\"resourceRequests\":{\"cpu\":{\"quotaSet\":{},\"expectCostTime\":0,\"engineParams\":\"\"}},\"useMachineTypeOnly\":true,\"graphMap\":{\"10X\":{\"jobVertexs\":[{\"name\":\"chunkSpliter\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://landyji.oss-cn-beijing.aliyuncs.com/2b67_concat_logo.mp4\\\",\\\"aliyunUid\\\":\\\"1183004442902047\\\",\\\"roleArn\\\":\\\"acs:ram::1183004442902047:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:download-0.output\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":2,\\\"frames\\\":49742,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":-1,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"videoInopts\\\":\\\" \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"chunks\\\":2,\\\"frames\\\":49742,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-map_metadata -1\\\",\\\"videoInopts\\\":\\\" \\\",\\\"databusMap\\\":{},\\\"outputMap\\\":{\\\"0\\\":\\\"chunkSpliter_output0,suffix=mov\\\",\\\"1\\\":\\\"chunkSpliter_output1,suffix=mov\\\"},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":2,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":-2,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v0\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output0\\\",\\\"chunks\\\":2,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v1\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output1\\\",\\\"chunks\\\":2,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":1,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"download-0\\\":{\\\"op\\\":{\\\"name\\\":\\\"download\\\",\\\"args\\\":{\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"output\\\"}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"audioCodec\":\"aac\",\"format\":\"QuickTime / MOV\",\"duration\":3316.907,\"size\":123643565,\"fps\":15.083333,\"videoBitrate\":168.347,\"audioBitrate\":126.245}],\"configs\":[{\"jobType\":\"decode\"}]}},{\"name\":\"encoder_0\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":2,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_0.output\\\",\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":0,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_0\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=mp4\\\",\\\"chunks\\\":2,\\\"frames\\\":49742,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -movflags +faststart -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\" \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -pix_fmt yuv420p -color_range 2 -profile:v high -x264-params psy=0:trellis=2 -keyint_min 150 -g 150 -crf 23 -preset medium \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 126.245k -ar 44100 -ac 2 -profile:a aac_low \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":3316.907,\"size\":123643565,\"fps\":15.083333}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"mp4\",\"fps\":14.996506,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_1\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":2,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":1,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_1.output\\\",\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":1,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_1\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=mp4\\\",\\\"chunks\\\":2,\\\"frames\\\":49742,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":1,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -movflags +faststart -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\" \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -pix_fmt yuv420p -color_range 2 -profile:v high -x264-params psy=0:trellis=2 -keyint_min 150 -g 150 -crf 23 -preset medium \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 126.245k -ar 44100 -ac 2 -profile:a aac_low \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":3316.907,\"size\":123643565,\"fps\":15.083333}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"mp4\",\"fps\":14.996506,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"merger\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_2\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://landyji.oss-cn-beijing.aliyuncs.com/2b67speedetst.mp4\\\",\\\"aliyunUid\\\":\\\"1183004442902047\\\",\\\"roleArn\\\":\\\"acs:ram::1183004442902047:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"chunkMerger\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkMerge\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mp4\\\",\\\"chunks\\\":2,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -movflags +faststart -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -pix_fmt yuv420p -color_range 2 -profile:v high -x264-params psy=0:trellis=2 -keyint_min 150 -g 150 -crf 23 -preset medium \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 126.245k -ar 44100 -ac 2 -profile:a aac_low \\\",\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"},\\\"audioIn\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"probe\\\":{\\\"op\\\":{\\\"name\\\":\\\"probe\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkMerger.000\\\",\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"upload\\\":{\\\"op\\\":{\\\"name\\\":\\\"upload\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkMerger.000\\\",\\\"output\\\":\\\"var:var_2\\\",\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"Proberesults\\\":null,\\\"isProcess\\\":\\\"false\\\"}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":3316.907,\"size\":123643565,\"fps\":15.083333}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"mp4\",\"fps\":14.996506,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"copy\",\"width\":null}]}}],\"jobEdges\":[{\"name\":\"chunkSpliterAudio\",\"from\":\"chunkSpliter\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderIn_0\",\"from\":\"chunkSpliter\",\"to\":\"encoder_0\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_0\",\"from\":\"encoder_0\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_0\",\"from\":\"encoder_0\",\"to\":\"encoder_1\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_1\",\"from\":\"chunkSpliter\",\"to\":\"encoder_1\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_1\",\"from\":\"encoder_1\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"}],\"migratePolicy\":\"RESTART_ALL\"},\"20X\":{\"jobVertexs\":[{\"name\":\"chunkSpliter\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://landyji.oss-cn-beijing.aliyuncs.com/2b67_concat_logo.mp4\\\",\\\"aliyunUid\\\":\\\"1183004442902047\\\",\\\"roleArn\\\":\\\"acs:ram::1183004442902047:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:download-0.output\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":3,\\\"frames\\\":49742,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":-1,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"videoInopts\\\":\\\" \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"chunks\\\":3,\\\"frames\\\":49742,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-map_metadata -1\\\",\\\"videoInopts\\\":\\\" \\\",\\\"databusMap\\\":{},\\\"outputMap\\\":{\\\"0\\\":\\\"chunkSpliter_output0,suffix=mov\\\",\\\"1\\\":\\\"chunkSpliter_output1,suffix=mov\\\",\\\"2\\\":\\\"chunkSpliter_output2,suffix=mov\\\"},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":3,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":-2,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v0\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output0\\\",\\\"chunks\\\":3,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v1\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output1\\\",\\\"chunks\\\":3,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":1,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_v2\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSpliter.chunkSpliter_output2\\\",\\\"chunks\\\":3,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":2,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"download-0\\\":{\\\"op\\\":{\\\"name\\\":\\\"download\\\",\\\"args\\\":{\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"output\\\"}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"audioCodec\":\"aac\",\"format\":\"QuickTime / MOV\",\"duration\":3316.907,\"size\":123643565,\"fps\":15.083333,\"videoBitrate\":168.347,\"audioBitrate\":126.245}],\"configs\":[{\"jobType\":\"decode\"}]}},{\"name\":\"encoder_0\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":3,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_0.output\\\",\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":0,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_0\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=mp4\\\",\\\"chunks\\\":3,\\\"frames\\\":49742,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -movflags +faststart -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\" \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -pix_fmt yuv420p -color_range 2 -profile:v high -x264-params psy=0:trellis=2 -keyint_min 150 -g 150 -crf 23 -preset medium \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 126.245k -ar 44100 -ac 2 -profile:a aac_low \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":3316.907,\"size\":123643565,\"fps\":15.083333}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"mp4\",\"fps\":14.996506,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_1\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":3,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":1,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_1.output\\\",\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":1,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_1\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=mp4\\\",\\\"chunks\\\":3,\\\"frames\\\":49742,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":1,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -movflags +faststart -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\" \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -pix_fmt yuv420p -color_range 2 -profile:v high -x264-params psy=0:trellis=2 -keyint_min 150 -g 150 -crf 23 -preset medium \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 126.245k -ar 44100 -ac 2 -profile:a aac_low \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":3316.907,\"size\":123643565,\"fps\":15.083333}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"mp4\",\"fps\":14.996506,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"encoder_2\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mov\\\",\\\"chunks\\\":3,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":2,\\\"modeType\\\":3,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"chunkSpliter\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_2.output\\\",\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":2,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_2\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"output\\\":\\\"output, suffix=mp4\\\",\\\"chunks\\\":3,\\\"frames\\\":49742,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":2,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -movflags +faststart -map_metadata -1 -loglevel warning \\\",\\\"videoInopts\\\":\\\" \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -pix_fmt yuv420p -color_range 2 -profile:v high -x264-params psy=0:trellis=2 -keyint_min 150 -g 150 -crf 23 -preset medium \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 126.245k -ar 44100 -ac 2 -profile:a aac_low \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"h264\",\"duration\":3316.907,\"size\":123643565,\"fps\":15.083333}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"mp4\",\"fps\":14.996506,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"merger\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_2\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://landyji.oss-cn-beijing.aliyuncs.com/2b67speedetst.mp4\\\",\\\"aliyunUid\\\":\\\"1183004442902047\\\",\\\"roleArn\\\":\\\"acs:ram::1183004442902047:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"chunkMerger\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkMerge\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=mp4\\\",\\\"chunks\\\":3,\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"false\\\",\\\"chunkIndex\\\":0,\\\"modeType\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -movflags +faststart -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -pix_fmt yuv420p -color_range 2 -profile:v high -x264-params psy=0:trellis=2 -keyint_min 150 -g 150 -crf 23 -preset medium \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 126.245k -ar 44100 -ac 2 -profile:a aac_low \\\",\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"},\\\"audioIn\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"probe\\\":{\\\"op\\\":{\\\"name\\\":\\\"probe\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkMerger.000\\\",\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"upload\\\":{\\\"op\\\":{\\\"name\\\":\\\"upload\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkMerger.000\\\",\\\"output\\\":\\\"var:var_2\\\",\\\"duration\\\":3316.906077,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"Proberesults\\\":null,\\\"isProcess\\\":\\\"false\\\"}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":3316.907,\"size\":123643565,\"fps\":15.083333}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"mp4\",\"fps\":14.996506,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"copy\",\"width\":null}]}}],\"jobEdges\":[{\"name\":\"chunkSpliterAudio\",\"from\":\"chunkSpliter\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderIn_0\",\"from\":\"chunkSpliter\",\"to\":\"encoder_0\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_0\",\"from\":\"encoder_0\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_0\",\"from\":\"encoder_0\",\"to\":\"encoder_1\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_1\",\"from\":\"chunkSpliter\",\"to\":\"encoder_1\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_1\",\"from\":\"encoder_1\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_1\",\"from\":\"encoder_1\",\"to\":\"encoder_2\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderIn_2\",\"from\":\"chunkSpliter\",\"to\":\"encoder_2\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_2\",\"from\":\"encoder_2\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"}],\"migratePolicy\":\"RESTART_ALL\"},\"5X\":{}},\"speedXMessage\":{\"10X\":{\"code\":0,\"message\":\"success\"},\"20X\":{\"code\":0,\"message\":\"success\"},\"5X\":{\"code\":1,\"message\":\"noNeedParallel\"}}}";
        Mockito.when(httpUtil.sendPostRequest(Mockito.anyString(), Mockito.any())).thenReturn(result);
        Mockito.when(newEngineTaskQuotaEstimator.analysis(Mockito.any(), Mockito.any())).thenReturn(mockEstimateResult());

        JobAnalysisResult analysisResult = jobAnalysisService.estimateJobWithScheduleParams(estimateParam);
        Assert.assertTrue(analysisResult.isSuccess());
    }

    /**
     * 倍速任务测试 V2
     * 新版倍速
     * 入参scheduleParams中包含speedXRange（支持的倍速范围）
     * 返回graphMap字段 返回结果中包含excuteMode = SINGLE / DAG
     * 测试当dmes返回graphMap全空结果时，是否能够正常退避为single模式
     *
     */
    @Test
    public void testEstimateJobWithScheduleParamsForBoostJobV2(){
        JobAnalysisParam estimateParam = new JobAnalysisParam();
        estimateParam.setEngineModel("mps-transcode-new");
        estimateParam.setAnalysisMode("byScheduleParams");
        estimateParam.setEngineParams("{\"action\":{\"uid\":1532103438415171,\"ops\":{\"segment-3\":{\"op\":{\"args\":{\"params\":\"-analyzeduration 60000000 -probesize 60000000 -i {{ op:vodTranscode-1.output }} -c copy -f hls -hls_time 9.9 -hls_list_size 0 -hls_allow_cache 1 {{ output,suffix=m3u8 }} -y\"},\"name\":\"segment\"},\"kind\":\"builtin\"},\"download-0\":{\"op\":{\"args\":{\"output\":\"output\",\"input\":\"var:var_1\"},\"name\":\"download\"},\"kind\":\"builtin\"},\"upload\":{\"op\":{\"args\":{\"output\":\"var:var_2\",\"input\":\"op:segment-3.output\",\"m3u8Config\":{\"tsPattern\":\"${prefix}-${index|05d}.ts\"},\"isProcess\":\"false\"},\"name\":\"upload\"},\"kind\":\"builtin\"},\"vodTranscode-1\":{\"op\":{\"args\":{\"preProcessResults\":[{\"videoInfo\":{\"rotate\":\"\",\"avgFPS\":\"25.000000\",\"audioStreamCnt\":1,\"colorRange\":\"\",\"audioCodecTag\":\"0x6134706d\",\"format_long_name\":\"QuickTime / MOV\",\"bitrate\":\"1627985\",\"avg_fps\":25,\"formatLongName\":\"QuickTime / MOV\",\"audio_codec_name\":\"aac\",\"dar\":\"16:9\",\"audio_codec_tag_string\":\"mp4a\",\"sliceMode\":\"normal\",\"audioDuration\":\"344597.206009\",\"video_bitrate\":1491037,\"height\":1080,\"videoStreamCnt\":1,\"samplerate\":\"44100\",\"video_codec_long_name\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"level\":40,\"streams\":[{\"pix_fmt\":\"yuv420p\",\"r_frame_rate\":\"25/1\",\"start_pts\":0,\"extradata_size\":46,\"duration_ts\":4410844160,\"duration\":\"344597.200000\",\"bit_rate\":\"1491037\",\"sample_aspect_ratio\":\"1:1\",\"field_order\":\"progressive\",\"film_grain\":0,\"avg_frame_rate\":\"25/1\",\"codec_tag_string\":\"avc1\",\"closed_captions\":0,\"id\":\"0x1\",\"nb_frames\":\"8614930\",\"codec_long_name\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"height\":1080,\"chroma_location\":\"left\",\"time_base\":\"1/12800\",\"level\":40,\"coded_height\":1080,\"profile\":\"High\",\"bits_per_raw_sample\":\"8\",\"index\":0,\"nb_read_frames\":\"8614930\",\"tags\":{\"handler_name\":\"VideoHandler\",\"vendor_id\":\"[0][0][0][0]\",\"language\":\"und\",\"mov_stsd_entries\":\"1\"},\"codec_name\":\"h264\",\"start_time\":\"0.000000\",\"disposition\":{\"metadata\":0,\"original\":0,\"visual_impaired\":0,\"attached_pic\":0,\"forced\":0,\"still_image\":0,\"descriptions\":0,\"captions\":0,\"dub\":0,\"karaoke\":0,\"default\":1,\"timed_thumbnails\":0,\"hearing_impaired\":0,\"comment\":0,\"dependent\":0,\"lyrics\":0,\"clean_effects\":0},\"codec_tag\":\"0x31637661\",\"has_b_frames\":2,\"refs\":1,\"codec_time_base\":\"1/50\",\"width\":1920,\"display_aspect_ratio\":\"16:9\",\"coded_width\":1920,\"codec_type\":\"video\"},{\"r_frame_rate\":\"0/0\",\"start_pts\":0,\"extradata_size\":2,\"channel_layout\":\"stereo\",\"duration_ts\":15196736785,\"duration\":\"344597.206009\",\"bit_rate\":\"128000\",\"avg_frame_rate\":\"0/0\",\"codec_tag_string\":\"mp4a\",\"id\":\"0x2\",\"nb_frames\":\"14840607\",\"codec_long_name\":\"AAC (Advanced Audio Coding)\",\"time_base\":\"1/44100\",\"profile\":\"LC\",\"index\":1,\"tags\":{\"handler_name\":\"SoundHandler\",\"vendor_id\":\"[0][0][0][0]\",\"language\":\"und\",\"mov_stsd_entries\":\"1\"},\"codec_name\":\"aac\",\"start_time\":\"0.000000\",\"disposition\":{\"metadata\":0,\"original\":0,\"visual_impaired\":0,\"attached_pic\":0,\"forced\":0,\"still_image\":0,\"descriptions\":0,\"captions\":0,\"dub\":0,\"karaoke\":0,\"default\":1,\"timed_thumbnails\":0,\"hearing_impaired\":0,\"comment\":0,\"dependent\":0,\"lyrics\":0,\"clean_effects\":0},\"codec_tag\":\"0x6134706d\",\"sample_rate\":\"44100\",\"channels\":2,\"sample_fmt\":\"fltp\",\"codec_time_base\":\"1/44100\",\"bits_per_sample\":0,\"codec_type\":\"audio\"}],\"workAgent\":true,\"profile\":\"High\",\"format\":{\"duration\":\"344597.253000\",\"start_time\":\"0.000000\",\"bit_rate\":\"1627985\",\"size\":\"70124898816\",\"probe_score\":100,\"format_long_name\":\"QuickTime / MOV\",\"nb_programs\":0,\"nb_streams\":2,\"format_name\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"tags\":{\"major_brand\":\"isom\",\"encoder\":\"www.aliyun.com - Media Transcoding\",\"minor_version\":\"512\",\"compatible_brands\":\"isomiso2avc1mp41\"}},\"audio_codec_long_name\":\"AAC (Advanced Audio Coding)\",\"sampleRate\":\"44100\",\"ar\":\"44100\",\"channels\":2,\"aac_profile\":\"LC\",\"format_name\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"video_codec_tag_string\":\"avc1\",\"bitrateInKbps\":1627.98,\"videoCodecLongName\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"streamBasicInfo\":[{\"streamType\":\"video\",\"notificationMessage\":\"\",\"index\":0,\"codecName\":\"h264\",\"notificationCode\":0},{\"streamType\":\"audio\",\"notificationMessage\":\"\",\"index\":1,\"codecName\":\"aac\",\"notificationCode\":0}],\"videoBitrate\":1491037,\"duration\":344597.253,\"colorSpace\":\"\",\"videoBitrateInKbps\":1491.04,\"startTime\":\"0.000000\",\"audioCodecTagString\":\"mp4a\",\"subtitleStreamCnt\":0,\"color_space\":\"\",\"colorTransfer\":\"\",\"videoCodecTagString\":\"avc1\",\"videoCodecTag\":\"0x31637661\",\"videoCodecName\":\"h264\",\"audioBitrateInKbps\":128,\"videoStartTime\":\"0.000000\",\"audioStartTime\":\"0.000000\",\"sar\":\"1:1\",\"audioStrmKey\":1,\"formatName\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"fps\":\"25.000000\",\"audio_bitrate\":\"128000\",\"audioCodecName\":\"aac\",\"audioBitrate\":\"128000\",\"pixFmt\":\"yuv420p\",\"audioCodecLongName\":\"AAC (Advanced Audio Coding)\",\"videoDuration\":\"344597.200000\",\"video_codec_name\":\"h264\",\"width\":1920,\"colorPrimaries\":\"\",\"videoStrmKey\":0},\"errorCode\":0}],\"params\":\"-disable_checkts -disable_checkts -i {{ op:download-0.output }}  -c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower -filter_complex \\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\" -c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 -enable_dolby -map_metadata -1 -loglevel warning {{ output,suffix=ts }} -y\",\"platform\":\"cpu\"},\"name\":\"vodTranscode\"},\"kind\":\"builtin\"},\"probe\":{\"op\":{\"args\":{\"input\":\"op:vodTranscode-1.output\"},\"name\":\"probe\"},\"kind\":\"builtin\"}},\"callback\":\"\",\"unfinished\":false},\"vars\":{\"userData\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"\",\"url\":\"44ce7d077ec141e9957a20b4c27ec578\"}},\"var_1\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"acs:ram::1532103438415171:role/AliyunMTSDefaultRole\",\"url\":\"http://oss-sim-vms-01.oss-cn-beijing.aliyuncs.com/video/1015/o/113303746052704665780001015/cc09c6ad278a4a39871ff4201b2b8c35.mp4\"}},\"var_2\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"acs:ram::1532103438415171:role/AliyunMTSDefaultRole\",\"url\":\"http://oss-sim-vms-pub-01.oss-cn-beijing.aliyuncs.com/video/1015/p/113303746052704665780001015/113303746059415552480011015/199a29cd815f4c0eae08b6695aee26ef-4.m3u8\"}}}}");
        estimateParam.setJobId(UUID.randomUUID().toString());
        estimateParam.setScheduleParams(JSON.parseObject("{\"configs\":[{\"audioBitrate\":64.0,\"audioCodec\":\"fdk_aac\",\"duration\":182.4,\"format\":\"mp4\",\"fps\":25.0,\"height\":360,\"id\":\"da1a512fcbdb465a9585c9ac130b3736\",\"jobType\":\"transcode\",\"templateId\":\"S00000001-200010\",\"videoBitrate\":400.0,\"videoCodec\":\"x264\",\"width\":640}],\"inputs\":[{\"audioCodec\":\"aac\",\"avgFps\":25.0,\"duration\":182.4,\"format\":\"Apple HTTP Live Streaming\",\"fps\":25.0,\"height\":1080,\"size\":18287136,\"videoBitrate\":0.0,\"videoCodec\":\"h264\",\"width\":1920}],\"jobCreateTime\":1741003395000,\"jobId\":\"da1a512fcbdb465a9585c9ac130b3736\",\"multiSpeedDowngradePolicy\":\"NORMALSPEED\",\"pipelineId\":\"cee199ca33014462a1172facb9b8751a\",\"priority\":6,\"scheduleLevel\":\"BOOST\",\"speedXRange\":[\"5X\",\"10X\",\"20X\"]}", ScheduleParams.class));
        estimateParam.setUserId("22");
        estimateParam.setCreateTime(new Date());
        estimateParam.setSliceProcess(true);
        estimateParam.setTag("test");
        estimateParam.setInvokeWorkerBrain(false);

        String result = "{\"code\":\"Success\",\"message\":\"\",\"executeMode\":\"SINGLE\",\"resourceRequests\":{\"cpu\":{\"quotaSet\":{},\"expectCostTime\":0,\"engineParams\":\"\"}},\"useMachineTypeOnly\":true,\"graphMap\":{\"10X\":{},\"20X\":{},\"30X\":{},\"5X\":{}},\"speedXMessage\":{\"10X\":{\"code\":3,\"message\":\"noSupportParallel\"},\"20X\":{\"code\":3,\"message\":\"noSupportParallel\"},\"30X\":{\"code\":3,\"message\":\"noSupportParallel\"},\"5X\":{\"code\":3,\"message\":\"noSupportParallel\"}}}";
        Mockito.when(httpUtil.sendPostRequest(Mockito.anyString(), Mockito.any())).thenReturn(result);
        Mockito.when(newEngineTaskQuotaEstimator.analysis(Mockito.any(), Mockito.any())).thenReturn(mockEstimateResult());

        JobAnalysisResult analysisResult = jobAnalysisService.estimateJobWithScheduleParams(estimateParam);
        Assert.assertNotNull(analysisResult.getExecuteMode().equals(JobAnalysisResult.EnumExecuteMode.SINGLE));
        Assert.assertTrue(analysisResult.isSuccess());
    }


    /**
     * 倍速任务测试 V2
     * 旧版倍速 请求参数中包含sliceNum（切片数），不包含speedXRange（支持的倍速范围）
     * 返回graph字段 返回结果中不包含excuteMode
     *
     */
    @Test
    public void testEstimateJobWithScheduleParamsForBoostJobV3(){
        JobAnalysisParam estimateParam = new JobAnalysisParam();
        estimateParam.setEngineModel("mps-transcode-new");
        estimateParam.setAnalysisMode("byScheduleParams");
        estimateParam.setEngineParams("{\"action\":{\"uid\":1532103438415171,\"ops\":{\"segment-3\":{\"op\":{\"args\":{\"params\":\"-analyzeduration 60000000 -probesize 60000000 -i {{ op:vodTranscode-1.output }} -c copy -f hls -hls_time 9.9 -hls_list_size 0 -hls_allow_cache 1 {{ output,suffix=m3u8 }} -y\"},\"name\":\"segment\"},\"kind\":\"builtin\"},\"download-0\":{\"op\":{\"args\":{\"output\":\"output\",\"input\":\"var:var_1\"},\"name\":\"download\"},\"kind\":\"builtin\"},\"upload\":{\"op\":{\"args\":{\"output\":\"var:var_2\",\"input\":\"op:segment-3.output\",\"m3u8Config\":{\"tsPattern\":\"${prefix}-${index|05d}.ts\"},\"isProcess\":\"false\"},\"name\":\"upload\"},\"kind\":\"builtin\"},\"vodTranscode-1\":{\"op\":{\"args\":{\"preProcessResults\":[{\"videoInfo\":{\"rotate\":\"\",\"avgFPS\":\"25.000000\",\"audioStreamCnt\":1,\"colorRange\":\"\",\"audioCodecTag\":\"0x6134706d\",\"format_long_name\":\"QuickTime / MOV\",\"bitrate\":\"1627985\",\"avg_fps\":25,\"formatLongName\":\"QuickTime / MOV\",\"audio_codec_name\":\"aac\",\"dar\":\"16:9\",\"audio_codec_tag_string\":\"mp4a\",\"sliceMode\":\"normal\",\"audioDuration\":\"344597.206009\",\"video_bitrate\":1491037,\"height\":1080,\"videoStreamCnt\":1,\"samplerate\":\"44100\",\"video_codec_long_name\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"level\":40,\"streams\":[{\"pix_fmt\":\"yuv420p\",\"r_frame_rate\":\"25/1\",\"start_pts\":0,\"extradata_size\":46,\"duration_ts\":4410844160,\"duration\":\"344597.200000\",\"bit_rate\":\"1491037\",\"sample_aspect_ratio\":\"1:1\",\"field_order\":\"progressive\",\"film_grain\":0,\"avg_frame_rate\":\"25/1\",\"codec_tag_string\":\"avc1\",\"closed_captions\":0,\"id\":\"0x1\",\"nb_frames\":\"8614930\",\"codec_long_name\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"height\":1080,\"chroma_location\":\"left\",\"time_base\":\"1/12800\",\"level\":40,\"coded_height\":1080,\"profile\":\"High\",\"bits_per_raw_sample\":\"8\",\"index\":0,\"nb_read_frames\":\"8614930\",\"tags\":{\"handler_name\":\"VideoHandler\",\"vendor_id\":\"[0][0][0][0]\",\"language\":\"und\",\"mov_stsd_entries\":\"1\"},\"codec_name\":\"h264\",\"start_time\":\"0.000000\",\"disposition\":{\"metadata\":0,\"original\":0,\"visual_impaired\":0,\"attached_pic\":0,\"forced\":0,\"still_image\":0,\"descriptions\":0,\"captions\":0,\"dub\":0,\"karaoke\":0,\"default\":1,\"timed_thumbnails\":0,\"hearing_impaired\":0,\"comment\":0,\"dependent\":0,\"lyrics\":0,\"clean_effects\":0},\"codec_tag\":\"0x31637661\",\"has_b_frames\":2,\"refs\":1,\"codec_time_base\":\"1/50\",\"width\":1920,\"display_aspect_ratio\":\"16:9\",\"coded_width\":1920,\"codec_type\":\"video\"},{\"r_frame_rate\":\"0/0\",\"start_pts\":0,\"extradata_size\":2,\"channel_layout\":\"stereo\",\"duration_ts\":15196736785,\"duration\":\"344597.206009\",\"bit_rate\":\"128000\",\"avg_frame_rate\":\"0/0\",\"codec_tag_string\":\"mp4a\",\"id\":\"0x2\",\"nb_frames\":\"14840607\",\"codec_long_name\":\"AAC (Advanced Audio Coding)\",\"time_base\":\"1/44100\",\"profile\":\"LC\",\"index\":1,\"tags\":{\"handler_name\":\"SoundHandler\",\"vendor_id\":\"[0][0][0][0]\",\"language\":\"und\",\"mov_stsd_entries\":\"1\"},\"codec_name\":\"aac\",\"start_time\":\"0.000000\",\"disposition\":{\"metadata\":0,\"original\":0,\"visual_impaired\":0,\"attached_pic\":0,\"forced\":0,\"still_image\":0,\"descriptions\":0,\"captions\":0,\"dub\":0,\"karaoke\":0,\"default\":1,\"timed_thumbnails\":0,\"hearing_impaired\":0,\"comment\":0,\"dependent\":0,\"lyrics\":0,\"clean_effects\":0},\"codec_tag\":\"0x6134706d\",\"sample_rate\":\"44100\",\"channels\":2,\"sample_fmt\":\"fltp\",\"codec_time_base\":\"1/44100\",\"bits_per_sample\":0,\"codec_type\":\"audio\"}],\"workAgent\":true,\"profile\":\"High\",\"format\":{\"duration\":\"344597.253000\",\"start_time\":\"0.000000\",\"bit_rate\":\"1627985\",\"size\":\"70124898816\",\"probe_score\":100,\"format_long_name\":\"QuickTime / MOV\",\"nb_programs\":0,\"nb_streams\":2,\"format_name\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"tags\":{\"major_brand\":\"isom\",\"encoder\":\"www.aliyun.com - Media Transcoding\",\"minor_version\":\"512\",\"compatible_brands\":\"isomiso2avc1mp41\"}},\"audio_codec_long_name\":\"AAC (Advanced Audio Coding)\",\"sampleRate\":\"44100\",\"ar\":\"44100\",\"channels\":2,\"aac_profile\":\"LC\",\"format_name\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"video_codec_tag_string\":\"avc1\",\"bitrateInKbps\":1627.98,\"videoCodecLongName\":\"H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10\",\"streamBasicInfo\":[{\"streamType\":\"video\",\"notificationMessage\":\"\",\"index\":0,\"codecName\":\"h264\",\"notificationCode\":0},{\"streamType\":\"audio\",\"notificationMessage\":\"\",\"index\":1,\"codecName\":\"aac\",\"notificationCode\":0}],\"videoBitrate\":1491037,\"duration\":344597.253,\"colorSpace\":\"\",\"videoBitrateInKbps\":1491.04,\"startTime\":\"0.000000\",\"audioCodecTagString\":\"mp4a\",\"subtitleStreamCnt\":0,\"color_space\":\"\",\"colorTransfer\":\"\",\"videoCodecTagString\":\"avc1\",\"videoCodecTag\":\"0x31637661\",\"videoCodecName\":\"h264\",\"audioBitrateInKbps\":128,\"videoStartTime\":\"0.000000\",\"audioStartTime\":\"0.000000\",\"sar\":\"1:1\",\"audioStrmKey\":1,\"formatName\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"fps\":\"25.000000\",\"audio_bitrate\":\"128000\",\"audioCodecName\":\"aac\",\"audioBitrate\":\"128000\",\"pixFmt\":\"yuv420p\",\"audioCodecLongName\":\"AAC (Advanced Audio Coding)\",\"videoDuration\":\"344597.200000\",\"video_codec_name\":\"h264\",\"width\":1920,\"colorPrimaries\":\"\",\"videoStrmKey\":0},\"errorCode\":0}],\"params\":\"-disable_checkts -disable_checkts -i {{ op:download-0.output }}  -c:v libx264 -s 1920x1080 -pix_fmt yuv420p -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:tb-abrmax=7000 -keyint_min 50 -g 50 -bufsize:v 17500k -maxrate:v 14000k -crf 24 -preset slower -filter_complex \\\"[0:v]scale=1920:-2:flags=lanczos[sclv];[sclv]ans=0.85:2:3:sharp_delta_max=12\\\" -c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 -enable_dolby -map_metadata -1 -loglevel warning {{ output,suffix=ts }} -y\",\"platform\":\"cpu\"},\"name\":\"vodTranscode\"},\"kind\":\"builtin\"},\"probe\":{\"op\":{\"args\":{\"input\":\"op:vodTranscode-1.output\"},\"name\":\"probe\"},\"kind\":\"builtin\"}},\"callback\":\"\",\"unfinished\":false},\"vars\":{\"userData\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"\",\"url\":\"44ce7d077ec141e9957a20b4c27ec578\"}},\"var_1\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"acs:ram::1532103438415171:role/AliyunMTSDefaultRole\",\"url\":\"http://oss-sim-vms-01.oss-cn-beijing.aliyuncs.com/video/1015/o/113303746052704665780001015/cc09c6ad278a4a39871ff4201b2b8c35.mp4\"}},\"var_2\":{\"type\":\"url\",\"object\":{\"aliyunUid\":\"1532103438415171\",\"roleArn\":\"acs:ram::1532103438415171:role/AliyunMTSDefaultRole\",\"url\":\"http://oss-sim-vms-pub-01.oss-cn-beijing.aliyuncs.com/video/1015/p/113303746052704665780001015/113303746059415552480011015/199a29cd815f4c0eae08b6695aee26ef-4.m3u8\"}}}}");
        estimateParam.setJobId(UUID.randomUUID().toString());
        estimateParam.setScheduleParams(JSON.parseObject("{\"configs\":[{\"audioBitrate\":128.0,\"audioCodec\":\"fdk_aac\",\"audioOnly\":false,\"duration\":11704.036,\"extend\":{},\"format\":\"m3u8\",\"fps\":25.0,\"height\":720,\"id\":\"cac3dedf733642ea8f3154e41b31df0a\",\"jobType\":\"transcode\",\"templateId\":\"b94f5d9130de40a4ad880513078d783e\",\"uhd\":\"1.0\",\"videoCodec\":\"x264\",\"width\":1280}],\"inputs\":[{\"audioBitrate\":128.0,\"audioCodec\":\"aac\",\"avgFPS\":25.0,\"duration\":11704.036,\"format\":\"QuickTime / MOV\",\"fps\":25.0,\"height\":1080,\"size\":7348471397,\"videoBitrate\":4887.185,\"videoCodec\":\"h264\",\"width\":1920}],\"pipelineId\":\"d54f510d583b47048ea74a6d9ae3c538\",\"priority\":9,\"sliceNum\":21}", ScheduleParams.class));
        estimateParam.setUserId("22");
        estimateParam.setCreateTime(new Date());
        estimateParam.setSliceProcess(true);
        estimateParam.setTag("test");
        estimateParam.setInvokeWorkerBrain(false);
        //StringBuffer stringBuffer = new StringBuffer();
        //stringBuffer.append("{\"code\":\"Success\",\"message\":\"\",\"graph\":{\"jobVertexs\":[{\"name\":\"encoder_0\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_0.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_0\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":0,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_1\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":1,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":1,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_1.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":1,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":1,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_1\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":1,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_2\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":2,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":2,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_2.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":2,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":2,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_2\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":2,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_3\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_3.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":3,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":3,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_3\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":3,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_4\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":4,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":4,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_4.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":4,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":4,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_4\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":4,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_5\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":5,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":5,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_5.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":5,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":5,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_5\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":5,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_6\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":6,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":6,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_6.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":6,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":6,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_6\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":6,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_7\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":7,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":7,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_7.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":7,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":7,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_7\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":7,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_8\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":8,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":8,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_8.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":8,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":8,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_8\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":8,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_9\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":9,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":9,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_9.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":9,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":9,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_9\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":9,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_10\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":10,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":10,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_10.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":10,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":10,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_10\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":10,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_11\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":11,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":11,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_11.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":11,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":11,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_11\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":11,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_12\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":12,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":12,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_12.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":12,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":12,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_12\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":12,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_13\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":13,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":13,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_13.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":13,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":13,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_13\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":13,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_14\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":14,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":14,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_14.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":14,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":14,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_14\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":14,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_15\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":15,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":15,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_15.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":15,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":15,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_15\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":15,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_16\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":16,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":16,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_16.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":16,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":16,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_16\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":16,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_17\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":17,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":17,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_17.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":17,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":17,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_17\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":17,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_18\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":18,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":18,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_18.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":18,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":18,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_18\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":18,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_19\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":19,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":19,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_19.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":19,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":19,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_19\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":19,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"encoder_20\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_1\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"avSpliter\\\":{\\\"op\\\":{\\\"name\\\":\\\"avSpliter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkSeekPuller.chunkSeekPuller_out\\\",\\\"outputAudio\\\":\\\"avSpliter_out_a,suffix=mov\\\",\\\"outputVideo\\\":\\\"avSpliter_out_v,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":20,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"chunkSeekPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkSeekPuller\\\",\\\"args\\\":{\\\"uid\\\":\\\"1762491171530701\\\",\\\"role\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"url\\\":\\\"http://oss-prod-cms-v-a.oss-cn-beijing.aliyuncs.com/video/1015/o/135166684815880192151001015/919ccfab0c0c445ea17e906cdc661f4d.mp4\\\",\\\"input\\\":\\\"var:var_1\\\",\\\"output\\\":\\\"chunkSeekPuller_out,suffix=mov\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":20,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:encoder_20.output\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":20,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"databusPusher_a\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_a\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":20,\\\"databusMap\\\":{\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"isAudio\\\":true,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_20\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileEncoder\\\",\\\"args\\\":{\\\"input\\\":\\\"op:avSpliter.avSpliter_out_v\\\",\\\"output\\\":\\\"output, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"needSub\\\":\\\"true\\\",\\\"chunkIndex\\\":20,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":1280}]}},{\"name\":\"merger\",\"engineParams\":\"{\\\"vars\\\":{\\\"var_2\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://oss-prod-cms-v-a-pub.oss-cn-beijing.aliyuncs.com/video/1015/p/135166684815880192151001015/135166684822591078651011015/8f81a9ba035247a39d15ab703035710a-3.m3u8\\\",\\\"aliyunUid\\\":\\\"1762491171530701\\\",\\\"roleArn\\\":\\\"acs:ram::1762491171530701:role/AliyunMTSDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"chunkMerger\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkMerge\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=ts\\\",\\\"chunks\\\":21,\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-enable_dolby -map_metadata -1 -loglevel warning \\\",\\\"videoOutopts\\\":\\\"-c:v libx264 -s 1280x720 -pix_fmt yuv420p -color_range 1 -colorspace bt709 -color_trc bt709 -color_primaries bt709 -x264-params ref=4:tb-abrmax=1500:sharp=1-1:psy=0:trellis=2 -keyint_min 50 -g 50 -bufsize:v 6000k -maxrate:v 3000k -qmin 20 -qmax 45 -crf 26 -preset slower \\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2 \\\",\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"},\\\"audioIn\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"probe\\\":{\\\"op\\\":{\\\"name\\\":\\\"probe\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkMerger.000\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"segment-3\\\":{\\\"op\\\":{\\\"name\\\":\\\"segment\\\",\\\"args\\\":{\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"params\\\":\\\"-analyzeduration 60000000 -probesize 60000000 -i {{ op:chunkMerger.000 }} -c copy -f hls -hls_time 9.9 -hls_list_size 0 -hls_allow_cache 1 {{ output,suffix=m3u8 }} -y\\\",\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"upload\\\":{\\\"op\\\":{\\\"name\\\":\\\"upload\\\",\\\"args\\\":{\\\"input\\\":\\\"op:segment-3.output\\\",\\\"output\\\":\\\"var:var_2\\\",\\\"duration\\\":11704,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"Proberesults\\\":null,\\\"isProcess\\\":\\\"false\\\"}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1920,\"Height\":1080,\"videoCodec\":\"yuv\",\"duration\":11704.036,\"size\":7348471397,\"fps\":25}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"m3u8\",\"fps\":25,\"height\":720,\"jobType\":\"transcode\",\"videoCodec\":\"copy\",\"width\":1280}]}}],\"jobEdges\":[{\"name\":\"encoderOut_0\",\"from\":\"encoder_0\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_0\",\"from\":\"encoder_0\",\"to\":\"encoder_1\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_1\",\"from\":\"encoder_1\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_1\",\"from\":\"encoder_1\",\"to\":\"encoder_2\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_2\",\"from\":\"encoder_2\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_2\",\"from\":\"encoder_2\",\"to\":\"encoder_3\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_3\",\"from\":\"encoder_3\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_3\",\"from\":\"encoder_3\",\"to\":\"encoder_4\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_4\",\"from\":\"encoder_4\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_4\",\"from\":\"encoder_4\",\"to\":\"encoder_5\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_5\",\"from\":\"encoder_5\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_5\",\"from\":\"encoder_5\",\"to\":\"encoder_6\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_6\",\"from\":\"encoder_6\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_6\",\"from\":\"encoder_6\",\"to\":\"encoder_7\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_7\",\"from\":\"encoder_7\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_7\",\"from\":\"encoder_7\",\"to\":\"encoder_8\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_8\",\"from\":\"encoder_8\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_8\",\"from\":\"encoder_8\",\"to\":\"encoder_9\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_9\",\"from\":\"encoder_9\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_9\",\"from\":\"encoder_9\",\"to\":\"encoder_10\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_10\",\"from\":\"encoder_10\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_10\",\"from\":\"encoder_10\",\"to\":\"encoder_11\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_11\",\"from\":\"encoder_11\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_11\",\"from\":\"encoder_11\",\"to\":\"encoder_12\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_12\",\"from\":\"encoder_12\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_12\",\"from\":\"encoder_12\",\"to\":\"encoder_13\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_13\",\"from\":\"encoder_13\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_13\",\"from\":\"encoder_13\",\"to\":\"encoder_14\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_14\",\"from\":\"encoder_14\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_14\",\"from\":\"encoder_14\",\"to\":\"encoder_15\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_15\",\"from\":\"encoder_15\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_15\",\"from\":\"encoder_15\",\"to\":\"encoder_16\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_16\",\"from\":\"encoder_16\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_16\",\"from\":\"encoder_16\",\"to\":\"encoder_17\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_17\",\"from\":\"encoder_17\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_17\",\"from\":\"encoder_17\",\"to\":\"encoder_18\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_18\",\"from\":\"encoder_18\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_18\",\"from\":\"encoder_18\",\"to\":\"encoder_19\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_19\",\"from\":\"encoder_19\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoder_19\",\"from\":\"encoder_19\",\"to\":\"encoder_20\",\"mode\":\"PULL\",\"fromStatus\":\"NOT_QUEUING\"},{\"name\":\"encoderOut_20\",\"from\":\"encoder_20\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"}],\"migratePolicy\":\"RESTART_ALL\"}}");
        String result = "{\"code\":\"Success\",\"message\":\"\",\"graph\":{\"jobVertexs\":[{\"name\":\"decoder\",\"engineParams\":\"{\\\"vars\\\":{\\\"fc4606de2fcd49e3be65c092a830e6d0\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://broadscope-wanxiang-new.oss-cn-beijing.aliyuncs.com/wanx/1742279520523420128/image_to_video/244719047f80417ea9ac1a6220ab95f1_0_origin.mp4\\\",\\\"aliyunUid\\\":\\\"1878810900690028\\\",\\\"roleArn\\\":\\\"acs:ram::1878810900690028:role/AliyunICEDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"decoder\\\":{\\\"op\\\":{\\\"name\\\":\\\"frameDecoderWorker\\\",\\\"args\\\":{\\\"input\\\":\\\"op:download-fc4606de2fcd49e3be65c092a830e6d0.download_output_fc4606de2fcd49e3be65c092a830e6d0\\\",\\\"output\\\":\\\"simple_transcode_output_fc4606de2fcd49e3be65c092a830e6d0,suffix=mp4\\\",\\\"chunks\\\":1,\\\"frames\\\":161,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":2,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"videoInopts\\\":\\\" -c:v rawvideo\\\",\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"decoder\\\":\\\"databus//ip:1935\\\"},\\\"audioOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"videoOnly\\\":1,\\\"preoverlapLen\\\":10,\\\"afteroverlapLen\\\":3,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"download-fc4606de2fcd49e3be65c092a830e6d0\\\":{\\\"op\\\":{\\\"name\\\":\\\"download\\\",\\\"args\\\":{\\\"input\\\":\\\"var:fc4606de2fcd49e3be65c092a830e6d0\\\",\\\"output\\\":\\\"download_output_fc4606de2fcd49e3be65c092a830e6d0\\\"}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1008,\"Height\":896,\"videoCodec\":\"h264\",\"format\":\"QuickTime / MOV\",\"duration\":5.367,\"size\":4536064,\"fps\":30,\"videoBitrate\":6755.666}],\"configs\":[{\"jobType\":\"decode\"}]}},{\"name\":\"normalFilter_0\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPuller\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPuller\\\",\\\"args\\\":{\\\"output\\\":\\\"000, suffix=y4m\\\",\\\"chunks\\\":1,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":2,\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"decoder\\\":\\\"databus//ip:1935\\\"}},\\\"videoOnly\\\":1,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"normalFilter_0\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkFileFilter\\\",\\\"args\\\":{\\\"input\\\":\\\"op:databusPuller.000\\\",\\\"chunks\\\":1,\\\"frames\\\":161,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":2,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"videoFilter\\\":\\\"uhdv1=model_dir=/home/<USER>/tools/data/uhdv141_models:model_version=1:guided_mode=2:postproc=strong:postproc_mode=1:uvproc=onnx:model_type_uv=bin:sr_en=1:model_dir_hy=/home/<USER>/tools/data/uhdv141_models:num_lowres_split_models=1:model_name_lowres=HQ13_LQ22_modelv5_refine_on_LQ20_1_e750:hdr_interp_name=sdr2hdr_interp_parm_0803v12.file:model_type_hy=bin:interp_name=hdr2sdr_interp_parm.file:extreme_constrain_en=0:dtf_sigma_r_2=23:s_a_2=10,scale=trunc(iw*0.75/2)*2:trunc(ih*0.75/2)*2:flags=lanczos\\\",\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"decoder\\\":\\\"databus//ip:1935\\\"},\\\"videoOut\\\":{\\\"encoder_0\\\":\\\"databus//ip:1935\\\"}},\\\"videoOnly\\\":1,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1008,\"Height\":896,\"videoCodec\":\"yuv\",\"duration\":5.367,\"size\":4536064,\"fps\":30}],\"configs\":[{\"jobType\":\"transcode\",\"mode\":\"single\",\"uhd\":\"1.0.0\"}]}},{\"name\":\"encoder_0\",\"engineParams\":\"{\\\"action\\\":{\\\"ops\\\":{\\\"databusPusher\\\":{\\\"op\\\":{\\\"name\\\":\\\"databusPusher\\\",\\\"args\\\":{\\\"input\\\":\\\"op:pass2-fc4606de2fcd49e3be65c092a830e6d0.transcode_output_fc4606de2fcd49e3be65c092a830e6d0\\\",\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{\\\"videoOut\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"videoOnly\\\":1,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"encoder_0\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkStreamEncoder\\\",\\\"args\\\":{\\\"output\\\":\\\"simple_transcode_output_fc4606de2fcd49e3be65c092a830e6d0,suffix=mp4\\\",\\\"chunks\\\":1,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":2,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-probesize 500000000\\\",\\\"videoOutopts\\\":\\\"-vsync 0 -c:v libx264 -color_range 1\\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2\\\",\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"encoder_0\\\":\\\"databus//ip:1935\\\"}},\\\"videoOnly\\\":1,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"pass1-fc4606de2fcd49e3be65c092a830e6d0\\\":{\\\"op\\\":{\\\"name\\\":\\\"vodTranscode\\\",\\\"args\\\":{\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"videoOnly\\\":1,\\\"params\\\":\\\"-color_follow_source -vsync 0 -i {{op:encoder_0.simple_transcode_output_fc4606de2fcd49e3be65c092a830e6d0}} -c:a copy -c:v libx264 -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:bitrate=7500:vbv-maxrate=9500:pass=1:stats={{pass1_output_fc4606de2fcd49e3be65c092a830e6d0}}:cutree=1 -preset slow {{transcode_output_fc4606de2fcd49e3be65c092a830e6d0,suffix=mov}}\\\",\\\"platform\\\":\\\"cpu\\\",\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"pass2-fc4606de2fcd49e3be65c092a830e6d0\\\":{\\\"op\\\":{\\\"name\\\":\\\"vodTranscode\\\",\\\"args\\\":{\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"videoOnly\\\":1,\\\"params\\\":\\\"-color_follow_source -vsync 0 -i {{op:encoder_0.simple_transcode_output_fc4606de2fcd49e3be65c092a830e6d0}} -c:a copy -c:v libx264 -color_range 1 -x264-params roi=1.5:hierarchical-b=1:ref=4:trellis=2:psy=0:aq-mode=2:aq-strength=1.0:bitrate=7500:vbv-maxrate=9500:pass=2:stats={{op:pass1-fc4606de2fcd49e3be65c092a830e6d0.pass1_output_fc4606de2fcd49e3be65c092a830e6d0}}:cutree=1 -preset slow {{transcode_output_fc4606de2fcd49e3be65c092a830e6d0,suffix=ts}}\\\",\\\"platform\\\":\\\"cpu\\\",\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1008,\"Height\":896,\"videoCodec\":\"yuv\",\"duration\":5.367,\"size\":4536064,\"fps\":30}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"mp4\",\"fps\":30,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"x264\",\"width\":null}]}},{\"name\":\"merger\",\"engineParams\":\"{\\\"vars\\\":{\\\"upload_output_fc4606de2fcd49e3be65c092a830e6d0\\\":{\\\"type\\\":\\\"url\\\",\\\"object\\\":{\\\"url\\\":\\\"http://broadscope-wanxiang-new.oss-cn-beijing.aliyuncs.com/wanx/1742279520523420128/video_super_resolution/12cb7b377c6f4decbcc8b8451b90dc61super_resolution.mp4\\\",\\\"aliyunUid\\\":\\\"1878810900690028\\\",\\\"roleArn\\\":\\\"acs:ram::1878810900690028:role/AliyunICEDefaultRole\\\",\\\"referer\\\":\\\"\\\"}}},\\\"action\\\":{\\\"ops\\\":{\\\"chunkMerger\\\":{\\\"op\\\":{\\\"name\\\":\\\"chunkMerge\\\",\\\"args\\\":{\\\"output\\\":\\\"000,suffix=mp4\\\",\\\"chunks\\\":1,\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"modeType\\\":2,\\\"codecName\\\":\\\"h264\\\",\\\"movStsdEntries\\\":1,\\\"containerOutopts\\\":\\\"-probesize 500000000\\\",\\\"videoOutopts\\\":\\\"-vsync 0 -c:v libx264 -color_range 1\\\",\\\"audioOutopts\\\":\\\"-c:a libfdk_aac -b:a 128k -ar 44100 -ac 2\\\",\\\"databusMap\\\":{\\\"videoIn\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"},\\\"audioIn\\\":{\\\"merger\\\":\\\"databus//ip:1935\\\"}},\\\"videoOnly\\\":1,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"probe-fc4606de2fcd49e3be65c092a830e6d0\\\":{\\\"op\\\":{\\\"name\\\":\\\"probe\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkMerger.000\\\",\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"},\\\"upload-fc4606de2fcd49e3be65c092a830e6d0\\\":{\\\"op\\\":{\\\"name\\\":\\\"upload\\\",\\\"args\\\":{\\\"input\\\":\\\"op:chunkMerger.000\\\",\\\"output\\\":\\\"var:upload_output_fc4606de2fcd49e3be65c092a830e6d0\\\",\\\"seekStart\\\":0,\\\"chunkIndex\\\":0,\\\"databusMap\\\":{},\\\"videoOnly\\\":1,\\\"Proberesults\\\":null}},\\\"kind\\\":\\\"builtin\\\"}}}}\",\"engineModel\":\"mps-transcode-new\",\"scheduleParams\":{\"inputs\":[{\"width\":1008,\"Height\":896,\"videoCodec\":\"yuv\",\"duration\":5.367,\"size\":4536064,\"fps\":30}],\"configs\":[{\"audioCodec\":\"fdk_aac\",\"format\":\"mp4\",\"fps\":30,\"height\":null,\"jobType\":\"transcode\",\"videoCodec\":\"copy\",\"width\":null}]}}],\"jobEdges\":[{\"name\":\"decoderAudio\",\"from\":\"decoder\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"workerIn_0\",\"from\":\"decoder\",\"to\":\"normalFilter_0\",\"mode\":\"PULL\",\"fromStatus\":\"RUNNING\"},{\"name\":\"workerOut_0\",\"from\":\"normalFilter_0\",\"to\":\"encoder_0\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"},{\"name\":\"encoderOut_0\",\"from\":\"encoder_0\",\"to\":\"merger\",\"mode\":\"PUSH\",\"fromStatus\":\"RUNNING\"}],\"migratePolicy\":\"RESTART_ALL\"}}";
        Mockito.when(httpUtil.sendPostRequest(Mockito.anyString(), Mockito.any())).thenReturn(result);
        Mockito.when(newEngineTaskQuotaEstimator.analysis(Mockito.any(), Mockito.any())).thenReturn(mockEstimateResult());

        JobAnalysisResult analysisResult = jobAnalysisService.estimateJobWithScheduleParams(estimateParam);
        Assert.assertNotNull(analysisResult.getExecuteMode().equals(JobAnalysisResult.EnumExecuteMode.DAG));
        Assert.assertTrue(analysisResult.isSuccess());
    }

    private EstimateResult mockEstimateResult(){
        EstimateResult estimateResult = new EstimateResult();
        estimateResult.setExpectCostTime(100L);
        Map<String, Long> quotaSet = new HashMap<>();
        quotaSet.put("cpu", 4000L);
        quotaSet.put("disk", 10000L);
        estimateResult.setQuotaSet(quotaSet);

        return estimateResult;
    }
}
