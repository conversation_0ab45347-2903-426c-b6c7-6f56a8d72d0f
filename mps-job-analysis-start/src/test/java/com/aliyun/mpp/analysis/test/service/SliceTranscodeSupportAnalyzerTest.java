package com.aliyun.mpp.analysis.test.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.service.impl.analysis.SliceTranscodeSupportAnalyzer;
import com.aliyun.mpp.analysis.domain.types.JobAnalysisResult;
import com.aliyun.mpp.analysis.domain.types.JobParam;
import com.aliyun.mpp.analysis.domain.types.OSSFile;
import com.aliyun.mpp.analysis.domain.types.VideoInfo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Created by lihe.lh on 2020/2/13.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SliceTranscodeSupportAnalyzerTest extends BaseTest{
    @Resource
    private SliceTranscodeSupportAnalyzer sliceTranscodeSupportAnalyzer;

    @Test
    public void testSupportSliceTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addBasicAudioConfig(customParams);
        addBasicVideoConfig(customParams);

        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setHeight(480);
        videoInfo.setHeight(640);
        videoInfo.setDuration(30 * 60.0);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        Assert.assertTrue(sliceTranscodeSupportAnalyzer.supportSliceTranscode(jobParam, videoInfo));
    }

    @Test
    public void testNotSupportSliceTranscodeForShortVideo(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addBasicAudioConfig(customParams);
        addBasicVideoConfig(customParams);

        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setHeight(480);
        videoInfo.setHeight(640);
        videoInfo.setDuration(60.0);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        Assert.assertFalse(sliceTranscodeSupportAnalyzer.supportSliceTranscode(jobParam, videoInfo));
    }

    @Test
    public void testNotSupportSliceTranscodeForVideoCopy(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addVideoCopy(customParams);
        addBasicAudioConfig(customParams);

        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setDuration(60.0);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        Assert.assertFalse(sliceTranscodeSupportAnalyzer.supportSliceTranscode(jobParam, videoInfo));
    }

    @Test
    public void testNotSupportSliceTranscodeForAudioOnlyInput(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addBasicAudioConfig(customParams);
        addBasicVideoConfig(customParams);

        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setDuration(600.0);
        videoInfo.setWidth(null);
        videoInfo.setHeight(null);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        Assert.assertFalse(sliceTranscodeSupportAnalyzer.supportSliceTranscode(jobParam, videoInfo));
    }


    @Test
    public void testNotSupportSliceTranscodeForAudioOnlyOutput(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addBasicVideoConfig(customParams);

        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setDuration(600.0);
        videoInfo.setWidth(null);
        videoInfo.setHeight(null);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        Assert.assertFalse(sliceTranscodeSupportAnalyzer.supportSliceTranscode(jobParam, videoInfo));
    }

   /* @Test
    public void testGenerateSliceTranscodeDAG(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addBasicAudioConfig(customParams);
        addBasicVideoConfig(customParams);

        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setHeight(1280);
        videoInfo.setHeight(720);
        videoInfo.setDuration(30 * 60.0);
        videoInfo.setFile(new OSSFile("http://mts-sh-out.oss-cn-shanghai.aliyuncs.com/lihe/input/1080p_input.mp4", 1253406881704637L, null));

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());

        JobAnalysisResult result = sliceTranscodeSupportAnalyzer.generateSliceTranscodeDAG(jobParam, videoInfo);
        Assert.assertTrue(result.isSuccess());
    }
*/

}
