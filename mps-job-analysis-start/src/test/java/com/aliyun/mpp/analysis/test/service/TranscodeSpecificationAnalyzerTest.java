package com.aliyun.mpp.analysis.test.service;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.mpp.analysis.domain.param.JobAnalysisParam;
import com.aliyun.mpp.analysis.domain.service.impl.analysis.TranscodeSpecificationAnalyzer;
import com.aliyun.mpp.analysis.domain.service.impl.config.TranscodeSpecification;
import com.aliyun.mpp.analysis.domain.types.*;
import org.junit.Assert;
import org.junit.Test;

/**
 * Created by lihe.lh on 2020/2/12.
 */
public class TranscodeSpecificationAnalyzerTest extends BaseTest{
    @Test
    public void testTranscodeCopy(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addVideoCopy(customParams);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.COPY);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }

    @Test
    public void testAudioConfigTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addBasicAudioConfig(customParams);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.AUDIO);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }

    @Test
    public void testAudioInputVideoConfigTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addBasicAudioConfig(customParams);
        addBasicVideoConfig(customParams);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setWidth(null);
        videoInfo.setHeight(null);

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.AUDIO);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }

    @Test
    public void test4KResoNormalTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addOutputReso(customParams, 3840, 2160);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution._4K);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }

    @Test
    public void test2KResoNormalTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addOutputReso(customParams, 2560, 1440);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution._2K);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }


    @Test
    public void testFHDResoNormalTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addOutputReso(customParams, 1920, 1080);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.FHD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }


    @Test
    public void testHDResoNormalTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addOutputReso(customParams, 1280, 720);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.HD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }


    @Test
    public void testSDResoNormalTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addOutputReso(customParams, 848, 480);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.SD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }


    @Test
    public void testResoCheckValidTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addOutputReso(customParams, 2560, 1440);

        JSONObject transConfig = new JSONObject();
        transConfig.put("isCheckReso", true);
        customParams.put("transConfig", transConfig);


        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setHeight(480);
        videoInfo.setWidth(848);

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.SD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }


    @Test
    public void testResoCheckInValidTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addOutputReso(customParams, 1280, 720);

        JSONObject transConfig = new JSONObject();
        transConfig.put("isCheckReso", true);
        customParams.put("transConfig", transConfig);


        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setHeight(1080);

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.HD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }

    @Test
    public void testNHV1OfOutputResoTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addNarrowBandV1(customParams);
        addOutputReso(customParams, 1280, 720);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.HD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NH_V1);
    }


    @Test
    public void testNHV1OfInputResoTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addNarrowBandV1(customParams);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();
        videoInfo.setHeight(480);
        videoInfo.setWidth(848);

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.SD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NH_V1);
    }

    @Test
    public void testNHV1OAndH265Transcode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addNarrowBandV1(customParams);
        addOutputReso(customParams, 1280, 720);
        addH265Codec(customParams);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_265);
        Assert.assertTrue(specification.getResolution() == EnumResolution.HD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NH_V1);
    }


    @Test
    public void testH265NormalTranscode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addOutputReso(customParams, 1280, 720);
        addH265Codec(customParams);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_265);
        Assert.assertTrue(specification.getResolution() == EnumResolution.HD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }


    @Test
    public void testNHV2Transcode(){
        JSONObject params = JSONObject.parseObject(baseParams);
        params.put(PROCESS_LOGIC, CUSTOMTRANSCODE);
        JSONObject customParams = new JSONObject();
        addOutputReso(customParams, 1280, 720);
        addNarrowBandV2(customParams);

        JobAnalysisParam param = generateTranscodeJobParam(params, customParams);
        JobParam jobParam = JobParam.parseParam(param.getEngineParams());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.HD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NH_V2);
    }


    @Test
    public void testSliceVideoTranscode(){
        JSONObject params = JSONObject.parseObject("{\"jobId\":\"219b01b0b9824a779e0b0748f0c1faa4\",\"params\":\"{\\\"arguments\\\":\\\"{\\\\\\\"output_oss_host\\\\\\\":\\\\\\\"oss-cn-shanghai.aliyuncs.com\\\\\\\",\\\\\\\"output_oss_object\\\\\\\":\\\\\\\"lihe/temp/mps/34fe898c-b794-454e-af02-113d36224ab6\\\\\\\",\\\\\\\"output_oss_bucket\\\\\\\":\\\\\\\"mts-sh-out\\\\\\\",\\\\\\\"custom_param\\\\\\\":{\\\\\\\"custom_params\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"audio\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"samplerate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"44100\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"channels\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"bitrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"64\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"codec\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"aac\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"container\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"format\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"mp4\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"transFeatures\\\\\\\\\\\\\\\":{},\\\\\\\\\\\\\\\"outputfile\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"isProcess\\\\\\\\\\\\\\\":false},\\\\\\\\\\\\\\\"transConfig\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"transMode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"onepass\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"video\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"crf\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"27\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"width\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"848\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"bitrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"400\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"codec\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"H.264\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"preset\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"medium\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"pixFmt\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"yuv420p\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"maxrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"900\\\\\\\\\\\\\\\"}}\\\\\\\"},\\\\\\\"parallelEnv\\\\\\\":{\\\\\\\"hasAudio\\\\\\\":true,\\\\\\\"taskType\\\\\\\":\\\\\\\"sliceTranscode\\\\\\\",\\\\\\\"sliceVer\\\\\\\":\\\\\\\"slice_v2_mp4\\\\\\\",\\\\\\\"instanceId\\\\\\\":8,\\\\\\\"sliceCount\\\\\\\":13,\\\\\\\"ossTempPrefix\\\\\\\":\\\\\\\"parallel/output/52a2e1cf9ba04c0191d13cfcf459c3c7\\\\\\\"},\\\\\\\"aliyunUid\\\\\\\":1253406881704637}\\\",\\\"debugInfo\\\":\\\"w*h=230400.0\\\",\\\"expire\\\":86400,\\\"input_param\\\":\\\"{\\\\\\\"param\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"error_code\\\\\\\\\\\\\\\":0}\\\\\\\",\\\\\\\"data\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"type\\\\\\\\\\\\\\\":1,\\\\\\\\\\\\\\\"url\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"http://mts-sh-out.oss-cn-shanghai.aliyuncs.com/lihe/input/yunxi_ls.mp4\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"videoInfo\\\\\\\":{\\\\\\\"fileSize\\\\\\\":619015932,\\\\\\\"duration\\\\\\\":1577,\\\\\\\"height\\\\\\\":1080,\\\\\\\"width\\\\\\\":1920,\\\\\\\"bitRate\\\\\\\":3139.65}}\\\",\\\"mixEncodingSwitch\\\":0,\\\"mixTag\\\":0,\\\"nvencTag\\\":1,\\\"oHeight\\\":0,\\\"oWidth\\\":640,\\\"process_logic\\\":\\\"sliceTranscode\\\",\\\"userData\\\":{\\\"mtsJobId\\\":\\\"219b01b0b9824a779e0b0748f0c1faa4\\\",\\\"requestId\\\":\\\"50E166FC-7855-410B-B8C8-71ED3378D810\\\",\\\"sliceActivityId\\\":\\\"e366ab0885a3431ebde72fda275a05ec\\\",\\\"format\\\":\\\"mp4\\\",\\\"activityType\\\":\\\"transcode\\\",\\\"type\\\":\\\"sliceTranscode\\\"}}\",\"priority\":10,\"type\":\"Transcode\",\"userId\":1253406881704637}");
        JobParam jobParam = JobParam.parseParam(params.toJSONString());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.SD);
        Assert.assertTrue(specification.getTranscodeType() == EnumTranscodeType.NORMAL);
    }

    @Test
    public void testSliceAudioTranscode(){
        JSONObject params = JSONObject.parseObject("{\"jobId\":\"219b01b0b9824a779e0b0748f0c1faa4\",\"params\":\"{\\\"arguments\\\":\\\"{\\\\\\\"output_oss_host\\\\\\\":\\\\\\\"oss-cn-shanghai.aliyuncs.com\\\\\\\",\\\\\\\"output_oss_object\\\\\\\":\\\\\\\"lihe/temp/mps/34fe898c-b794-454e-af02-113d36224ab6\\\\\\\",\\\\\\\"output_oss_bucket\\\\\\\":\\\\\\\"mts-sh-out\\\\\\\",\\\\\\\"custom_param\\\\\\\":{\\\\\\\"custom_params\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"audio\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"samplerate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"44100\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"channels\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"bitrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"64\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"codec\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"aac\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"container\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"format\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"mp4\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"transFeatures\\\\\\\\\\\\\\\":{},\\\\\\\\\\\\\\\"outputfile\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"isProcess\\\\\\\\\\\\\\\":false},\\\\\\\\\\\\\\\"transConfig\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"transMode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"onepass\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"video\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"crf\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"27\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"width\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"640\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"bitrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"400\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"codec\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"H.264\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"preset\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"medium\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"pixFmt\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"yuv420p\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"maxrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"900\\\\\\\\\\\\\\\"}}\\\\\\\"},\\\\\\\"parallelEnv\\\\\\\":{\\\\\\\"hasAudio\\\\\\\":true,\\\\\\\"taskType\\\\\\\":\\\\\\\"sliceTranscode\\\\\\\",\\\\\\\"sliceVer\\\\\\\":\\\\\\\"slice_v2_mp4\\\\\\\",\\\\\\\"instanceId\\\\\\\":0,\\\\\\\"sliceCount\\\\\\\":13,\\\\\\\"ossTempPrefix\\\\\\\":\\\\\\\"parallel/output/52a2e1cf9ba04c0191d13cfcf459c3c7\\\\\\\"},\\\\\\\"aliyunUid\\\\\\\":1253406881704637}\\\",\\\"debugInfo\\\":\\\"w*h=230400.0\\\",\\\"expire\\\":86400,\\\"input_param\\\":\\\"{\\\\\\\"param\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"error_code\\\\\\\\\\\\\\\":0}\\\\\\\",\\\\\\\"data\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"type\\\\\\\\\\\\\\\":1,\\\\\\\\\\\\\\\"url\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"http://mts-sh-out.oss-cn-shanghai.aliyuncs.com/lihe/input/yunxi_ls.mp4\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"videoInfo\\\\\\\":{\\\\\\\"fileSize\\\\\\\":619015932,\\\\\\\"duration\\\\\\\":1577,\\\\\\\"height\\\\\\\":1080,\\\\\\\"width\\\\\\\":1920,\\\\\\\"bitRate\\\\\\\":3139.65}}\\\",\\\"mixEncodingSwitch\\\":0,\\\"mixTag\\\":0,\\\"nvencTag\\\":1,\\\"oHeight\\\":0,\\\"oWidth\\\":640,\\\"process_logic\\\":\\\"sliceTranscode\\\",\\\"userData\\\":{\\\"mtsJobId\\\":\\\"219b01b0b9824a779e0b0748f0c1faa4\\\",\\\"requestId\\\":\\\"50E166FC-7855-410B-B8C8-71ED3378D810\\\",\\\"sliceActivityId\\\":\\\"e366ab0885a3431ebde72fda275a05ec\\\",\\\"format\\\":\\\"mp4\\\",\\\"activityType\\\":\\\"transcode\\\",\\\"type\\\":\\\"sliceTranscode\\\"}}\",\"priority\":10,\"type\":\"Transcode\",\"userId\":1253406881704637}");
        JobParam jobParam = JobParam.parseParam(params.toJSONString());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.H_264);
        Assert.assertTrue(specification.getResolution() == EnumResolution.AUDIO);
    }

    @Test
    public void testSliceMergeTranscode(){
        JSONObject params = JSONObject.parseObject("{\"jobId\":\"219b01b0b9824a779e0b0748f0c1faa4\",\"params\":\"{\\\"arguments\\\":\\\"{\\\\\\\"output_oss_host\\\\\\\":\\\\\\\"oss-cn-shanghai.aliyuncs.com\\\\\\\",\\\\\\\"output_oss_object\\\\\\\":\\\\\\\"lihe/temp/mps/34fe898c-b794-454e-af02-113d36224ab6\\\\\\\",\\\\\\\"output_oss_bucket\\\\\\\":\\\\\\\"mts-sh-out\\\\\\\",\\\\\\\"custom_param\\\\\\\":{\\\\\\\"custom_params\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"audio\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"samplerate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"44100\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"channels\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"bitrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"64\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"codec\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"aac\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"container\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"format\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"mp4\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"transFeatures\\\\\\\\\\\\\\\":{},\\\\\\\\\\\\\\\"outputfile\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"isProcess\\\\\\\\\\\\\\\":false},\\\\\\\\\\\\\\\"transConfig\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"transMode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"onepass\\\\\\\\\\\\\\\"},\\\\\\\\\\\\\\\"video\\\\\\\\\\\\\\\":{\\\\\\\\\\\\\\\"crf\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"27\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"width\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"640\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"bitrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"400\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"codec\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"H.264\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"preset\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"medium\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"pixFmt\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"yuv420p\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"maxrate\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"900\\\\\\\\\\\\\\\"}}\\\\\\\"},\\\\\\\"parallelEnv\\\\\\\":{\\\\\\\"hasAudio\\\\\\\":true,\\\\\\\"taskType\\\\\\\":\\\\\\\"sliceTranscode\\\\\\\",\\\\\\\"sliceVer\\\\\\\":\\\\\\\"slice_v2_mp4\\\\\\\",\\\\\\\"instanceId\\\\\\\":-1,\\\\\\\"sliceCount\\\\\\\":13,\\\\\\\"ossTempPrefix\\\\\\\":\\\\\\\"parallel/output/52a2e1cf9ba04c0191d13cfcf459c3c7\\\\\\\"},\\\\\\\"aliyunUid\\\\\\\":1253406881704637}\\\",\\\"debugInfo\\\":\\\"w*h=230400.0\\\",\\\"expire\\\":86400,\\\"input_param\\\":\\\"{\\\\\\\"param\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"error_code\\\\\\\\\\\\\\\":0}\\\\\\\",\\\\\\\"data\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"type\\\\\\\\\\\\\\\":1,\\\\\\\\\\\\\\\"url\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"http://mts-sh-out.oss-cn-shanghai.aliyuncs.com/lihe/input/yunxi_ls.mp4\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"videoInfo\\\\\\\":{\\\\\\\"fileSize\\\\\\\":619015932,\\\\\\\"duration\\\\\\\":1577,\\\\\\\"height\\\\\\\":1080,\\\\\\\"width\\\\\\\":1920,\\\\\\\"bitRate\\\\\\\":3139.65}}\\\",\\\"mixEncodingSwitch\\\":0,\\\"mixTag\\\":0,\\\"nvencTag\\\":1,\\\"oHeight\\\":0,\\\"oWidth\\\":640,\\\"process_logic\\\":\\\"sliceTranscode\\\",\\\"userData\\\":{\\\"mtsJobId\\\":\\\"219b01b0b9824a779e0b0748f0c1faa4\\\",\\\"requestId\\\":\\\"50E166FC-7855-410B-B8C8-71ED3378D810\\\",\\\"sliceActivityId\\\":\\\"e366ab0885a3431ebde72fda275a05ec\\\",\\\"format\\\":\\\"mp4\\\",\\\"activityType\\\":\\\"transcode\\\",\\\"type\\\":\\\"sliceTranscode\\\"}}\",\"priority\":10,\"type\":\"Transcode\",\"userId\":1253406881704637}");
        JobParam jobParam = JobParam.parseParam(params.toJSONString());
        VideoInfo videoInfo = generateVideoInfo();

        TranscodeSpecification specification = TranscodeSpecificationAnalyzer.analysis(jobParam, videoInfo);
        Assert.assertTrue(specification.getTranscodeCodec() == EnumTranscodeCodec.COPY);
    }
}
