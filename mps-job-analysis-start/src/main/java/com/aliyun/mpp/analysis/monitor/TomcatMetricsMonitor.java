package com.aliyun.mpp.analysis.monitor;

import com.aliyun.mpp.analysis.util.log.SLSLogInfo;
import com.aliyun.mpp.analysis.util.log.SLSLogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.util.Set;

/**
 * Created by lihe.lh on 2020/5/26.
 */
@Component
public class TomcatMetricsMonitor {
    @Autowired(required = false)
    private MBeanServer mbeanServer;

    @PostConstruct
    public void init(){
        System.out.println();
    }

    @Scheduled(cron = "0/30 * * * * ?")  //cron接受cron表达式，根据cron表达式确定定时规则
    public void reportTomcatMetrics() {
        try {
            Set<ObjectName> objectNames = mbeanServer.queryNames(new ObjectName("Tomcat:type=ThreadPool,*"), null);
            if (objectNames != null && objectNames.size() == 1) {
                for (ObjectName objectName : objectNames) {
                    int maxThreads = (Integer) mbeanServer.getAttribute(objectName, "maxThreads");
                    int currentThreadCount = (Integer) mbeanServer.getAttribute(objectName, "currentThreadCount");
                    int currentThreadsBusy = (Integer) mbeanServer.getAttribute(objectName, "currentThreadsBusy");
                    long connectionCount = (Long) mbeanServer.getAttribute(objectName, "connectionCount");
                    int maxConnections = (Integer) mbeanServer.getAttribute(objectName, "maxConnections");

                    SLSLogInfo slsLogInfo = new SLSLogInfo();
                    slsLogInfo.setFunction("reportTomcatMetrics");
                    slsLogInfo.getParams().put("maxThreads", maxThreads);
                    slsLogInfo.getParams().put("currentThreadCount", currentThreadCount);
                    slsLogInfo.getParams().put("currentThreadsBusy", currentThreadsBusy);
                    slsLogInfo.getParams().put("connectionCount", connectionCount);
                    slsLogInfo.getParams().put("maxConnections", maxConnections);

                    slsLogInfo.getParams().put("utilization", 100 * currentThreadsBusy / maxThreads);

                    SLSLogUtil.info(slsLogInfo);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
