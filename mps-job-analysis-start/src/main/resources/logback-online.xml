<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- https://github.com/spring-projects/spring-boot/blob/v1.5.9.RELEASE/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml -->
    <!-- https://github.com/spring-projects/spring-boot/blob/v1.5.9.RELEASE/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    <property name="HOME_PATH" value="/home/<USER>"/>
    <property name="APP_NAME" value="job-analysis" />
    <property name="LOG_PATH" value="${HOME_PATH}/mps-job-analysis/logs" />
    <property name="LOG_FILE" value="${LOG_PATH}/${APP_NAME}.log" />
    <property name="ERROR_LOG_FILE" value="${LOG_PATH}/error.log"/>
    <property name="DEPENDENCY_LOG_FILE" value="${LOG_PATH}/dependency-service.log"/>
    <property name="TRACE_LOG_FILE" value="${LOG_PATH}/trace.log"/>
    <property name="RESULT_REPORT_LOG_FILE" value="${LOG_PATH}/result_report.log"/>
    <property name="JOB_RESULT_COLLECT_LOG_FILE" value="${LOG_PATH}/job_result_collect.log"/>

    <property name="LOG_LEVEL" value="INFO"/>


    <appender name="APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <append>true</append>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern><![CDATA[%n
t=%d{yyyy-MM-dd HH:mm:ss.SSSZ}&%.10000m]]></pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_NAME}.%d{yyyy-MM-dd-HH}.%i.log</fileNamePattern>
            <maxHistory>1</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>


    <logger name="com.aliyun.*" additivity="false">
        <level value="info" />
        <appender-ref ref="APPLICATION" />
    </logger>

    <appender name="TRACE-LOG-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${TRACE_LOG_FILE}</file>
        <append>true</append>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern><![CDATA[
				%n%.10000m
            ]]></pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/trace.%d{yyyy-MM-dd-HH}.%i.log
            </fileNamePattern>
            <maxHistory>1</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="JOB-RESULT-REPORT-LOG-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${RESULT_REPORT_LOG_FILE}</file>
        <append>true</append>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern><![CDATA[
				%n%.10000m
            ]]></pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/result_report.%d{yyyy-MM-dd-HH}.%i.log
            </fileNamePattern>
            <maxHistory>1</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>


    <appender name="JOB-RESULT-COLLECT-LOG-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${JOB_RESULT_COLLECT_LOG_FILE}</file>
        <append>true</append>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern><![CDATA[
				%n%.10000m
            ]]></pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/job_result_collect.%d{yyyy-MM-dd-HH}.%i.log
            </fileNamePattern>
            <maxHistory>1</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="async_trace_log" class="ch.qos.logback.classic.AsyncAppender">
        <neverBlock>true</neverBlock>
        <discardingThreshold>1000</discardingThreshold>
        <queueSize>8192</queueSize>
        <appender-ref ref="TRACE-LOG-APPENDER"/>
    </appender>

    <logger name="trace-log" additivity="false">
        <level value="info" />
        <appender-ref ref="async_trace_log" />
    </logger>


    <appender name="async_job_result_collect_log" class="ch.qos.logback.classic.AsyncAppender">
        <neverBlock>true</neverBlock>
        <discardingThreshold>1000</discardingThreshold>
        <queueSize>8192</queueSize>
        <appender-ref ref="JOB-RESULT-COLLECT-LOG-APPENDER"/>
    </appender>

    <logger name="job-result-collect-log" additivity="false">
        <level value="info" />
        <appender-ref ref="async_job_result_collect_log" />
    </logger>



    <appender name="async_job_result_report_log" class="ch.qos.logback.classic.AsyncAppender">
        <neverBlock>true</neverBlock>
        <discardingThreshold>1000</discardingThreshold>
        <queueSize>8192</queueSize>
        <appender-ref ref="JOB-RESULT-REPORT-LOG-APPENDER"/>
    </appender>

    <logger name="job_result_report_log" additivity="false">
        <level value="info" />
        <appender-ref ref="async_job_result_report_log" />
    </logger>



    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <root level="${LOG_LEVEL}">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="APPLICATION" />
    </root>

</configuration>