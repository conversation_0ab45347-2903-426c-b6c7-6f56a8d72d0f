server.port=8080


spring.jmx.enabled=true
server.tomcat.max-threads=400
server.tomcat.min-spare-threads=50
server.tomcat.mbeanregistry.enabled=true

app.region=cn-shanghai
app.deploy.env=online
app.mediaMeta.serverUrl=http://mpp-media-meta:8080
app.media.serviceBucketList=aaa
app.media.defaultDiskTimesForMultiTranscode=10
app.media.maxDiskForMultiTranscode=100000
app.media.minDiskForMultiTranscode=10000
app.engine.serviceUrl=http://11.158.143.164:8080/
app.dmes.serviceUrl=http://dmes:8080/
app.sla.serviceUrl=http://11.158.143.164:8080
app.worker_brain.mpsTranscodeNewUrl=http://11.158.143.164:8080
app.worker_brain.mpsEditingUrl=http://11.158.143.164:8080

#http-pool
http-pool.maxTotal=1000
http-pool.defaultMaxPerRoute=1000
http-pool.connectTimeout=1000
http-pool.connectionRequestTimeout=2000
http-pool.socketTimeout=2000
http-pool.validateAfterInactivity=2000

#log
logging.config=classpath:logback-test.xml

#rds
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.username=mtsvideoapi_test
spring.datasource.password=c8f8vsazsqpxk4wn
spring.datasource.minimum-idle=5
spring.datasource.maximum-pool-size=10
spring.datasource.connection-test-query=SELECT 1
spring.datasource.url=*******************************************************************************************************************************************************