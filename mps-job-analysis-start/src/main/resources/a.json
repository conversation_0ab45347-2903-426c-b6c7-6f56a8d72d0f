{"code": "Success", "message": "Successful.", "data": [{"name": "analysis_null_null_null", "param": {"config": {"jobType": "analysis"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1000}, "speed": 50}]}, {"name": "convertSubtitle_null_null_null", "param": {"config": {"jobType": "convertSubtitle"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 10}, "cost": 100}]}, {"name": "editing_null_null_null", "param": {"config": {"jobType": "editing"}}, "result": [{"parallel": 7, "quotaSet": {"cpu": 8000}, "cost": 300000}]}, {"name": "meta_null_null_null", "param": {"config": {"jobType": "meta"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 500}, "cost": 200}]}, {"name": "package_null_null_null", "param": {"config": {"jobType": "package"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 10}, "cost": 100}]}, {"name": "snapshot_multi", "param": {"config": {"jobType": "snapshot", "snapshotType": "multi"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 2000}, "cost": 60}]}, {"name": "snapshot_single", "param": {"config": {"jobType": "snapshot", "snapshotType": "single"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 500}, "cost": 1}]}, {"name": "transcode_1.0_1080p_x264", "param": {"config": {"jobType": "transcode", "width": 1920, "height": 1080, "videoCodec": "x264", "nhVersion": "1.0"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 13259}, "speed": 1.063}]}, {"name": "transcode_1.0_2k_x264", "param": {"config": {"jobType": "transcode", "width": 2560, "height": 1440, "videoCodec": "x264", "nhVersion": "1.0"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 13873}, "speed": 0.867}]}, {"name": "transcode_1.0_360p_x264", "param": {"config": {"jobType": "transcode", "width": 640, "height": 360, "videoCodec": "x264", "nhVersion": "1.0"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 6862}, "speed": 6}]}, {"name": "transcode_1.0_480p_x264", "param": {"config": {"jobType": "transcode", "width": 848, "height": 480, "videoCodec": "x264", "nhVersion": "1.0"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 8168}, "speed": 4.4}]}, {"name": "transcode_1.0_4k_x264", "param": {"config": {"jobType": "transcode", "width": 3840, "height": 2160, "videoCodec": "x264", "nhVersion": "1.0"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 15192}, "speed": 0.216}]}, {"name": "transcode_1.0_720p_x264", "param": {"config": {"jobType": "transcode", "width": 1280, "height": 720, "videoCodec": "x264", "nhVersion": "1.0"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 9531}, "speed": 2.467}]}, {"name": "transcode_1.5_1080p_x264", "param": {"config": {"jobType": "transcode", "width": 1920, "height": 1080, "videoCodec": "x264", "nhVersion": "1.5"}}, "result": [{"parallel": 7, "quotaSet": {"cpu": 7074}, "speed": 0.713}, {"parallel": 6, "quotaSet": {}, "speed": 0.82}, {"parallel": 1, "quotaSet": {}, "speed": 1.82}]}, {"name": "transcode_1.5_480p_x264", "param": {"config": {"jobType": "transcode", "width": 848, "height": 480, "videoCodec": "x264", "nhVersion": "1.5"}}, "result": [{"parallel": 9, "quotaSet": {"cpu": 5502}, "speed": 4.6}, {"parallel": 8, "quotaSet": {}, "speed": 5.08}, {"parallel": 1, "quotaSet": {}, "speed": 9.5}]}, {"name": "transcode_1.5_720p_x264", "param": {"config": {"jobType": "transcode", "width": 1280, "height": 720, "videoCodec": "x264", "nhVersion": "1.5"}}, "result": [{"parallel": 9, "quotaSet": {"cpu": 5502}, "speed": 1.267}, {"parallel": 8, "quotaSet": {}, "speed": 1.412}, {"parallel": 1, "quotaSet": {}, "speed": 2.88}]}, {"name": "transcode_1.6_1080p_s265", "param": {"config": {"jobType": "transcode", "width": 1920, "height": 1080, "videoCodec": "s265", "nhVersion": "1.6"}}, "result": [{"parallel": 6, "quotaSet": {"cpu": 8253}, "speed": 0.729}, {"parallel": 5, "quotaSet": {}, "speed": 0.865}, {"parallel": 1, "quotaSet": {}, "speed": 1.844}]}, {"name": "transcode_1.6_1080p_x264", "param": {"config": {"jobType": "transcode", "width": 1920, "height": 1080, "videoCodec": "x264", "nhVersion": "1.6"}}, "result": [{"parallel": 9, "quotaSet": {"cpu": 5502}, "speed": 0.66}, {"parallel": 8, "quotaSet": {}, "speed": 0.735}, {"parallel": 1, "quotaSet": {}, "speed": 1.6}]}, {"name": "transcode_1.6_480p_s265", "param": {"config": {"jobType": "transcode", "width": 848, "height": 480, "videoCodec": "s265", "nhVersion": "1.6"}}, "result": [{"parallel": 11, "quotaSet": {"cpu": 4502}, "speed": 2.51}, {"parallel": 10, "quotaSet": {}, "speed": 2.72}, {"parallel": 1, "quotaSet": {}, "speed": 5.145}]}, {"name": "transcode_1.6_480p_x264", "param": {"config": {"jobType": "transcode", "width": 848, "height": 480, "videoCodec": "x264", "nhVersion": "1.6"}}, "result": [{"parallel": 10, "quotaSet": {"cpu": 4952}, "speed": 3.749}, {"parallel": 9, "quotaSet": {}, "speed": 4.08}, {"parallel": 1, "quotaSet": {}, "speed": 6.69}]}, {"name": "transcode_1.6_720p_s265", "param": {"config": {"jobType": "transcode", "width": 1280, "height": 720, "videoCodec": "s265", "nhVersion": "1.6"}}, "result": [{"parallel": 7, "quotaSet": {"cpu": 7074}, "speed": 1.38}, {"parallel": 6, "quotaSet": {}, "speed": 1.6}, {"parallel": 1, "quotaSet": {}, "speed": 2.91}]}, {"name": "transcode_1.6_720p_x264", "param": {"config": {"jobType": "transcode", "width": 1280, "height": 720, "videoCodec": "x264", "nhVersion": "1.6"}}, "result": [{"parallel": 9, "quotaSet": {"cpu": 5502}, "speed": 1.523}, {"parallel": 8, "quotaSet": {}, "speed": 1.68}, {"parallel": 1, "quotaSet": {}, "speed": 3.6}]}, {"name": "transcode_2.0_1080p_x264", "param": {"config": {"jobType": "transcode", "width": 1920, "height": 1080, "videoCodec": "x264", "nhVersion": "2.0"}}, "result": [{"parallel": 4, "quotaSet": {"cpu": 3809, "gpu": 238095}, "speed": 0.17}, {"parallel": 3, "quotaSet": {}, "speed": 0.23}, {"parallel": 1, "quotaSet": {}, "speed": 0.6}]}, {"name": "transcode_2.0_2k_x264", "param": {"config": {"jobType": "transcode", "width": 2560, "height": 1440, "videoCodec": "x264", "nhVersion": "2.0"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 5939, "gpu": 960300}, "speed": 0.188}]}, {"name": "transcode_2.0_360p_x264", "param": {"config": {"jobType": "transcode", "width": 640, "height": 360, "videoCodec": "x264", "nhVersion": "2.0"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 6329, "gpu": 720000}, "speed": 2.651}]}, {"name": "transcode_2.0_480p_x264", "param": {"config": {"jobType": "transcode", "width": 848, "height": 480, "videoCodec": "x264", "nhVersion": "2.0"}}, "result": [{"parallel": 4, "quotaSet": {"cpu": 3809, "gpu": 238095}, "speed": 1.104}, {"parallel": 3, "quotaSet": {}, "speed": 1.449}, {"parallel": 1, "quotaSet": {}, "speed": 2.85}]}, {"name": "transcode_2.0_4k_x264", "param": {"config": {"jobType": "transcode", "width": 3840, "height": 2160, "videoCodec": "x264", "nhVersion": "2.0"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 5518, "gpu": 963300}, "speed": 0.086}]}, {"name": "transcode_2.0_720p_x264", "param": {"config": {"jobType": "transcode", "width": 1280, "height": 720, "videoCodec": "x264", "nhVersion": "2.0"}}, "result": [{"parallel": 4, "quotaSet": {"cpu": 3809, "gpu": 238095}, "speed": 0.39}, {"parallel": 3, "quotaSet": {}, "speed": 0.51}, {"parallel": 1, "quotaSet": {}, "speed": 1.22}]}, {"name": "transcode_null_1080p_aom_av1", "param": {"config": {"jobType": "transcode", "width": 1920, "height": 1080, "videoCodec": "aom_av1"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1600}, "speed": 0.2}]}, {"name": "transcode_null_1080p_h264_nvenc", "param": {"config": {"jobType": "transcode", "width": 1920, "height": 1080, "videoCodec": "h264_nvenc"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1600, "gpu": 100000}, "speed": 0.341}]}, {"name": "transcode_null_1080p_x264", "param": {"config": {"jobType": "transcode", "width": 1920, "height": 1080, "videoCodec": "x264"}}, "result": [{"parallel": 6, "quotaSet": {"cpu": 8253}, "speed": 1.498}, {"parallel": 5, "quotaSet": {}, "speed": 1.77}, {"parallel": 1, "quotaSet": {}, "speed": 4.044}]}, {"name": "transcode_null_1080p_x265", "param": {"config": {"jobType": "transcode", "width": 1920, "height": 1080, "videoCodec": "x265"}}, "result": [{"parallel": 6, "quotaSet": {"cpu": 8253}, "speed": 0.51}, {"parallel": 5, "quotaSet": {}, "speed": 0.6}, {"parallel": 1, "quotaSet": {}, "speed": 1.05}]}, {"name": "transcode_null_2k_aom_av1", "param": {"config": {"jobType": "transcode", "width": 2560, "height": 1440, "videoCodec": "aom_av1"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1800}, "speed": 0.133}]}, {"name": "transcode_null_2k_h264_nvenc", "param": {"config": {"jobType": "transcode", "width": 2560, "height": 1440, "videoCodec": "h264_nvenc"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1600, "gpu": 100000}, "speed": 0.188}]}, {"name": "transcode_null_2k_x264", "param": {"config": {"jobType": "transcode", "width": 2560, "height": 1440, "videoCodec": "x264"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 12672}, "speed": 2.27}]}, {"name": "transcode_null_2k_x265", "param": {"config": {"jobType": "transcode", "width": 2560, "height": 1440, "videoCodec": "x265"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 11688}, "speed": 0.463}]}, {"name": "transcode_null_360p_aom_av1", "param": {"config": {"jobType": "transcode", "width": 640, "height": 360, "videoCodec": "aom_av1"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1000}, "speed": 1.067}]}, {"name": "transcode_null_360p_h264_nvenc", "param": {"config": {"jobType": "transcode", "width": 640, "height": 360, "videoCodec": "h264_nvenc"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1600, "gpu": 100000}, "speed": 2.651}]}, {"name": "transcode_null_360p_x264", "param": {"config": {"jobType": "transcode", "width": 640, "height": 360, "videoCodec": "x264"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 5793}, "speed": 8.11}]}, {"name": "transcode_null_360p_x265", "param": {"config": {"jobType": "transcode", "width": 640, "height": 360, "videoCodec": "x265"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 5176}, "speed": 3.73}]}, {"name": "transcode_null_480p_aom_av1", "param": {"config": {"jobType": "transcode", "width": 848, "height": 480, "videoCodec": "aom_av1"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1200}, "speed": 0.533}]}, {"name": "transcode_null_480p_h264_nvenc", "param": {"config": {"jobType": "transcode", "width": 848, "height": 480, "videoCodec": "h264_nvenc"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1600, "gpu": 100000}, "speed": 1.659}]}, {"name": "transcode_null_480p_x264", "param": {"config": {"jobType": "transcode", "width": 848, "height": 480, "videoCodec": "x264"}}, "result": [{"parallel": 8, "quotaSet": {"cpu": 6190}, "speed": 7.6}, {"parallel": 7, "quotaSet": {}, "speed": 8.59}, {"parallel": 1, "quotaSet": {}, "speed": 19.01}]}, {"name": "transcode_null_480p_x265", "param": {"config": {"jobType": "transcode", "width": 848, "height": 480, "videoCodec": "x265"}}, "result": [{"parallel": 12, "quotaSet": {"cpu": 4126}, "speed": 1.9}, {"parallel": 11, "quotaSet": {}, "speed": 2.039}, {"parallel": 1, "quotaSet": {}, "speed": 3.478}]}, {"name": "transcode_null_4k_aom_av1", "param": {"config": {"jobType": "transcode", "width": 3840, "height": 2160, "videoCodec": "aom_av1"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 2000}, "speed": 0.067}]}, {"name": "transcode_null_4k_h264_nvenc", "param": {"config": {"jobType": "transcode", "width": 3840, "height": 2160, "videoCodec": "h264_nvenc"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1600, "gpu": 100000}, "speed": 0.087}]}, {"name": "transcode_null_4k_x264", "param": {"config": {"jobType": "transcode", "width": 3840, "height": 2160, "videoCodec": "x264"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 13715}, "speed": 0.712}]}, {"name": "transcode_null_4k_x265", "param": {"config": {"jobType": "transcode", "width": 3840, "height": 2160, "videoCodec": "x265"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 12001}, "speed": 0.199}]}, {"name": "transcode_null_720p_aom_av1", "param": {"config": {"jobType": "transcode", "width": 1280, "height": 720, "videoCodec": "aom_av1"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1400}, "speed": 0.267}]}, {"name": "transcode_null_720p_h264_nvenc", "param": {"config": {"jobType": "transcode", "width": 1280, "height": 720, "videoCodec": "h264_nvenc"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1600, "gpu": 100000}, "speed": 0.772}]}, {"name": "transcode_null_720p_x264", "param": {"config": {"jobType": "transcode", "width": 1280, "height": 720, "videoCodec": "x264"}}, "result": [{"parallel": 6, "quotaSet": {"cpu": 8253}, "speed": 3.009}, {"parallel": 5, "quotaSet": {}, "speed": 3.54}, {"parallel": 1, "quotaSet": {}, "speed": 7.62}]}, {"name": "transcode_null_720p_x265", "param": {"config": {"jobType": "transcode", "width": 1280, "height": 720, "videoCodec": "x265"}}, "result": [{"parallel": 9, "quotaSet": {"cpu": 5502}, "speed": 0.765}, {"parallel": 8, "quotaSet": {}, "speed": 0.848}, {"parallel": 1, "quotaSet": {}, "speed": 1.54}]}, {"name": "transcode_null_null_copy", "param": {"config": {"jobType": "transcode", "videoCodec": "copy"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1000}, "speed": 80}]}, {"name": "transcode_null_null_null", "param": {"config": {"jobType": "transcode"}}, "result": [{"parallel": 1, "quotaSet": {"cpu": 1000}, "speed": 30}]}]}