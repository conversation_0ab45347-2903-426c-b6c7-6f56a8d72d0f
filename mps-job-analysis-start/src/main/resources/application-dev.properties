server.port=8090


spring.jmx.enabled=true
server.tomcat.max-threads=400
server.tomcat.min-spare-threads=50
server.tomcat.mbeanregistry.enabled=true

app.region=cn-shanghai
app.deploy.env=dev
app.mediaMeta.serverUrl=http://localhost:8060
app.media.defaultDiskTimesForMultiTranscode=10
app.media.maxDiskForMultiTranscode=100000
app.media.minDiskForMultiTranscode=10000
app.media.serviceBucketList=live-aliyun-record-sh,mts-log-param-cn-shanghai
app.engine.serviceUrl=http://**************:8080/
app.dmes.serviceUrl=http://**************:8080/
app.sla.serviceUrl=http://**************:8080/
app.worker_brain.mpsTranscodeNewUrl=http://**************:8080/
app.worker_brain.mpsEditingUrl=http://**************:8080/

#http-pool
http-pool.maxTotal=2000
http-pool.defaultMaxPerRoute=1000
http-pool.connectTimeout=1000
http-pool.connectionRequestTimeout=8000
http-pool.socketTimeout=8000
http-pool.validateAfterInactivity=2000

#log
logging.config=classpath:logback-test.xml
