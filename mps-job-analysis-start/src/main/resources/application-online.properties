server.port=8080


spring.jmx.enabled=true
server.tomcat.max-threads=400
server.tomcat.min-spare-threads=50
server.tomcat.mbeanregistry.enabled=true

app.region=${APP_REGION}
app.deploy.env=online
app.mediaMeta.serverUrl=http://mpp-media-meta.mps-traffic.svc.cluster.local:8080
app.media.serviceBucketList=${SERVICE_BUCKET_LIST}
app.media.defaultDiskTimesForMultiTranscode=10
app.media.maxDiskForMultiTranscode=100000
app.media.minDiskForMultiTranscode=10000
app.engine.serviceUrl=${ENGINE_SERVICE_URL}
app.sla.serviceUrl=${SLA_SERVICE_URL}
app.dmes.serviceUrl=${DMES_SERVICE_URL}

app.worker_brain.mpsTranscodeNewUrl=${WORKER_BRAIN_MPS_TRANSCODE_NEW_URL}
app.worker_brain.mpsEditingUrl=${WORKER_BRAIN_MPS_EDITING_URL}


#http-pool
http-pool.maxTotal=1000
http-pool.defaultMaxPerRoute=1000
http-pool.connectTimeout=1000
http-pool.connectionRequestTimeout=2000
http-pool.socketTimeout=2000
http-pool.validateAfterInactivity=2000

#log
logging.config=classpath:logback-online.xml

spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.username=mpp_tc
spring.datasource.password=mpp_1201
spring.datasource.minimum-idle=5
spring.datasource.maximum-pool-size=10
spring.datasource.connection-test-query=SELECT 1
spring.datasource.url=jdbc:mysql://${DATA_SOURCE_URL}?useUnicode=true&characterEncoding=utf-8&useSSL=true&serverTimezone=UTC